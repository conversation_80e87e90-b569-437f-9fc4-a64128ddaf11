/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/components/cards.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/* ========================================
   CARD COMPONENT STYLES
   Reusable card styles for the application
   ======================================== */

/* ========================================
   BASE CARD STYLES
   ======================================== */

.card {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* ========================================
   CARD HEADER
   ======================================== */

.card-header {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.card-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0.25rem 0 0 0;
}

/* ========================================
   CARD BODY
   ======================================== */

.card-body {
  padding: 0;
}

.card-body.compact {
  padding: 1rem;
}

.card-body.spacious {
  padding: 2rem;
}

/* ========================================
   CARD FOOTER
   ======================================== */

.card-footer {
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* ========================================
   CARD VARIANTS
   ======================================== */

.card-primary {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.card-success {
  border-color: #10b981;
  background-color: #ecfdf5;
}

.card-warning {
  border-color: #f59e0b;
  background-color: #fffbeb;
}

.card-danger {
  border-color: #dc2626;
  background-color: #fef2f2;
}

.card-info {
  border-color: #06b6d4;
  background-color: #ecfeff;
}

/* ========================================
   CARD SIZES
   ======================================== */

.card-sm {
  padding: 1rem;
}

.card-sm .card-title {
  font-size: 1.125rem;
}

.card-lg {
  padding: 2rem;
}

.card-lg .card-title {
  font-size: 1.5rem;
}

.card-xl {
  padding: 2.5rem;
}

.card-xl .card-title {
  font-size: 1.875rem;
}

/* ========================================
   STATISTICS CARDS
   ======================================== */

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.stat-card .stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-card .stat-label {
  font-size: 0.875rem;
  opacity: 0.9;
  margin-bottom: 0.25rem;
}

.stat-card .stat-change {
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.stat-card .stat-change.positive {
  color: #10b981;
}

.stat-card .stat-change.negative {
  color: #ef4444;
}

/* ========================================
   FEATURE CARDS
   ======================================== */

.feature-card {
  text-align: center;
  padding: 2rem 1.5rem;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.feature-card .feature-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto 1rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.feature-card .feature-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #111827;
}

.feature-card .feature-description {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.5;
}

/* ========================================
   PRODUCT CARDS
   ======================================== */

.product-card {
  position: relative;
  overflow: hidden;
}

.product-card .product-image {
  width: 100%;
  height: 200px;
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

.product-card .product-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #ef4444;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.product-card .product-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.5rem;
}

.product-card .product-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

/* ========================================
   TESTIMONIAL CARDS
   ======================================== */

.testimonial-card {
  background: #f8fafc;
  border: none;
  position: relative;
}

.testimonial-card::before {
  content: '"';
  position: absolute;
  top: -0.5rem;
  left: 1rem;
  font-size: 4rem;
  color: #e5e7eb;
  font-family: serif;
}

.testimonial-card .testimonial-content {
  font-style: italic;
  margin-bottom: 1rem;
  color: #374151;
}

.testimonial-card .testimonial-author {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.testimonial-card .author-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
}

.testimonial-card .author-info .author-name {
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.25rem;
}

.testimonial-card .author-info .author-title {
  font-size: 0.875rem;
  color: #6b7280;
}

/* ========================================
   ADMIN/CLIENT DASHBOARD CARDS
   ======================================== */

/* Override any theme classes for admin/client dashboards */
.admin-page .card,
.client-page .card {
  background-color: #ffffff;
  border-color: #e5e7eb;
  color: #111827;
}

.admin-page .card-title,
.client-page .card-title {
  color: #111827;
}

.admin-page .card-subtitle,
.client-page .card-subtitle {
  color: #6b7280;
}

.admin-page .card-header,
.admin-page .card-footer,
.client-page .card-header,
.client-page .card-footer {
  border-color: #e5e7eb;
}

/* ========================================
   NAVIGATION CARDS (Clients Management Style)
   ======================================== */

/* Base Navigation Card */
.nav-card {
  position: relative;
  border-radius: 0.75rem;
  border: 2px solid;
  transition: all 0.2s ease;
  cursor: pointer;
  overflow: hidden;
  min-height: 140px;
  display: flex;
  flex-direction: column;
}

/* Navigation Card States */
.nav-card.active {
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.nav-card.disabled {
  border-color: #e5e7eb;
  cursor: not-allowed;
  opacity: 0.6;
  background-color: #f9fafb;
}

.nav-card.inactive {
  border-color: #e5e7eb;
  background-color: #ffffff;
}

.nav-card.inactive:hover {
  border-color: #d1d5db;
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px) scale(1.02);
  background-color: #f9fafb;
}

/* Navigation Card Color Variants - Active State */
.nav-card.active.clients {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #3b82f6;
}

.nav-card.active.projects {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: #10b981;
}

.nav-card.active.invoices {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: #f59e0b;
}

.nav-card.active.payments {
  background: linear-gradient(135deg, #e9d5ff 0%, #ddd6fe 100%);
  border-color: #8b5cf6;
}

.nav-card.active.categories {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #3b82f6;
}

.nav-card.active.services {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: #10b981;
}

.nav-card.active.options {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: #f59e0b;
}

.nav-card.active.features {
  background: linear-gradient(135deg, #e9d5ff 0%, #ddd6fe 100%);
  border-color: #8b5cf6;
}

/* Navigation Card Content */
.nav-card-content {
  position: relative;
  padding: 0.75rem;
  border-radius: 0.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Navigation Card Header */
.nav-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.25rem;
}

.nav-card-header-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Navigation Card Icon */
.nav-card-icon {
  padding: 0.5rem;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.nav-card.active .nav-card-icon.clients {
  background-color: #dbeafe;
  color: #2563eb;
}

.nav-card.active .nav-card-icon.projects {
  background-color: #d1fae5;
  color: #059669;
}

.nav-card.active .nav-card-icon.invoices {
  background-color: #fef3c7;
  color: #d97706;
}

.nav-card.active .nav-card-icon.payments {
  background-color: #e9d5ff;
  color: #7c3aed;
}

.nav-card.active .nav-card-icon.categories {
  background-color: #dbeafe;
  color: #2563eb;
}

.nav-card.active .nav-card-icon.services {
  background-color: #d1fae5;
  color: #059669;
}

.nav-card.active .nav-card-icon.options {
  background-color: #fef3c7;
  color: #d97706;
}

.nav-card.active .nav-card-icon.features {
  background-color: #e9d5ff;
  color: #7c3aed;
}

.nav-card.disabled .nav-card-icon {
  background-color: #f3f4f6;
  color: #9ca3af;
}

.nav-card.inactive .nav-card-icon {
  background-color: #f9fafb;
  color: #6b7280;
}

.nav-card.inactive:hover .nav-card-icon {
  background-color: #f3f4f6;
}

/* Navigation Card Counter */
.nav-card-counter {
  font-size: 1.125rem;
  font-weight: 700;
  transition: color 0.3s ease;
}

.nav-card.active .nav-card-counter {
  color: #111827;
}

.nav-card.disabled .nav-card-counter {
  color: #9ca3af;
}

.nav-card.inactive .nav-card-counter {
  color: #374151;
}

.nav-card.inactive:hover .nav-card-counter {
  color: #111827;
}

/* Navigation Card Title */
.nav-card-title {
  font-size: 1.125rem;
  font-weight: 700;
  transition: color 0.3s ease;
}

.nav-card.active .nav-card-title {
  color: #111827;
}

.nav-card.disabled .nav-card-title {
  color: #9ca3af;
}

.nav-card.inactive .nav-card-title {
  color: #374151;
}

.nav-card.inactive:hover .nav-card-title {
  color: #111827;
}

/* Navigation Card Description */
.nav-card-description {
  margin-bottom: 0.25rem;
}

.nav-card-description p {
  font-size: 0.75rem;
  line-height: 1.5;
  transition: color 0.3s ease;
}

.nav-card.active .nav-card-description p {
  color: #4b5563;
}

.nav-card.disabled .nav-card-description p {
  color: #9ca3af;
}

.nav-card.inactive .nav-card-description p {
  color: #6b7280;
}

.nav-card.inactive:hover .nav-card-description p {
  color: #4b5563;
}

/* Navigation Card Bottom Section */
.nav-card-bottom {
  margin-top: auto;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 0;
}

/* Navigation Card Selected Name */
.nav-card-selected-name {
  flex: 1;
  min-width: 0;
}

.nav-card-selected-name-container {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  max-width: 100%;
}

.nav-card.active .nav-card-selected-name-container.clients {
  background-color: rgba(219, 234, 254, 0.8);
  color: #1e3a8a;
}

.nav-card.active .nav-card-selected-name-container.projects {
  background-color: rgba(209, 250, 229, 0.8);
  color: #064e3b;
}

.nav-card.active .nav-card-selected-name-container.invoices {
  background-color: rgba(254, 243, 199, 0.8);
  color: #92400e;
}

.nav-card.active .nav-card-selected-name-container.payments {
  background-color: rgba(233, 213, 255, 0.8);
  color: #581c87;
}

.nav-card.inactive .nav-card-selected-name-container.clients {
  background-color: rgba(219, 234, 254, 0.8);
  color: #1e40af;
}

.nav-card.inactive .nav-card-selected-name-container.projects {
  background-color: rgba(209, 250, 229, 0.8);
  color: #047857;
}

.nav-card.inactive .nav-card-selected-name-container.invoices {
  background-color: rgba(254, 243, 199, 0.8);
  color: #b45309;
}

.nav-card.inactive .nav-card-selected-name-container.payments {
  background-color: rgba(233, 213, 255, 0.8);
  color: #6b21a8;
}

.nav-card.active .nav-card-selected-name-container.categories {
  background-color: rgba(219, 234, 254, 0.8);
  color: #1e3a8a;
}

.nav-card.active .nav-card-selected-name-container.services {
  background-color: rgba(209, 250, 229, 0.8);
  color: #064e3b;
}

.nav-card.active .nav-card-selected-name-container.options {
  background-color: rgba(254, 243, 199, 0.8);
  color: #92400e;
}

.nav-card.active .nav-card-selected-name-container.features {
  background-color: rgba(233, 213, 255, 0.8);
  color: #581c87;
}

.nav-card.inactive .nav-card-selected-name-container.categories {
  background-color: rgba(219, 234, 254, 0.8);
  color: #1e40af;
}

.nav-card.inactive .nav-card-selected-name-container.services {
  background-color: rgba(209, 250, 229, 0.8);
  color: #047857;
}

.nav-card.inactive .nav-card-selected-name-container.options {
  background-color: rgba(254, 243, 199, 0.8);
  color: #b45309;
}

.nav-card.inactive .nav-card-selected-name-container.features {
  background-color: rgba(233, 213, 255, 0.8);
  color: #6b21a8;
}

.nav-card-selected-name-content {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  min-width: 0;
}

.nav-card-selected-name-dot {
  width: 0.375rem;
  height: 0.375rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.nav-card-selected-name-dot.clients {
  background-color: #2563eb;
}

.nav-card-selected-name-dot.projects {
  background-color: #059669;
}

.nav-card-selected-name-dot.invoices {
  background-color: #d97706;
}

.nav-card-selected-name-dot.payments {
  background-color: #7c3aed;
}

.nav-card-selected-name-dot.categories {
  background-color: #2563eb;
}

.nav-card-selected-name-dot.services {
  background-color: #059669;
}

.nav-card-selected-name-dot.options {
  background-color: #d97706;
}

.nav-card-selected-name-dot.features {
  background-color: #7c3aed;
}

.nav-card-selected-name-text {
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

/* Navigation Card Row Counter (for client cards only) */
.nav-card-row-counter {
  text-align: right;
  flex-shrink: 0;
}

.nav-card-row-counter-text {
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
}

.nav-card.active .nav-card-row-counter-text.clients {
  color: #2563eb;
}

.nav-card.active .nav-card-row-counter-text.categories {
  color: #2563eb;
}

.nav-card.disabled .nav-card-row-counter-text {
  color: #9ca3af;
}

.nav-card.inactive .nav-card-row-counter-text {
  color: #6b7280;
}

/* Navigation Card Triangle Arrow */
.nav-card-arrow {
  position: absolute;
  top: 50%;
  right: -20px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 20px solid transparent;
  border-bottom: 20px solid transparent;
  border-left: 20px solid;
}

.nav-card.active .nav-card-arrow.clients {
  border-left-color: #dbeafe;
}

.nav-card.active .nav-card-arrow.projects {
  border-left-color: #d1fae5;
}

.nav-card.active .nav-card-arrow.invoices {
  border-left-color: #fef3c7;
}

.nav-card.active .nav-card-arrow.payments {
  border-left-color: #e9d5ff;
}

.nav-card.active .nav-card-arrow.categories {
  border-left-color: #dbeafe;
}

.nav-card.active .nav-card-arrow.services {
  border-left-color: #d1fae5;
}

.nav-card.active .nav-card-arrow.options {
  border-left-color: #fef3c7;
}

.nav-card.active .nav-card-arrow.features {
  border-left-color: #e9d5ff;
}

.nav-card.inactive .nav-card-arrow {
  border-left-color: #ffffff;
}

.nav-card-arrow-shadow {
  filter: drop-shadow(1px 0 0 #d1d5db);
}

/* ========================================
   RESPONSIVE CARDS
   ======================================== */

@media (max-width: 768px) {
  .card {
    padding: 1rem;
  }
  
  .card-lg {
    padding: 1.5rem;
  }
  
  .card-xl {
    padding: 2rem;
  }
  
  .feature-card {
    padding: 1.5rem 1rem;
  }
  
  .feature-card .feature-icon {
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
  }
  
  .stat-card .stat-value {
    font-size: 1.5rem;
  }
  
  /* Navigation Cards Responsive */
  .nav-card {
    min-height: 120px;
  }
  
  .nav-card-content {
    padding: 0.5rem;
  }
  
  .nav-card-header-left {
    gap: 0.5rem;
  }
  
  .nav-card-icon {
    padding: 0.375rem;
  }
  
  .nav-card-counter {
    font-size: 1rem;
  }
  
  .nav-card-title {
    font-size: 1rem;
  }
  
  .nav-card-selected-name-container {
    padding: 0.375rem 0.5rem;
  }
  
  .nav-card-selected-name-content {
    gap: 0.25rem;
  }
  
  .nav-card-arrow {
    right: -15px;
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
    border-left: 15px solid;
  }
}

