/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/projects/route";
exports.ids = ["app/api/admin/projects/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fprojects%2Froute&page=%2Fapi%2Fadmin%2Fprojects%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fprojects%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fprojects%2Froute&page=%2Fapi%2Fadmin%2Fprojects%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fprojects%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _Volumes_Files_Technoloway_New_Website_Technoloway_src_app_api_admin_projects_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/admin/projects/route.ts */ \"(rsc)/./src/app/api/admin/projects/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/projects/route\",\n        pathname: \"/api/admin/projects\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/projects/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/app/api/admin/projects/route.ts\",\n    nextConfigOutput,\n    userland: _Volumes_Files_Technoloway_New_Website_Technoloway_src_app_api_admin_projects_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/admin/projects/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fprojects%2Froute&page=%2Fapi%2Fadmin%2Fprojects%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fprojects%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/projects/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/admin/projects/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _config_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/prisma */ \"(rsc)/./src/config/prisma.ts\");\n/* harmony import */ var _services_api_api_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/api/api-utils */ \"(rsc)/./src/services/api/api-utils.ts\");\n/* harmony import */ var _lib_utils_validations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils/validations */ \"(rsc)/./src/lib/utils/validations.ts\");\n\n\n\n// GET /api/admin/projects - List all projects with pagination and search\nconst GET = (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_1__.withErrorHandler)(async (request)=>{\n    await (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_1__.requireAdmin)(request);\n    const { page, limit, search, sortBy, sortOrder, filter } = (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_1__.getQueryParams)(request);\n    const { skip, take } = (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_1__.getPaginationParams)(page, limit);\n    // Build where clause\n    const where = {};\n    // Add search functionality\n    if (search && search.trim()) {\n        const searchTerm = search.trim();\n        where.OR = [\n            {\n                name: {\n                    contains: searchTerm,\n                    mode: 'insensitive'\n                }\n            },\n            {\n                description: {\n                    contains: searchTerm,\n                    mode: 'insensitive'\n                }\n            },\n            {\n                projgoals: {\n                    contains: searchTerm,\n                    mode: 'insensitive'\n                }\n            },\n            {\n                tags: {\n                    contains: searchTerm,\n                    mode: 'insensitive'\n                }\n            },\n            {\n                clients: {\n                    companyname: {\n                        contains: searchTerm,\n                        mode: 'insensitive'\n                    }\n                }\n            },\n            {\n                clients: {\n                    contactname: {\n                        contains: searchTerm,\n                        mode: 'insensitive'\n                    }\n                }\n            }\n        ];\n    }\n    // Add filters\n    if (filter) {\n        try {\n            const filters = JSON.parse(filter);\n            if (filters.status) where.status = filters.status;\n            if (filters.isfeatured !== undefined) where.isfeatured = filters.isfeatured === 'true';\n            if (filters.ispublic !== undefined) where.ispublic = filters.ispublic === 'true';\n            if (filters.clientid) {\n                const cid = typeof filters.clientid === 'string' ? Number(filters.clientid) : filters.clientid;\n                if (!isNaN(cid)) where.clientid = cid;\n            }\n            if (filters.orderid) {\n                const oid = typeof filters.orderid === 'string' ? Number(filters.orderid) : filters.orderid;\n                if (!isNaN(oid)) where.orderid = oid;\n            }\n        } catch (e) {\n        // Invalid filter JSON, ignore\n        }\n    }\n    // Build orderBy clause with field mapping\n    let orderBy;\n    // Map frontend sort fields to database fields\n    const sortFieldMap = {\n        'client': {\n            clients: {\n                companyname: sortOrder || 'asc'\n            }\n        },\n        'clients.companyname': {\n            clients: {\n                companyname: sortOrder || 'asc'\n            }\n        },\n        'budget': {\n            estimatecost: sortOrder || 'asc'\n        },\n        'timeline': {\n            projstartdate: sortOrder || 'asc'\n        },\n        'manager': {\n            projmanager: sortOrder || 'asc'\n        },\n        'flags': {\n            status: sortOrder || 'asc'\n        } // Map flags to status field\n    };\n    if (sortBy && sortFieldMap[sortBy]) {\n        orderBy = sortFieldMap[sortBy];\n    } else if (sortBy) {\n        orderBy = {\n            [sortBy]: sortOrder || 'asc'\n        };\n    } else {\n        orderBy = {\n            createdat: 'desc'\n        };\n    }\n    const [projects, total] = await Promise.all([\n        _config_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.projects.findMany({\n            where,\n            skip,\n            take,\n            orderBy,\n            include: {\n                clients: {\n                    select: {\n                        id: true,\n                        companyname: true,\n                        contactname: true,\n                        contactemail: true\n                    }\n                },\n                orders: {\n                    select: {\n                        id: true,\n                        ordertitle: true,\n                        status: true,\n                        ordertotalamount: true\n                    }\n                },\n                teammembers: {\n                    select: {\n                        id: true,\n                        name: true\n                    }\n                },\n                _count: {\n                    select: {\n                        tasks: true,\n                        projectdocuments: true,\n                        messages: true\n                    }\n                }\n            }\n        }),\n        _config_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.projects.count({\n            where\n        })\n    ]);\n    return (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_1__.paginatedResponse)(projects, page, limit, total);\n});\n// POST /api/admin/projects - Create a new project\nconst POST = (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_1__.withErrorHandler)(async (request)=>{\n    await (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_1__.requireAdmin)(request);\n    const validate = (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_1__.validateRequest)(_lib_utils_validations__WEBPACK_IMPORTED_MODULE_2__.schemas.project.create);\n    const data = await validate(request);\n    // Validate order exists (only if orderid is provided and not empty)\n    let orderIdNum = undefined;\n    if (data.orderid && data.orderid.trim() !== '' && !isNaN(Number(data.orderid))) {\n        orderIdNum = Number(data.orderid);\n        const order = await _config_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.orders.findUnique({\n            where: {\n                id: orderIdNum\n            }\n        });\n        if (!order) {\n            throw new Error('Order not found');\n        }\n    } else {\n        // If no orderid provided, find the first available order or create a default one\n        const firstOrder = await _config_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.orders.findFirst({\n            orderBy: {\n                id: 'asc'\n            }\n        });\n        if (firstOrder) {\n            orderIdNum = Number(firstOrder.id);\n        } else {\n            // Create a default order if none exists\n            const defaultOrder = await _config_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.orders.create({\n                data: {\n                    ordertitle: 'Default Order',\n                    clientid: clientIdNum || 1,\n                    orderdesc: 'Default order for projects',\n                    status: 'PENDING'\n                }\n            });\n            orderIdNum = Number(defaultOrder.id);\n        }\n    }\n    // Validate client exists if provided\n    let clientIdNum = undefined;\n    if (data.clientid && !isNaN(Number(data.clientid))) {\n        clientIdNum = Number(data.clientid);\n        const client = await _config_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.clients.findUnique({\n            where: {\n                id: clientIdNum\n            }\n        });\n        if (!client) {\n            throw new Error('Client not found');\n        }\n    }\n    // Prepare project data\n    const { projmanager, clientid, orderid, ...restData } = data;\n    const projectData = {\n        ...restData\n    };\n    if (orderIdNum !== undefined) projectData.orderid = orderIdNum;\n    if (clientIdNum !== undefined) projectData.clientid = clientIdNum;\n    // projmanager is omitted because the schema expects it to be undefined\n    const project = await _config_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.projects.create({\n        data: projectData,\n        include: {\n            clients: {\n                select: {\n                    id: true,\n                    companyname: true,\n                    contactname: true,\n                    contactemail: true\n                }\n            },\n            orders: {\n                select: {\n                    id: true,\n                    ordertitle: true,\n                    status: true,\n                    ordertotalamount: true\n                }\n            },\n            teammembers: {\n                select: {\n                    id: true,\n                    name: true\n                }\n            },\n            _count: {\n                select: {\n                    tasks: true,\n                    projectdocuments: true,\n                    messages: true\n                }\n            }\n        }\n    });\n    return (0,_services_api_api_utils__WEBPACK_IMPORTED_MODULE_1__.successResponse)(project, 'Project created successfully', 201);\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/projects/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/prisma.ts":
/*!******************************!*\
  !*** ./src/config/prisma.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ prisma),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n// Re-export for convenience\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29uZmlnL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZDO0FBRTdDLE1BQU1DLGtCQUFrQkM7QUFJakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUU7QUFFbEUsSUFBSUksSUFBcUMsRUFBRUgsZ0JBQWdCRSxNQUFNLEdBQUdBO0FBRXBFLDRCQUE0QjtBQUNMIiwic291cmNlcyI6WyIvVm9sdW1lcy9GaWxlcy9UZWNobm9sb3dheS1OZXctV2Vic2l0ZS9UZWNobm9sb3dheS9zcmMvY29uZmlnL3ByaXNtYS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG5cbi8vIFJlLWV4cG9ydCBmb3IgY29udmVuaWVuY2VcbmV4cG9ydCB7IHByaXNtYSBhcyBkYiB9XG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiLCJkYiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/config/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/validations.ts":
/*!**************************************!*\
  !*** ./src/lib/utils/validations.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAboutPageSchema: () => (/* binding */ createAboutPageSchema),\n/* harmony export */   createAboutPageSectionSchema: () => (/* binding */ createAboutPageSectionSchema),\n/* harmony export */   createBlogPostSchema: () => (/* binding */ createBlogPostSchema),\n/* harmony export */   createCategorySchema: () => (/* binding */ createCategorySchema),\n/* harmony export */   createClientSchema: () => (/* binding */ createClientSchema),\n/* harmony export */   createContactFormSchema: () => (/* binding */ createContactFormSchema),\n/* harmony export */   createContractSchema: () => (/* binding */ createContractSchema),\n/* harmony export */   createHeroSectionSchema: () => (/* binding */ createHeroSectionSchema),\n/* harmony export */   createHeroSlideSchema: () => (/* binding */ createHeroSlideSchema),\n/* harmony export */   createInvoiceSchema: () => (/* binding */ createInvoiceSchema),\n/* harmony export */   createJobApplicationSchema: () => (/* binding */ createJobApplicationSchema),\n/* harmony export */   createJobListingSchema: () => (/* binding */ createJobListingSchema),\n/* harmony export */   createLegalPageSchema: () => (/* binding */ createLegalPageSchema),\n/* harmony export */   createLegalPageSectionSchema: () => (/* binding */ createLegalPageSectionSchema),\n/* harmony export */   createOrderSchema: () => (/* binding */ createOrderSchema),\n/* harmony export */   createProjectSchema: () => (/* binding */ createProjectSchema),\n/* harmony export */   createServiceOptionFeatureSchema: () => (/* binding */ createServiceOptionFeatureSchema),\n/* harmony export */   createServiceOptionSchema: () => (/* binding */ createServiceOptionSchema),\n/* harmony export */   createServiceSchema: () => (/* binding */ createServiceSchema),\n/* harmony export */   createSiteSettingSchema: () => (/* binding */ createSiteSettingSchema),\n/* harmony export */   createTeamMemberSchema: () => (/* binding */ createTeamMemberSchema),\n/* harmony export */   createTechnologySchema: () => (/* binding */ createTechnologySchema),\n/* harmony export */   createTestimonialSchema: () => (/* binding */ createTestimonialSchema),\n/* harmony export */   createUserSchema: () => (/* binding */ createUserSchema),\n/* harmony export */   schemas: () => (/* binding */ schemas),\n/* harmony export */   updateAboutPageSchema: () => (/* binding */ updateAboutPageSchema),\n/* harmony export */   updateAboutPageSectionSchema: () => (/* binding */ updateAboutPageSectionSchema),\n/* harmony export */   updateBlogPostSchema: () => (/* binding */ updateBlogPostSchema),\n/* harmony export */   updateCategorySchema: () => (/* binding */ updateCategorySchema),\n/* harmony export */   updateClientSchema: () => (/* binding */ updateClientSchema),\n/* harmony export */   updateContactFormSchema: () => (/* binding */ updateContactFormSchema),\n/* harmony export */   updateContractSchema: () => (/* binding */ updateContractSchema),\n/* harmony export */   updateHeroSectionSchema: () => (/* binding */ updateHeroSectionSchema),\n/* harmony export */   updateHeroSlideSchema: () => (/* binding */ updateHeroSlideSchema),\n/* harmony export */   updateInvoiceSchema: () => (/* binding */ updateInvoiceSchema),\n/* harmony export */   updateJobApplicationSchema: () => (/* binding */ updateJobApplicationSchema),\n/* harmony export */   updateJobListingSchema: () => (/* binding */ updateJobListingSchema),\n/* harmony export */   updateLegalPageSchema: () => (/* binding */ updateLegalPageSchema),\n/* harmony export */   updateLegalPageSectionSchema: () => (/* binding */ updateLegalPageSectionSchema),\n/* harmony export */   updateOrderSchema: () => (/* binding */ updateOrderSchema),\n/* harmony export */   updateProjectSchema: () => (/* binding */ updateProjectSchema),\n/* harmony export */   updateServiceOptionFeatureSchema: () => (/* binding */ updateServiceOptionFeatureSchema),\n/* harmony export */   updateServiceOptionSchema: () => (/* binding */ updateServiceOptionSchema),\n/* harmony export */   updateServiceSchema: () => (/* binding */ updateServiceSchema),\n/* harmony export */   updateSiteSettingSchema: () => (/* binding */ updateSiteSettingSchema),\n/* harmony export */   updateTeamMemberSchema: () => (/* binding */ updateTeamMemberSchema),\n/* harmony export */   updateTechnologySchema: () => (/* binding */ updateTechnologySchema),\n/* harmony export */   updateTestimonialSchema: () => (/* binding */ updateTestimonialSchema),\n/* harmony export */   updateUserSchema: () => (/* binding */ updateUserSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v4/classic/schemas.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v4/classic/coerce.js\");\n\n// User schemas\nconst createUserSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.string().email(),\n    firstname: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    lastname: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    imageurl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    role: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'ADMIN',\n        'USER',\n        'CLIENT'\n    ]).default('USER'),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(6, 'Password must be at least 6 characters').optional(),\n    isactive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    linkedclientid: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).transform((val)=>val ? Number(val) : undefined).optional()\n});\nconst updateUserSchema = createUserSchema.partial();\n// Service schemas\nconst createServiceSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    categoryId: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).transform((val)=>String(val)),\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(255),\n    slug: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(255).optional(),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    excerpt: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(500).optional(),\n    iconClass: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(100).optional(),\n    logoUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().url().optional(),\n    price: zod__WEBPACK_IMPORTED_MODULE_1__.number().positive(),\n    discountRate: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().min(0).max(100).optional(),\n    totalDiscount: zod__WEBPACK_IMPORTED_MODULE_1__.number().optional(),\n    manager: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(50).optional(),\n    duration: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(50).optional(),\n    complexity: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'Simple',\n        'Medium',\n        'Complex'\n    ]).optional(),\n    features: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    displayOrder: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().default(0)\n});\nconst updateServiceSchema = createServiceSchema.partial();\n// Service Option schemas\nconst createServiceOptionSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    serviceId: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).transform((val)=>String(val)),\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(50),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    price: zod__WEBPACK_IMPORTED_MODULE_1__.number().positive().optional(),\n    discountRate: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().min(0).max(100).optional(),\n    totalDiscount: zod__WEBPACK_IMPORTED_MODULE_1__.number().optional(),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true)\n});\nconst updateServiceOptionSchema = createServiceOptionSchema.partial();\n// Service Option Feature schemas\nconst createServiceOptionFeatureSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    optionId: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).transform((val)=>String(val)),\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(50),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    cost: zod__WEBPACK_IMPORTED_MODULE_1__.number().optional(),\n    discountRate: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).max(100).transform((val)=>Math.round(val)).optional(),\n    totalDiscount: zod__WEBPACK_IMPORTED_MODULE_1__.number().optional(),\n    isIncluded: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true)\n});\nconst updateServiceOptionFeatureSchema = createServiceOptionFeatureSchema.partial();\n// Project schemas\nconst createProjectSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    orderid: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().transform((val)=>{\n        if (!val || val.trim() === '') return undefined;\n        return val;\n    }),\n    clientid: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Client is required\"),\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    projgoals: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().transform((val)=>{\n        if (!val || val.trim() === '') return undefined;\n        return val;\n    }),\n    projmanager: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().transform((val)=>{\n        if (!val || val.trim() === '') return undefined;\n        return val;\n    }),\n    projstartdate: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.date()\n    ]).optional().transform((val)=>{\n        if (!val || val === '') return undefined;\n        return typeof val === 'string' ? new Date(val) : val;\n    }),\n    projcompletiondate: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.date()\n    ]).optional().transform((val)=>{\n        if (!val || val === '') return undefined;\n        return typeof val === 'string' ? new Date(val) : val;\n    }),\n    estimatecost: zod__WEBPACK_IMPORTED_MODULE_1__.number().positive().optional(),\n    estimatetime: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().transform((val)=>{\n        if (!val || val.trim() === '') return undefined;\n        return val;\n    }),\n    estimateeffort: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().transform((val)=>{\n        if (!val || val.trim() === '') return undefined;\n        return val;\n    }),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'PLANNING',\n        'IN_PROGRESS',\n        'COMPLETED',\n        'ON_HOLD',\n        'CANCELLED'\n    ]).default('PLANNING'),\n    isfeatured: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.boolean(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.string()\n    ]).default(false).transform((val)=>{\n        if (typeof val === 'string') return val === 'true';\n        return val;\n    }),\n    ispublic: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.boolean(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.string()\n    ]).default(false).transform((val)=>{\n        if (typeof val === 'string') return val === 'true';\n        return val;\n    }),\n    displayorder: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().default(0),\n    imageurl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().transform((val)=>{\n        if (!val || val.trim() === '') return undefined;\n        return val;\n    }),\n    projecturl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().transform((val)=>{\n        if (!val || val.trim() === '') return undefined;\n        return val;\n    }),\n    githuburl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().transform((val)=>{\n        if (!val || val.trim() === '') return undefined;\n        return val;\n    }),\n    tags: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().transform((val)=>{\n        if (!val || val.trim() === '') return undefined;\n        return val;\n    })\n});\nconst updateProjectSchema = createProjectSchema.partial().extend({\n    clientid: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Client is required\") // Keep clientid required for updates\n});\n// Client schemas\nconst createClientSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    userId: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    companyName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(200),\n    contactName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(100),\n    contactPosition: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(100).optional(),\n    contactEmail: zod__WEBPACK_IMPORTED_MODULE_0__.string().email().max(100),\n    contactPhone: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(20).optional(),\n    contactFax: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(20).optional(),\n    website: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(100).optional(),\n    companyWebsite: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(100).optional(),\n    address: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(200).optional(),\n    city: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(100).optional(),\n    state: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(50).optional(),\n    zipCode: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(20).optional(),\n    country: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(100).optional(),\n    logoUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(500).optional(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.boolean(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.string()\n    ]).default(true).transform((val)=>{\n        if (typeof val === 'string') return val === 'true';\n        return val;\n    })\n});\nconst updateClientSchema = createClientSchema.partial();\n// Blog post schemas\nconst createBlogPostSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(255),\n    slug: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(255),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    excerpt: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    featuredImageUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    authorId: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    isPublished: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.boolean(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.string()\n    ]).default(false).transform((val)=>{\n        if (typeof val === 'string') return val === 'true';\n        return val;\n    }),\n    publishedAt: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.date()\n    ]).optional().nullable().transform((val)=>{\n        if (!val || val === '') return undefined;\n        return typeof val === 'string' ? new Date(val) : val;\n    }),\n    categories: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    tags: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined)\n});\nconst updateBlogPostSchema = createBlogPostSchema.partial();\n// Team member schemas\nconst createTeamMemberSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    position: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    birthDate: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.date()\n    ]).optional().transform((val)=>{\n        if (!val || val === '') return undefined;\n        return typeof val === 'string' ? new Date(val) : val;\n    }),\n    gender: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    maritalStatus: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    socialSecurityNo: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    hireDate: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.date()\n    ]).optional().transform((val)=>{\n        if (!val || val === '') return undefined;\n        return typeof val === 'string' ? new Date(val) : val;\n    }),\n    address: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    city: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    state: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    zipCode: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    country: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    phone: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    salary: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).optional().nullable().transform((val)=>{\n        if (!val || val === '' || val === null) return undefined;\n        return typeof val === 'string' ? parseFloat(val) : val;\n    }),\n    payrollMethod: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    resumeUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    notes: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    bio: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    photoUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.string().email().optional().nullable().transform((val)=>val || undefined),\n    linkedinUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    twitterUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    githubUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    displayOrder: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).default(0).transform((val)=>{\n        return typeof val === 'string' ? parseInt(val) || 0 : val;\n    }),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.boolean(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.string()\n    ]).default(true).transform((val)=>{\n        if (typeof val === 'string') return val === 'true';\n        return val;\n    })\n});\nconst updateTeamMemberSchema = createTeamMemberSchema.partial();\n// Technology schemas\nconst createTechnologySchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    iconUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    displayOrder: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).default(0).transform((val)=>{\n        return typeof val === 'string' ? parseInt(val) || 0 : val;\n    }),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.boolean(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.string()\n    ]).default(true).transform((val)=>{\n        if (typeof val === 'string') return val === 'true';\n        return val;\n    })\n});\nconst updateTechnologySchema = createTechnologySchema.partial();\n// Testimonial schemas\nconst createTestimonialSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    clientName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(100),\n    clientTitle: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(100),\n    clientCompany: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).max(100),\n    clientPhotoUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    rating: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).default(5).transform((val)=>{\n        return typeof val === 'string' ? parseInt(val) || 5 : val;\n    }),\n    isFeatured: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.boolean(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.string()\n    ]).default(false).transform((val)=>{\n        if (typeof val === 'string') return val === 'true';\n        return val;\n    }),\n    displayOrder: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).default(0).transform((val)=>{\n        return typeof val === 'string' ? parseInt(val) || 0 : val;\n    })\n});\nconst updateTestimonialSchema = createTestimonialSchema.partial();\n// Contact form schemas\nconst createContactFormSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.string().email(),\n    phone: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    subject: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    message: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    isread: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(false),\n    readat: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.date()\n    ]).optional().transform((val)=>{\n        if (!val || val === '') return undefined;\n        return typeof val === 'string' ? new Date(val) : val;\n    }),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'New',\n        'In Progress',\n        'Resolved',\n        'Closed'\n    ]).default('New')\n});\nconst updateContactFormSchema = createContactFormSchema.partial();\n// Category schemas\nconst createCategorySchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    parentId: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    displayOrder: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().default(0)\n});\nconst updateCategorySchema = createCategorySchema.partial();\n// Order schemas\nconst createOrderSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    clientId: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    orderNumber: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    totalAmount: zod__WEBPACK_IMPORTED_MODULE_1__.number().positive(),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'PENDING',\n        'CONFIRMED',\n        'IN_PROGRESS',\n        'COMPLETED',\n        'CANCELLED'\n    ]).default('PENDING'),\n    orderDate: zod__WEBPACK_IMPORTED_MODULE_0__.date().default(()=>new Date())\n});\nconst updateOrderSchema = createOrderSchema.partial();\n// Invoice schemas\nconst createInvoiceSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    clientid: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().positive(),\n    projectid: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().positive().optional(),\n    orderid: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().positive(),\n    contid: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().positive(),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')).transform((val)=>val === '' ? undefined : val),\n    subtotal: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).optional(),\n    taxrate: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).default(0),\n    taxamount: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).default(0),\n    totalamount: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'DRAFT',\n        'SENT',\n        'PAID',\n        'OVERDUE',\n        'CANCELLED',\n        'Pending',\n        'Sent',\n        'Paid',\n        'Overdue',\n        'Cancelled'\n    ]).default('DRAFT'),\n    duedate: zod__WEBPACK_IMPORTED_MODULE_1__.date(),\n    paidat: zod__WEBPACK_IMPORTED_MODULE_1__.date().optional()\n});\nconst updateInvoiceSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    clientid: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().positive().optional(),\n    projectid: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().positive().optional(),\n    orderid: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().positive().optional(),\n    contid: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().positive().optional(),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__[\"null\"](),\n        zod__WEBPACK_IMPORTED_MODULE_0__.literal('')\n    ]).transform((val)=>val === '' || val === null ? undefined : val).optional(),\n    subtotal: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).optional(),\n    taxrate: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).optional(),\n    taxamount: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).optional(),\n    totalamount: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).optional(),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'DRAFT',\n        'SENT',\n        'PAID',\n        'OVERDUE',\n        'CANCELLED',\n        'Pending',\n        'Sent',\n        'Paid',\n        'Overdue',\n        'Cancelled'\n    ]).optional(),\n    duedate: zod__WEBPACK_IMPORTED_MODULE_1__.date().optional(),\n    paidat: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__[\"null\"](),\n        zod__WEBPACK_IMPORTED_MODULE_0__.literal('')\n    ]).transform((val)=>val === '' || val === null ? undefined : val ? new Date(val) : undefined).optional()\n});\n// Job listing schemas\nconst createJobListingSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    requirements: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    location: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    employmentType: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    salaryMin: zod__WEBPACK_IMPORTED_MODULE_1__.number().positive().optional(),\n    salaryMax: zod__WEBPACK_IMPORTED_MODULE_1__.number().positive().optional(),\n    salaryCurrency: zod__WEBPACK_IMPORTED_MODULE_0__.string().default('USD'),\n    isRemote: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(false),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    expiresAt: zod__WEBPACK_IMPORTED_MODULE_0__.date().optional()\n});\nconst updateJobListingSchema = createJobListingSchema.partial();\n// Job Application schemas\nconst createJobApplicationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    jobListingId: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    applicantName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    applicantEmail: zod__WEBPACK_IMPORTED_MODULE_0__.string().email(),\n    applicantPhone: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    resumeUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().url().optional(),\n    coverLetter: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'PENDING',\n        'REVIEWED',\n        'INTERVIEW',\n        'HIRED',\n        'REJECTED'\n    ]).default('PENDING'),\n    notes: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional()\n});\nconst updateJobApplicationSchema = createJobApplicationSchema.partial();\n// Contract schemas\nconst createContractSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    clientId: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    projectId: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    orderId: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    contractNumber: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    value: zod__WEBPACK_IMPORTED_MODULE_1__.number().positive(),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'DRAFT',\n        'PENDING',\n        'SIGNED',\n        'ACTIVE',\n        'COMPLETED',\n        'TERMINATED'\n    ]).default('DRAFT'),\n    startDate: zod__WEBPACK_IMPORTED_MODULE_1__.date(),\n    endDate: zod__WEBPACK_IMPORTED_MODULE_1__.date(),\n    signedAt: zod__WEBPACK_IMPORTED_MODULE_1__.date().optional(),\n    expiresAt: zod__WEBPACK_IMPORTED_MODULE_1__.date().optional(),\n    terms: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional()\n});\nconst updateContractSchema = createContractSchema.partial();\n// Additional schemas for missing models\n// Hero Section schemas\nconst createHeroSectionSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    metaDescription: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    metaKeywords: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    pageName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).default('Home'),\n    mainTitle: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    mainSubtitle: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    mainDescription: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    primaryButtonText: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    primaryButtonUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    secondaryButtonText: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    secondaryButtonUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    enableSlideshow: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    slideshowSpeed: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().min(1000).max(10000).default(5000),\n    autoplay: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    showDots: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    showArrows: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    enableFloatingElements: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    floatingElementsConfig: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    modifiedBy: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional()\n});\nconst updateHeroSectionSchema = createHeroSectionSchema.partial();\n// Hero Slide schemas\nconst createHeroSlideSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    heroSectionId: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    mediaType: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'image',\n        'video'\n    ]).default('image'),\n    imageUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    videoUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    mediaAlt: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    videoAutoplay: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    videoMuted: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    videoLoop: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    videoControls: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(false),\n    buttonText: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    buttonUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    displayOrder: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().default(0),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true),\n    animationType: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'fade',\n        'slide',\n        'zoom'\n    ]).default('fade'),\n    duration: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().min(1000).max(10000).default(5000)\n});\nconst updateHeroSlideSchema = createHeroSlideSchema.partial();\n// About Page schemas\nconst createAboutPageSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true)\n});\nconst updateAboutPageSchema = createAboutPageSchema.partial();\n// About Page Section schemas\nconst createAboutPageSectionSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    aboutPageId: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    imageUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().url().optional(),\n    displayOrder: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().default(0),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true)\n});\nconst updateAboutPageSectionSchema = createAboutPageSectionSchema.partial();\n// Legal Page schemas\nconst createLegalPageSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    slug: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1).optional(),\n    metaDescription: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || undefined),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.boolean(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.string()\n    ]).default(true).transform((val)=>{\n        if (typeof val === 'string') return val === 'true';\n        return val;\n    }),\n    displayOrder: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.number()\n    ]).default(0).transform((val)=>{\n        return typeof val === 'string' ? parseInt(val) || 0 : val;\n    }),\n    modifiedBy: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().nullable().transform((val)=>val || 'admin'),\n    lastModified: zod__WEBPACK_IMPORTED_MODULE_0__.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.date()\n    ]).optional().transform((val)=>{\n        if (!val || val === '') return new Date();\n        return typeof val === 'string' ? new Date(val) : val;\n    })\n});\nconst updateLegalPageSchema = createLegalPageSchema.partial();\n// Legal Page Section schemas\nconst createLegalPageSectionSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    legalPageId: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    displayOrder: zod__WEBPACK_IMPORTED_MODULE_1__.number().int().default(0)\n});\nconst updateLegalPageSectionSchema = createLegalPageSectionSchema.partial();\n// Site Setting schemas\nconst createSiteSettingSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    key: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    value: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    icon: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    category: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    isactive: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true)\n});\nconst updateSiteSettingSchema = createSiteSettingSchema.partial();\n// Export all schemas as a single object for easier imports\nconst schemas = {\n    user: {\n        create: createUserSchema,\n        update: updateUserSchema\n    },\n    service: {\n        create: createServiceSchema,\n        update: updateServiceSchema\n    },\n    project: {\n        create: createProjectSchema,\n        update: updateProjectSchema\n    },\n    client: {\n        create: createClientSchema,\n        update: updateClientSchema\n    },\n    blog: {\n        create: createBlogPostSchema,\n        update: updateBlogPostSchema\n    },\n    teamMember: {\n        create: createTeamMemberSchema,\n        update: updateTeamMemberSchema\n    },\n    technology: {\n        create: createTechnologySchema,\n        update: updateTechnologySchema\n    },\n    testimonial: {\n        create: createTestimonialSchema,\n        update: updateTestimonialSchema\n    },\n    contactForm: {\n        create: createContactFormSchema,\n        update: updateContactFormSchema\n    },\n    category: {\n        create: createCategorySchema,\n        update: updateCategorySchema\n    },\n    order: {\n        create: createOrderSchema,\n        update: updateOrderSchema\n    },\n    invoice: {\n        create: createInvoiceSchema,\n        update: updateInvoiceSchema\n    },\n    jobListing: {\n        create: createJobListingSchema,\n        update: updateJobListingSchema\n    },\n    jobApplication: {\n        create: createJobApplicationSchema,\n        update: updateJobApplicationSchema\n    },\n    contract: {\n        create: createContractSchema,\n        update: updateContractSchema\n    },\n    heroSection: {\n        create: createHeroSectionSchema,\n        update: updateHeroSectionSchema\n    },\n    heroSlide: {\n        create: createHeroSlideSchema,\n        update: updateHeroSlideSchema\n    },\n    aboutPage: {\n        create: createAboutPageSchema,\n        update: updateAboutPageSchema\n    },\n    aboutPageSection: {\n        create: createAboutPageSectionSchema,\n        update: updateAboutPageSectionSchema\n    },\n    legalPage: {\n        create: createLegalPageSchema,\n        update: updateLegalPageSchema\n    },\n    legalPageSection: {\n        create: createLegalPageSectionSchema,\n        update: updateLegalPageSectionSchema\n    },\n    siteSetting: {\n        create: createSiteSettingSchema,\n        update: updateSiteSettingSchema\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/validations.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/api/api-utils.ts":
/*!***************************************!*\
  !*** ./src/services/api/api-utils.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiError: () => (/* binding */ ApiError),\n/* harmony export */   buildSearchQuery: () => (/* binding */ buildSearchQuery),\n/* harmony export */   buildSortQuery: () => (/* binding */ buildSortQuery),\n/* harmony export */   errorResponse: () => (/* binding */ errorResponse),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getPaginationParams: () => (/* binding */ getPaginationParams),\n/* harmony export */   getQueryParams: () => (/* binding */ getQueryParams),\n/* harmony export */   paginatedResponse: () => (/* binding */ paginatedResponse),\n/* harmony export */   requireAdmin: () => (/* binding */ requireAdmin),\n/* harmony export */   requireAuth: () => (/* binding */ requireAuth),\n/* harmony export */   successResponse: () => (/* binding */ successResponse),\n/* harmony export */   validateMethod: () => (/* binding */ validateMethod),\n/* harmony export */   validateRequest: () => (/* binding */ validateRequest),\n/* harmony export */   withErrorHandler: () => (/* binding */ withErrorHandler)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v4/classic/errors.js\");\n\n\n\n// Error handling\nclass ApiError extends Error {\n    constructor(message, statusCode = 500, code){\n        super(message), this.message = message, this.statusCode = statusCode, this.code = code;\n        this.name = 'ApiError';\n    }\n}\n// Success response helper\nfunction successResponse(data, message, statusCode = 200) {\n    // Serialize BigInt values in the data\n    const serializedData = serializeBigInt(data);\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n        success: true,\n        data: serializedData,\n        message\n    }, {\n        status: statusCode\n    });\n}\n// Error response helper\nfunction errorResponse(error, statusCode = 500, code) {\n    const message = error instanceof Error ? error.message : error;\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n        success: false,\n        error: message,\n        code\n    }, {\n        status: statusCode\n    });\n}\n// Paginated response helper\n// Helper function to serialize BigInt and Decimal values\nfunction serializeBigInt(obj) {\n    if (obj === null || obj === undefined) return obj;\n    if (typeof obj === 'bigint') {\n        return obj.toString();\n    }\n    // Handle Date objects\n    if (obj instanceof Date) {\n        return obj.toISOString();\n    }\n    // Handle Prisma Decimal types\n    if (obj && typeof obj === 'object' && obj.constructor && obj.constructor.name === 'Decimal') {\n        return obj.toString();\n    }\n    if (Array.isArray(obj)) {\n        return obj.map(serializeBigInt);\n    }\n    if (typeof obj === 'object') {\n        const serialized = {};\n        for (const [key, value] of Object.entries(obj)){\n            serialized[key] = serializeBigInt(value);\n        }\n        return serialized;\n    }\n    return obj;\n}\nfunction paginatedResponse(data, page, limit, total, message) {\n    const totalPages = Math.ceil(total / limit);\n    // Serialize BigInt values in the data\n    const serializedData = serializeBigInt(data);\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n        success: true,\n        data: serializedData,\n        message,\n        pagination: {\n            page,\n            limit,\n            total,\n            totalPages\n        }\n    });\n}\n// Validation middleware\nfunction validateRequest(schema) {\n    return async (request)=>{\n        try {\n            const body = await request.json();\n            return schema.parse(body);\n        } catch (error) {\n            if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.ZodError) {\n                const errorMessages = error.issues.map((err)=>`${err.path.join('.')}: ${err.message}`).join(', ');\n                throw new ApiError(`Validation error: ${errorMessages}`, 400, 'VALIDATION_ERROR');\n            }\n            throw new ApiError('Invalid request body', 400, 'INVALID_BODY');\n        }\n    };\n}\n// Query parameter helpers\nfunction getQueryParams(request) {\n    const { searchParams } = new URL(request.url);\n    return {\n        page: parseInt(searchParams.get('page') || '1'),\n        limit: Math.min(parseInt(searchParams.get('limit') || '10'), 100),\n        search: searchParams.get('search') || undefined,\n        sortBy: searchParams.get('sortBy') || undefined,\n        sortOrder: searchParams.get('sortOrder') || 'desc',\n        filter: searchParams.get('filter') || undefined,\n        categoryId: searchParams.get('categoryId') || undefined,\n        serviceId: searchParams.get('serviceId') || undefined,\n        optionId: searchParams.get('optionId') || undefined\n    };\n}\n// Pagination helpers\nfunction getPaginationParams(page, limit) {\n    const skip = (page - 1) * limit;\n    return {\n        skip,\n        take: limit\n    };\n}\n// Error handler wrapper for API routes\nfunction withErrorHandler(handler) {\n    return async (request, context)=>{\n        try {\n            return await handler(request, context);\n        } catch (error) {\n            // Log errors only in development mode\n            if (true) {\n                console.error('API Error:', error);\n            }\n            if (error instanceof ApiError) {\n                return errorResponse(error.message, error.statusCode, error.code);\n            }\n            if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.ZodError) {\n                const errorMessages = error.issues.map((err)=>`${err.path.join('.')}: ${err.message}`).join(', ');\n                return errorResponse(`Validation error: ${errorMessages}`, 400, 'VALIDATION_ERROR');\n            }\n            if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_0__.Prisma.PrismaClientKnownRequestError) {\n                switch(error.code){\n                    case 'P2002':\n                        return errorResponse('A record with this data already exists', 409, 'DUPLICATE_RECORD');\n                    case 'P2025':\n                        return errorResponse('Record not found', 404, 'NOT_FOUND');\n                    case 'P2003':\n                        return errorResponse('Foreign key constraint failed', 400, 'FOREIGN_KEY_ERROR');\n                    default:\n                        return errorResponse('Database error occurred', 500, 'DATABASE_ERROR');\n                }\n            }\n            return errorResponse('Internal server error', 500, 'INTERNAL_ERROR');\n        }\n    };\n}\n// Method validation helper\nfunction validateMethod(request, allowedMethods) {\n    if (!allowedMethods.includes(request.method)) {\n        throw new ApiError(`Method ${request.method} not allowed`, 405, 'METHOD_NOT_ALLOWED');\n    }\n}\n// Authentication helper using NextAuth\nasync function requireAuth(request) {\n    const { getServerSession } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(\"vendor-chunks/next-auth\"), __webpack_require__.e(\"vendor-chunks/@babel\"), __webpack_require__.e(\"vendor-chunks/jose\"), __webpack_require__.e(\"vendor-chunks/openid-client\"), __webpack_require__.e(\"vendor-chunks/oauth\"), __webpack_require__.e(\"vendor-chunks/@panva\"), __webpack_require__.e(\"vendor-chunks/yallist\"), __webpack_require__.e(\"vendor-chunks/preact-render-to-string\"), __webpack_require__.e(\"vendor-chunks/preact\"), __webpack_require__.e(\"vendor-chunks/oidc-token-hash\"), __webpack_require__.e(\"vendor-chunks/object-hash\"), __webpack_require__.e(\"vendor-chunks/lru-cache\"), __webpack_require__.e(\"vendor-chunks/cookie\")]).then(__webpack_require__.bind(__webpack_require__, /*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\"));\n    const { authOptions } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next-auth\"), __webpack_require__.e(\"vendor-chunks/bcryptjs\"), __webpack_require__.e(\"_rsc_src_services_auth_auth-config_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/services/auth/auth-config */ \"(rsc)/./src/services/auth/auth-config.ts\"));\n    const session = await getServerSession(authOptions);\n    if (!session || !session.user) {\n        throw new ApiError('Authentication required', 401, 'UNAUTHORIZED');\n    }\n    return {\n        id: session.user.id,\n        email: session.user.email,\n        role: session.user.role,\n        name: session.user.name\n    };\n}\n// Admin authorization helper\nasync function requireAdmin(request) {\n    const user = await requireAuth(request);\n    if (user.role !== 'ADMIN') {\n        throw new ApiError('Admin access required', 403, 'FORBIDDEN');\n    }\n    return user;\n}\n// Slug generation helper\nfunction generateSlug(text) {\n    return text.toLowerCase().replace(/[^\\w\\s-]/g, '') // Remove special characters\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\n    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen\n    .trim();\n}\n// Note: File upload helpers moved to @/services/file-upload/file-upload\n// Search helpers\nfunction buildSearchQuery(searchTerm, fields) {\n    if (!searchTerm) return {};\n    // For case-insensitive search, we'll use the original search term\n    // Prisma will handle case sensitivity based on the database collation\n    return {\n        OR: fields.map((field)=>({\n                [field]: {\n                    contains: searchTerm,\n                    mode: 'insensitive' // This works with PostgreSQL and MySQL, for SQLite it's ignored but still works\n                }\n            }))\n    };\n}\n// Sort helpers\nfunction buildSortQuery(sortBy, sortOrder = 'desc') {\n    if (!sortBy) return {\n        createdat: sortOrder\n    };\n    return {\n        [sortBy]: sortOrder\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/api/api-utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fprojects%2Froute&page=%2Fapi%2Fadmin%2Fprojects%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fprojects%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();