/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/components/modals.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/* ========================================
   REUSABLE MODAL COMPONENT STYLES
   Organized by component sections for better maintainability
   
   NOTE: This file uses Tailwind CSS @apply directives and vendor-specific
   properties like text-fill-color. Linter warnings for these are expected
   and acceptable in a Tailwind CSS project.
   ======================================== */

/* ========================================
   BACKDROP STYLES
   ======================================== */

/* Mobile sidebar overlay/backdrop */
.sidebar-overlay {
  /* stylelint-disable-next-line at-rule-no-unknown */
  position: fixed;
  inset: 0px;
  z-index: 40;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
  --tw-bg-opacity: 0.75;
}

.sidebar-overlay.lg-hidden {
  /* stylelint-disable-next-line at-rule-no-unknown */
}

@media (min-width: 1024px) {
  .sidebar-overlay.lg-hidden {
    display: none;
  }
}

/* General overlay backdrop for modals */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 40;
}

.modal-overlay.dark {
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
  --tw-bg-opacity: 0.5;
}

.modal-overlay.light {
  background-color: transparent;
  --tw-bg-opacity: 0.5;
}

/* Backdrop blur effects */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.backdrop-blur-md {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.backdrop-blur-lg {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

/* ========================================
   CONTAINER STYLES
   ======================================== */

/* Modal container base */
.modal-container {
  position: fixed;
  display: flex;
  cursor: move;
  flex-direction: column;
  overflow: hidden;
  border-radius: 0.75rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 0px;
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.modal-container.sample-style {
  overflow: hidden;
  border-radius: 0.75rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 0px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  isolation: isolate;
  contain: layout style paint;
  resize: none;
  transition: box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  animation: modalAppear 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes modalAppear {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-container.draggable {
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.modal-container.no-backdrop {
  /* No backdrop styling - modal is draggable without overlay */
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
}

/* Modal container with motion/framer-motion support */
.modal-container-motion {
  overflow: hidden;
  border-radius: 0.75rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  background-color: transparent;
  padding: 0px;
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.modal-container-motion.dragging {
  cursor: grabbing;
}

.modal-container-motion.default {
  cursor: default;
}

/* Modal container positioning */
.modal-positioned {
  position: fixed;
}

.modal-positioned.dragging {
  cursor: grabbing;
}

.modal-positioned.default {
  cursor: default;
}

/* Modal z-index utilities - removed to prevent overrides */

/* Draggable modal utility class */
.draggable-modal {
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Drag handle utility class */
.drag-handle {
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Modal dynamic styles (from modal-system.tsx) */
.modal-container-dynamic {
  position: fixed;
}

.modal-container-dragging {
  cursor: grabbing;
}

.modal-container-default {
  cursor: default;
}

/* Modal component specific containers */
.modal-payment-container {
  /* Dynamic width and height will be set via inline styles */
  position: relative;
}

.modal-project-container {
  /* Dynamic width and height will be set via inline styles */
  position: relative;
}

.modal-blog-container {
  /* Dynamic top and left will be set via inline styles */
}

/* ========================================
   HEADER STYLES
   ======================================== */

/* Modal header base styles */
.modal-header {
  background: linear-gradient(to right, #3b82f6, #2563eb);
  border-bottom: 2px solid #1d4ed8;
  padding: 16px 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: -8px;
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  opacity: 1;
  background-color: transparent;
}

/* Modal header with motion support and icon colors - matching sample modal */
.modal-header-motion {
  background: linear-gradient(to right, #3b82f6, #2563eb);
  border-bottom: 2px solid #1d4ed8;
  padding: 16px 24px;
  height: 80px;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: -8px;
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  opacity: 1;
  background-color: transparent;
}

.modal-header-motion.blue-gradient {
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}

.modal-header-motion.green-gradient {
  --tw-border-opacity: 1;
  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #16a34a var(--tw-gradient-to-position);
}

.modal-header-motion.purple-gradient {
  --tw-border-opacity: 1;
  border-color: rgb(126 34 206 / var(--tw-border-opacity, 1));
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}

.modal-header-motion.orange-gradient {
  --tw-border-opacity: 1;
  border-color: rgb(194 65 12 / var(--tw-border-opacity, 1));
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);
}

.modal-header-motion.red-gradient {
  --tw-border-opacity: 1;
  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #dc2626 var(--tw-gradient-to-position);
}

/* Modal header content and icon styles */
.modal-header-content {
  flex: 1;
}

.modal-header-icon {
  padding: 0px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header-icon svg {
  width: 24px;
  height: 24px;
  color: white;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.modal-header-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin: 0;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.modal-header-subtitle {
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: rgba(255, 255, 255, 0.9);
}

.modal-header-close {
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.modal-header-close svg {
  width: 20px;
  height: 20px;
  color: white;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.modal-header-motion-content {
  display: flex;
  align-items: center;
}

.modal-header-motion-content > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.modal-header-motion-icon {
  padding: 0px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.modal-header-motion-icon svg {
  width: 24px;
  height: 24px;
  stroke: white;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
}

.modal-header-motion-text {
  flex: 1;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.modal-header-motion-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin: 0;
  line-height: 1.2;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.modal-header-motion-subtitle {
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.3;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: rgba(255, 255, 255, 0.9);
}

.modal-header-motion-close {
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header-motion-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.modal-header-motion-close svg {
  width: 20px;
  height: 20px;
  stroke: white;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
}

/* ========================================
   BODY STYLES
   ======================================== */

/* Modal content base */
.modal-content {
  flex: 1 1 0%;
  cursor: default;
}

/* Custom scrollbar styling for modal content */
.modal-content::-webkit-scrollbar {
  width: 8px;
}

.modal-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: #94a3b8;
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Firefox scrollbar styling */
.modal-content {
  scrollbar-width: thin;
  scrollbar-color: #94a3b8 #f1f5f9;
}

.modal-content-motion {
  flex: 1 1 0%;
  cursor: default;
  overflow-y: auto;
}

/* Modal form styles */
.modal-form > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.modal-form {
  padding: 1rem;
}

.modal-form.compact > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.modal-form.compact {
  padding: 0.75rem;
}

/* Modal form sections */
.modal-form-section > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.modal-form-section.compact > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.modal-form-section-motion {
  background-color: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.modal-form-section-motion.compact {
  padding: 0.75rem;
}

.modal-form-section-motion.default {
  padding: 1rem;
}

/* Removed - icons are now directly inside titles */

.modal-form-section-motion-header.compact {
  margin-bottom: 0.5rem;
}

.modal-form-section-motion-header.default {
  margin-bottom: 0.75rem;
}

.modal-form-section-motion-icon {
  display: flex;
  height: 1.25rem;
  width: 1.25rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
}

.modal-form-section-motion-icon.blue {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.modal-form-section-motion-icon.green {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.modal-form-section-motion-icon.purple {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}

.modal-form-section-motion-icon.orange {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}

.modal-form-section-motion-icon.red {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.modal-form-section-motion-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Modal form fields */
.modal-form-field {
  /* Container for form field */
  display: flex;
  flex-direction: column;
}

.modal-form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.modal-form-grid.no-bottom-space {
  margin-bottom: 0;
}

.modal-form-grid.no-bottom-space > div:last-child {
  margin-bottom: 0;
}

.modal-form-grid.compact {
  gap: 0.75rem;
}

.modal-form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 2px;
}

.modal-required {
  color: #dc2626;
  font-weight: 600;
}

.modal-form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
}

.modal-form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-form-input-error {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.modal-form-input-error:focus {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.modal-form-input.with-icon {
  padding-left: 2.5rem;
}

.modal-form-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
}

.modal-form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
  resize: vertical;
}

.modal-form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-form-textarea.modal-form-input-error {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.modal-form-select.modal-form-input-error {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.modal-form-checkbox.modal-form-input-error {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.modal-form-checkbox {
  margin-top: 0.25rem;
  height: 1rem;
  width: 1rem;
  border-radius: 0.25rem;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.modal-form-checkbox:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.modal-form-error {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.modal-form-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  height: 1.25rem;
  width: 1.25rem;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

/* Modal buttons */
.modal-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-weight: 500;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.modal-button:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-offset-width: 2px;
}
.modal-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.modal-button.primary {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.modal-button.primary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.modal-button.primary:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.modal-button.secondary {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.modal-button.secondary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.modal-button.secondary:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(107 114 128 / var(--tw-ring-opacity, 1));
}

.modal-button.outline {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.modal-button.outline:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.modal-button.outline:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.modal-button.danger {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.modal-button.danger:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.modal-button.danger:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}

.modal-button.success {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.modal-button.success:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}

.modal-button.success:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.modal-button.warning {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.modal-button.warning:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(161 98 7 / var(--tw-bg-opacity, 1));
}

.modal-button.warning:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(234 179 8 / var(--tw-ring-opacity, 1));
}

.modal-button.sm {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.modal-button.md {
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.modal-button.lg {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
}

.modal-button-loading-spinner {
  margin-right: 0.5rem;
  height: 1rem;
  width: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.modal-button-loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 9999px;
  border-bottom-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

/* Sample modal button styles - using buttons.css classes instead */

/* Modal cards & sections */
.modal-card {
  border-radius: 0.5rem;
  border-width: 1px;
  padding: 1rem;
}

.modal-card.blue-gradient {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);
}

.modal-card.gray {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.modal-card.white {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  background-color: transparent;
}

.modal-card-header {
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-card-title {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.modal-card-amount {
  text-align: right;
}

.modal-card-amount-value {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.modal-card-amount-label {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.modal-card-content > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.modal-card-content {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.modal-card-row {
  display: flex;
  justify-content: space-between;
}

.modal-card-row-label {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.modal-card-row-value {
  font-weight: 500;
}

.modal-card-row.discount {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.modal-card-row.fee {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

/* Modal dropdown styles */
.modal-dropdown {
  position: relative;
}

.modal-dropdown-button {
  width: 100%;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  background-color: transparent;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: left;
}

.modal-dropdown-button:focus {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.modal-dropdown-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-dropdown-selection {
  display: flex;
  align-items: center;
}

.modal-dropdown-icon {
  margin-right: 0.75rem;
  height: 1.25rem;
  width: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.modal-dropdown-text {
  /* Container for dropdown text */
  display: flex;
  flex-direction: column;
}

.modal-dropdown-title {
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.modal-dropdown-description {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.modal-dropdown-arrow {
  height: 1.25rem;
  width: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.modal-dropdown-arrow.open {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.modal-dropdown-menu {
  position: absolute;
  margin-top: 0.25rem;
  max-height: 15rem;
  width: 100%;
  overflow: auto;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  background-color: transparent;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.modal-dropdown-item {
  width: 100%;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: left;
}

.modal-dropdown-item:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.modal-dropdown-item:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.modal-dropdown-item.selected {
  border-left-width: 4px;
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.modal-dropdown-item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-dropdown-item-icon {
  margin-right: 0.75rem;
  height: 1.25rem;
  width: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.modal-dropdown-item-text {
  /* Container for item text */
  display: flex;
  flex-direction: column;
}

.modal-dropdown-item-title {
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.modal-dropdown-item-description {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.modal-dropdown-item-fee {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

/* Modal status messages */
.modal-message {
  border-radius: 0.5rem;
  border-width: 1px;
  padding: 0.75rem;
}

.modal-message.success {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.modal-message.error {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.modal-message.warning {
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}

.modal-message.info {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.modal-message-content {
  display: flex;
  align-items: center;
}

.modal-message-icon {
  margin-right: 0.5rem;
  height: 1.25rem;
  width: 1.25rem;
}

.modal-message-icon.success {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.modal-message-icon.error {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.modal-message-icon.warning {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}

.modal-message-icon.info {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.modal-message-text {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
}

.modal-message-text.success {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}

.modal-message-text.error {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.modal-message-text.warning {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}

.modal-message-text.info {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

/* Modal checkbox sections */
.modal-checkbox-section > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.modal-checkbox-item {
  display: flex;
  align-items: flex-start;
}

.modal-checkbox-label {
  margin-left: 0.75rem;
}

.modal-checkbox-title {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.modal-checkbox-description {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.modal-checkbox-input {
  margin-top: 0.5rem;
}

/* Modal promo code section */
.modal-promo-input {
  width: 100%;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-transform: uppercase;
}
.modal-promo-input:focus {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.modal-promo-success {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.modal-promo-success-icon {
  height: 1.25rem;
  width: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.modal-promo-message {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

/* Modal security & payment sections */
.modal-security-section {
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
  padding: 1rem;
}

.modal-security-header {
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
}

.modal-security-icon {
  margin-right: 0.5rem;
  height: 1.25rem;
  width: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.modal-security-title {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.modal-security-status {
  margin-bottom: 0.75rem;
  border-radius: 0.5rem;
  border-width: 1px;
  padding: 0.75rem;
}

.modal-security-status.creating {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.modal-security-status.ready {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.modal-security-status-content {
  display: flex;
  align-items: center;
}

.modal-security-status-spinner {
  margin-right: 0.5rem;
  height: 1rem;
  width: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.modal-security-status-spinner {
  animation: spin 1s linear infinite;
  border-radius: 9999px;
  border-bottom-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}

.modal-security-status-icon {
  margin-right: 0.5rem;
  height: 1rem;
  width: 1rem;
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.modal-security-status-text {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.modal-security-status-text.creating {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.modal-security-status-text.ready {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.modal-security-card {
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  background-color: transparent;
  padding: 0.75rem;
}

.modal-security-footer {
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.modal-security-footer-icon {
  margin-right: 0.25rem;
  height: 0.75rem;
  width: 0.75rem;
}

/* Modal resize handle - invisible border handles like sample modal */
.modal-resize-handle {
  position: absolute;
  background: transparent;
}

/* Border resize handles - 4px borders like sample modal */
.modal-resize-handle-top {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  cursor: n-resize;
}

.modal-resize-handle-right {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 4px;
  cursor: e-resize;
}

.modal-resize-handle-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  cursor: s-resize;
}

.modal-resize-handle-left {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 4px;
  cursor: w-resize;
}

/* Note: All resize handle variants removed - using border handles only like sample modal */

/* Ensure no corner resize handles appear - only border handles */
.modal-container {
  resize: none;
  overflow: hidden;
}

.modal-container * {
  resize: none;
}

/* Prevent any browser default resize handles */
.modal-container,
.modal-container *,
.modal-container::before,
.modal-container::after {
  resize: none;
  overflow: hidden;
}


/* Modal content specific styles */
.modal-project-content {
  height: calc(100% - 80px);
}

.modal-blog-content {
  cursor: default;
}

.modal-blog-form-content {
  /* Dynamic max-height and min-height will be set via inline styles */
  position: relative;
}

.modal-blog-view-mode-text {
  background-color: transparent;
}

/* ========================================
   MODAL FORM SECTIONS
   ======================================== */

.modal-form-section {
  margin-bottom: 1rem;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 0.75rem;
}

.modal-form-section.sample-style {
  background-color: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  margin-bottom: 0;
}

.modal-form-section-header.sample-style {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.modal-form-section-title.sample-style {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.modal-form-section-icon.sample-style {
  width: 16px;
  height: 16px;
  color: #3b82f6;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.modal-form-section-icon.sample-style.green {
  color: #10b981;
}

.modal-form-section-icon.sample-style.orange {
  color: #f59e0b;
}

.modal-form-section-header {
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
}

.modal-form-section-header > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.modal-form-section-icon {
  height: 1rem;
  width: 1rem;
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.modal-form-section-title {
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.modal-form-section-content > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.modal-form-section-content.sample-style > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.modal-form-grid.sample-style {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0px;
}

.modal-form-input.sample-style {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
}

.modal-form-input.sample-style:focus {
  border-color: #3b82f6;
  outline: none;
}

.modal-form-select.sample-style {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
}

.modal-form-select.sample-style:focus {
  border-color: #3b82f6;
  outline: none;
}

.modal-form-textarea.sample-style {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  resize: vertical;
  font-family: inherit;
  transition: border-color 0.2s ease;
}

.modal-form-textarea.sample-style:focus {
  border-color: #3b82f6;
  outline: none;
}

/* ========================================
   MODAL CONTENT STRUCTURE
   ======================================== */

.modal-content {
  display: flex;
  flex-direction: column;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  padding: 0px;
}

.modal-content-body {
  flex: 1 1 0%;
  cursor: default;
}

.modal-content.sample-style {
  display: flex;
  flex-direction: column;
  opacity: 1;
  background-color: transparent;
  flex: 1;
}

.modal-content-body.sample-style {
  flex: 1;
  padding: 20px;
  cursor: default;
  overflow-y: visible;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.modal-content-wrapper.sample-style {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.modal-form > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

/* ========================================
   MODAL FOOTER
   ======================================== */

.modal-footer {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
  padding: 0.75rem;
}

.modal-footer-buttons {
  display: flex;
  justify-content: center;
}

.modal-footer-buttons > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.modal-footer.sample-style {
  background-color: white;
  padding: 24px 24px 0 24px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  min-height: 60px;
  opacity: 1;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

.modal-footer.no-padding {
  padding: 6px 8px 0 8px;
}

.modal-footer-buttons.sample-style {
  display: flex;
  gap: 12px;
  justify-content: center;
  align-items: center;
  padding: 8px 16px;
  min-height: 48px;
}

/* Modal status message styles (matching sample modal) */
.modal-status-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #64748b;
  font-size: 14px;
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
}

.modal-status-message svg {
  width: 16px;
  height: 16px;
  color: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

/* ========================================
   MODAL RESIZE HANDLE
   ======================================== */

/* Note: Modal resize handles are defined above as invisible border handles */

/* ========================================
   MODAL HEADER COMPONENTS
   ======================================== */

.modal-header-icon {
  border-radius: 0.375rem;
  background-color: rgb(0 0 0 / 0.1);
  padding: 0.25rem;
}

.modal-header-icon-svg {
  height: 2rem;
  width: 2rem;
  color: transparent;
}

.modal-header-content {
  flex: 1 1 0%;
}

.modal-header-title {
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: unset;
}

.modal-header-subtitle {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  color: rgb(255 255 255 / 0.8);
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: unset;
}

.modal-close-button {
  border-radius: 0.375rem;
  padding: 0.25rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.modal-close-button:hover {
  background-color: rgb(255 255 255 / 0.1);
}

.modal-close-icon {
  height: 1.5rem;
  width: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

/* ========================================
   ADMIN/CLIENT DASHBOARD MODAL OVERRIDES
   ======================================== */

.admin-page .modal-container,
.admin-page .modal-container-motion,
.client-page .modal-container,
.client-page .modal-container-motion {
  background-color: #ffffff;
  color: #111827;
  border-color: #e5e7eb;
}

/* Modal form inputs override for admin/client dashboards */
.admin-page .modal-form-input,
.admin-page .modal-form-select,
.admin-page .modal-form-textarea,
.client-page .modal-form-input,
.client-page .modal-form-select,
.client-page .modal-form-textarea {
  background-color: #ffffff;
  color: #111827;
  border-color: #d1d5db;
}

.admin-page .modal-form-input::-moz-placeholder, .admin-page .modal-form-textarea::-moz-placeholder, .client-page .modal-form-input::-moz-placeholder, .client-page .modal-form-textarea::-moz-placeholder {
  color: #9ca3af;
}

.admin-page .modal-form-input::placeholder,
.admin-page .modal-form-textarea::placeholder,
.client-page .modal-form-input::placeholder,
.client-page .modal-form-textarea::placeholder {
  color: #9ca3af;
}

.modal-theme-blue {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}

.modal-theme-green {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}

/* Color utility classes for dynamic styling */
.modal-color-blue {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.modal-color-green {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.modal-color-purple {
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}

.modal-color-orange {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}

.modal-color-red {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

/* Required field indicator */
.modal-required {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

/* Resize handle SVG */
.modal-resize-svg {
  height: 1rem;
  width: 1rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

/* Modal accessibility */
.modal-focus-trap {
  /* Focus trap styles if needed */
  position: relative;
  outline: none;
}

.modal-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.modal-aria-hidden {
  aria-hidden: true;
}

/* ========================================
   FOOTER STYLES
   ======================================== */

/* Modal footer base */
.modal-footer {
  margin-top: auto;
  border-bottom-right-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  background-color: transparent;
  padding: 1rem;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.modal-footer.centered {
  display: flex;
  justify-content: center;
}

.modal-footer.centered > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.modal-footer.left-aligned {
  display: flex;
  justify-content: flex-end;
}

.modal-footer.left-aligned > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.modal-footer.compact {
  padding: 0.75rem;
}

.modal-footer-basic {
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
  padding: 1rem;
}

/* ========================================
   TRANSITIONS & ANIMATIONS
   ======================================== */

/* Modal animation utilities */
.modal-fade-in {
  transition-property: opacity;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-slide-in {
  transition-property: transform;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-scale-in {
  transition-property: all;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* ========================================
   INVOICE MODAL STYLES
   ======================================== */

.invoice-details-container {
  background-color: #e9ecef;
  border: 1px solid #e9ecef;
}

.invoice-details-title {
  color: #495057;
}

.invoice-details-icon {
  color: #6c757d;
}

.invoice-header-desc {
  background-color: #d1d5db;
  color: #374151;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.invoice-header-qty,
.invoice-header-price,
.invoice-header-total {
  background-color: #d1d5db;
  color: #374151;
}

.invoice-header-del {
  background-color: #d1d5db;
  color: #374151;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.invoice-item-row {
  margin: 0;
  padding: 2px 0;
}

.invoice-summary-container {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.invoice-summary-title {
  color: #495057;
}

.invoice-summary-icon {
  color: #6c757d;
}

.invoice-summary-subtotal {
  background-color: #e3f2fd;
  border: 1px solid #bbdefb;
}

.invoice-summary-subtotal-label {
  color: #1565c0;
}

.invoice-summary-subtotal-value {
  color: #0d47a1;
}

.invoice-summary-tax {
  background-color: #fff3e0;
  border: 1px solid #ffcc02;
}

.invoice-summary-tax-label {
  color: #e65100;
}

.invoice-summary-tax-value {
  color: #bf360c;
}

.invoice-summary-total {
  background-color: #e8f5e8;
  border: 1px solid #c8e6c9;
}

.invoice-summary-total-label {
  color: #2e7d32;
}

.invoice-summary-total-value {
  color: #1b5e20;
}

/* ========================================
   RESPONSIVE UTILITIES
   ======================================== */

@media (max-width: 768px) {
  .modal-form-grid {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .modal-header-content > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }
  
  .modal-header-icon svg {
    height: 1.5rem;
    width: 1.5rem;
  }
  
  .modal-header-title {
    font-size: 1rem;
    line-height: 1.5rem;
  }
  
  .modal-header-subtitle {
    font-size: 0.75rem;
    line-height: 1rem;
  }
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/components/forms.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/* ========================================
   FORM COMPONENT STYLES
   Reusable form element styles
   ======================================== */

/* ========================================
   BASE FORM STYLES
   ======================================== */

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: #ffffff;
  color: #111827;
  transition: border-color 0.2s ease;
  font-family: var(--default-font);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::-moz-placeholder, .form-textarea::-moz-placeholder {
  color: #9ca3af;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #9ca3af;
}

/* ========================================
   DATE INPUT STYLES (MATCHING SAMPLE MODAL)
   ======================================== */

.form-input[type="date"] {
  cursor: pointer;
  font-family: system-ui, -apple-system, sans-serif;
  line-height: 1.4;
  box-sizing: border-box;
  outline: none;
  margin: 0;
  vertical-align: baseline;
  -webkit-appearance: menulist-button;
  -moz-appearance: textfield;
  color-scheme: light;
}

.form-input[type="date"]:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.form-input[type="date"]:blur {
  border-color: #d1d5db;
  box-shadow: none;
}

/* ========================================
   FORM VALIDATION STATES
   ======================================== */

.form-input.is-valid,
.form-select.is-valid,
.form-textarea.is-valid {
  border-color: #10b981;
}

.form-input.is-valid:focus,
.form-select.is-valid:focus,
.form-textarea.is-valid:focus {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-input.is-invalid,
.form-select.is-invalid,
.form-textarea.is-invalid {
  border-color: #dc2626;
}

.form-input.is-invalid:focus,
.form-select.is-invalid:focus,
.form-textarea.is-invalid:focus {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

/* ========================================
   FORM FEEDBACK
   ======================================== */

.form-feedback {
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.form-feedback.valid {
  color: #10b981;
}

.form-feedback.invalid {
  color: #dc2626;
}

/* ========================================
   CHECKBOX AND RADIO STYLES
   ======================================== */

.form-check {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.form-check-input {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
  accent-color: #3b82f6;
}

.form-check-label {
  font-size: 14px;
  color: #374151;
  cursor: pointer;
}

/* ========================================
   ENHANCED CHECKBOX AND RADIO STYLES (MATCHING SAMPLE MODAL)
   ======================================== */

.form-check.enhanced {
  display: flex;
  align-items: flex-start;
  gap: 0px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  line-height: 1.4;
}

.form-check.enhanced .form-check-input {
  margin: 0;
  margin-top: 2px;
}

.form-check.enhanced .form-check-label {
  margin: 0;
}

/* ========================================
   RADIO BUTTON GROUPS (MATCHING SAMPLE MODAL)
   ======================================== */

.radio-group {
  display: flex;
  gap: 16px;
}

.radio-group .form-check {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  margin-bottom: 0;
}

.radio-group .form-check-input {
  margin: 0;
}

/* ========================================
   CHECKBOX GROUPS (MATCHING SAMPLE MODAL)
   ======================================== */

.checkbox-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.checkbox-group .form-check {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  margin-bottom: 0;
}

.checkbox-group .form-check-input {
  margin: 0;
}

/* ========================================
   FORM SIZES
   ======================================== */

.form-input-sm,
.form-select-sm,
.form-textarea-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.form-input-lg,
.form-select-lg,
.form-textarea-lg {
  padding: 0.75rem 1rem;
  font-size: 1rem;
}

/* ========================================
   FORM LAYOUTS
   ======================================== */

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-col {
  flex: 1;
}

.form-col-auto {
  flex: 0 0 auto;
}

/* ========================================
   INPUT GROUPS
   ======================================== */

.input-group {
  display: flex;
  align-items: stretch;
}

.input-group-prepend,
.input-group-append {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  color: #6b7280;
  font-size: 0.875rem;
}

.input-group-prepend {
  border-right: 0;
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.input-group-append {
  border-left: 0;
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.input-group .form-input {
  border-radius: 0;
  border-left: 0;
  border-right: 0;
}

.input-group .form-input:first-child {
  border-left: 1px solid #d1d5db;
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.input-group .form-input:last-child {
  border-right: 1px solid #d1d5db;
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

/* ========================================
   SEARCH INPUTS
   ======================================== */

.search-input {
  position: relative;
}

.search-input .form-input {
  padding-left: 2.5rem;
}

.search-input .search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  pointer-events: none;
}

/* ========================================
   FILE INPUTS
   ======================================== */

.file-input {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.file-input input[type="file"] {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-input-label {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #374151;
  transition: all 0.15s ease;
}

.file-input:hover .file-input-label {
  background-color: #e5e7eb;
  border-color: #9ca3af;
}

/* ========================================
   ENHANCED FILE UPLOAD STYLES (MATCHING SAMPLE MODAL)
   ======================================== */

.file-upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  background-color: #f9fafb;
  transition: border-color 0.2s ease;
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: #9ca3af;
}

.file-upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.file-upload-icon {
  width: 24px;
  height: 24px;
  color: #6b7280;
}

.file-upload-text {
  font-size: 14px;
  color: #6b7280;
}

.file-upload-subtext {
  font-size: 12px;
  color: #9ca3af;
}

/* ========================================
   ADMIN/CLIENT DASHBOARD FORMS
   ======================================== */

/* Override any theme classes for admin/client dashboards */
.admin-page .form-label,
.client-page .form-label {
  color: #374151;
}

.admin-page .form-input,
.admin-page .form-select,
.admin-page .form-textarea,
.client-page .form-input,
.client-page .form-select,
.client-page .form-textarea {
  background-color: #ffffff;
  color: #111827;
  border-color: #d1d5db;
}

.admin-page .form-input::-moz-placeholder, .admin-page .form-textarea::-moz-placeholder, .client-page .form-input::-moz-placeholder, .client-page .form-textarea::-moz-placeholder {
  color: #9ca3af;
}

.admin-page .form-input::placeholder,
.admin-page .form-textarea::placeholder,
.client-page .form-input::placeholder,
.client-page .form-textarea::placeholder {
  color: #9ca3af;
}

.admin-page .form-input:focus,
.admin-page .form-select:focus,
.admin-page .form-textarea:focus,
.client-page .form-input:focus,
.client-page .form-select:focus,
.client-page .form-textarea:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ========================================
   RESPONSIVE FORMS
   ======================================== */

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }
  
  .form-col {
    margin-bottom: 1rem;
  }
  
  .input-group {
    flex-direction: column;
  }
  
  .input-group-prepend,
  .input-group-append {
    border-radius: 0;
    border: 1px solid #d1d5db;
  }
  
  .input-group-prepend {
    border-bottom: 0;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
  }
  
  .input-group-append {
    border-top: 0;
    border-bottom-left-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
  }
  
  .input-group .form-input {
    border-radius: 0;
    border-left: 1px solid #d1d5db;
    border-right: 1px solid #d1d5db;
  }
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/components/buttons.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/* ========================================
   BUTTON COMPONENT STYLES
   Reusable button styles for the application
   ======================================== */

/* ========================================
   BASE BUTTON STYLES
   ======================================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.15s ease;
  font-family: var(--default-font);
}

.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* ========================================
   SAMPLE MODAL BUTTON STYLES
   ======================================== */

.btn.sample-primary {
  padding: 8px 24px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
  margin-right: 12px;
}

.btn.sample-primary:hover {
  background-color: #2563eb;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.btn.sample-secondary {
  padding: 8px 24px;
  background-color: #6b7280;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
  transform: translateY(-2px);
}

.btn.sample-secondary:hover {
  background-color: #4b5563;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(107, 114, 128, 0.4);
}

/* ========================================
   BUTTON VARIANTS
   ======================================== */

.btn-primary {
  padding: 12px 24px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
  padding: 12px 24px;
  background-color: #6b7280;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
  transform: translateY(-2px);
}

.btn-secondary:hover:not(:disabled) {
  background-color: #4b5563;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(107, 114, 128, 0.4);
}

.btn-danger {
  padding: 12px 24px;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
  transform: translateY(-2px);
}

.btn-danger:hover:not(:disabled) {
  background-color: #dc2626;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.4);
}

.btn-outline {
  padding: 12px 24px;
  background-color: transparent;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.btn-outline:hover:not(:disabled) {
  background-color: #f9fafb;
  border-color: #9ca3af;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-success {
  padding: 12px 24px;
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
  transform: translateY(-2px);
}

.btn-success:hover:not(:disabled) {
  background-color: #059669;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
}

.btn-warning {
  padding: 12px 24px;
  background-color: #f59e0b;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
  transform: translateY(-2px);
}

.btn-warning:hover:not(:disabled) {
  background-color: #d97706;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(245, 158, 11, 0.4);
}

/* ========================================
   BUTTON SIZES
   ======================================== */

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.btn-xl {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

/* ========================================
   BUTTON GROUPS
   ======================================== */

.btn-group {
  display: inline-flex;
  border-radius: 0.375rem;
  overflow: hidden;
}

.btn-group .btn {
  border-radius: 0;
  border-right-width: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.btn-group .btn:last-child {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
  border-right-width: 1px;
}

/* ========================================
   ICON BUTTONS
   ======================================== */

.btn-icon {
  padding: 0.5rem;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon-sm {
  padding: 0.25rem;
  width: 2rem;
  height: 2rem;
}

.btn-icon-lg {
  padding: 0.75rem;
  width: 3rem;
  height: 3rem;
}

/* ========================================
   LOADING STATES
   ======================================== */

.btn-loading {
  position: relative;
  color: transparent;
}

.btn-loading::after {
  content: '';
  position: absolute;
  width: 1rem;
  height: 1rem;
  top: 50%;
  left: 50%;
  margin-left: -0.5rem;
  margin-top: -0.5rem;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: btn-spin 1s linear infinite;
}

.btn-loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: btn-spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes btn-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ========================================
   ADMIN/CLIENT DASHBOARD BUTTONS
   ======================================== */

/* Override any theme classes for admin/client dashboards */
.admin-page .btn-outline,
.client-page .btn-outline {
  color: #1f2937;
  border-color: #d1d5db;
}

.admin-page .btn-outline:hover:not(:disabled),
.client-page .btn-outline:hover:not(:disabled) {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

/* ========================================
   RESPONSIVE BUTTONS
   ======================================== */

@media (max-width: 768px) {
  .btn {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
  
  .btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }
  
  .btn-lg {
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/components/cards.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/* ========================================
   CARD COMPONENT STYLES
   Reusable card styles for the application
   ======================================== */

/* ========================================
   BASE CARD STYLES
   ======================================== */

.card {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* ========================================
   CARD HEADER
   ======================================== */

.card-header {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.card-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0.25rem 0 0 0;
}

/* ========================================
   CARD BODY
   ======================================== */

.card-body {
  padding: 0;
}

.card-body.compact {
  padding: 1rem;
}

.card-body.spacious {
  padding: 2rem;
}

/* ========================================
   CARD FOOTER
   ======================================== */

.card-footer {
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* ========================================
   CARD VARIANTS
   ======================================== */

.card-primary {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.card-success {
  border-color: #10b981;
  background-color: #ecfdf5;
}

.card-warning {
  border-color: #f59e0b;
  background-color: #fffbeb;
}

.card-danger {
  border-color: #dc2626;
  background-color: #fef2f2;
}

.card-info {
  border-color: #06b6d4;
  background-color: #ecfeff;
}

/* ========================================
   CARD SIZES
   ======================================== */

.card-sm {
  padding: 1rem;
}

.card-sm .card-title {
  font-size: 1.125rem;
}

.card-lg {
  padding: 2rem;
}

.card-lg .card-title {
  font-size: 1.5rem;
}

.card-xl {
  padding: 2.5rem;
}

.card-xl .card-title {
  font-size: 1.875rem;
}

/* ========================================
   STATISTICS CARDS
   ======================================== */

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.stat-card .stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-card .stat-label {
  font-size: 0.875rem;
  opacity: 0.9;
  margin-bottom: 0.25rem;
}

.stat-card .stat-change {
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.stat-card .stat-change.positive {
  color: #10b981;
}

.stat-card .stat-change.negative {
  color: #ef4444;
}

/* ========================================
   FEATURE CARDS
   ======================================== */

.feature-card {
  text-align: center;
  padding: 2rem 1.5rem;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.feature-card .feature-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto 1rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.feature-card .feature-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #111827;
}

.feature-card .feature-description {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.5;
}

/* ========================================
   PRODUCT CARDS
   ======================================== */

.product-card {
  position: relative;
  overflow: hidden;
}

.product-card .product-image {
  width: 100%;
  height: 200px;
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

.product-card .product-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #ef4444;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.product-card .product-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.5rem;
}

.product-card .product-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

/* ========================================
   TESTIMONIAL CARDS
   ======================================== */

.testimonial-card {
  background: #f8fafc;
  border: none;
  position: relative;
}

.testimonial-card::before {
  content: '"';
  position: absolute;
  top: -0.5rem;
  left: 1rem;
  font-size: 4rem;
  color: #e5e7eb;
  font-family: serif;
}

.testimonial-card .testimonial-content {
  font-style: italic;
  margin-bottom: 1rem;
  color: #374151;
}

.testimonial-card .testimonial-author {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.testimonial-card .author-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
}

.testimonial-card .author-info .author-name {
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.25rem;
}

.testimonial-card .author-info .author-title {
  font-size: 0.875rem;
  color: #6b7280;
}

/* ========================================
   ADMIN/CLIENT DASHBOARD CARDS
   ======================================== */

/* Override any theme classes for admin/client dashboards */
.admin-page .card,
.client-page .card {
  background-color: #ffffff;
  border-color: #e5e7eb;
  color: #111827;
}

.admin-page .card-title,
.client-page .card-title {
  color: #111827;
}

.admin-page .card-subtitle,
.client-page .card-subtitle {
  color: #6b7280;
}

.admin-page .card-header,
.admin-page .card-footer,
.client-page .card-header,
.client-page .card-footer {
  border-color: #e5e7eb;
}

/* ========================================
   NAVIGATION CARDS (Clients Management Style)
   ======================================== */

/* Base Navigation Card */
.nav-card {
  position: relative;
  border-radius: 0.75rem;
  border: 2px solid;
  transition: all 0.2s ease;
  cursor: pointer;
  overflow: hidden;
  min-height: 140px;
  display: flex;
  flex-direction: column;
}

/* Navigation Card States */
.nav-card.active {
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.nav-card.disabled {
  border-color: #e5e7eb;
  cursor: not-allowed;
  opacity: 0.6;
  background-color: #f9fafb;
}

.nav-card.inactive {
  border-color: #e5e7eb;
  background-color: #ffffff;
}

.nav-card.inactive:hover {
  border-color: #d1d5db;
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px) scale(1.02);
  background-color: #f9fafb;
}

/* Navigation Card Color Variants - Active State */
.nav-card.active.clients {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #3b82f6;
}

.nav-card.active.projects {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: #10b981;
}

.nav-card.active.invoices {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: #f59e0b;
}

.nav-card.active.payments {
  background: linear-gradient(135deg, #e9d5ff 0%, #ddd6fe 100%);
  border-color: #8b5cf6;
}

.nav-card.active.categories {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #3b82f6;
}

.nav-card.active.services {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: #10b981;
}

.nav-card.active.options {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: #f59e0b;
}

.nav-card.active.features {
  background: linear-gradient(135deg, #e9d5ff 0%, #ddd6fe 100%);
  border-color: #8b5cf6;
}

/* Navigation Card Content */
.nav-card-content {
  position: relative;
  padding: 0.75rem;
  border-radius: 0.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Navigation Card Header */
.nav-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.25rem;
}

.nav-card-header-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Navigation Card Icon */
.nav-card-icon {
  padding: 0.5rem;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.nav-card.active .nav-card-icon.clients {
  background-color: #dbeafe;
  color: #2563eb;
}

.nav-card.active .nav-card-icon.projects {
  background-color: #d1fae5;
  color: #059669;
}

.nav-card.active .nav-card-icon.invoices {
  background-color: #fef3c7;
  color: #d97706;
}

.nav-card.active .nav-card-icon.payments {
  background-color: #e9d5ff;
  color: #7c3aed;
}

.nav-card.active .nav-card-icon.categories {
  background-color: #dbeafe;
  color: #2563eb;
}

.nav-card.active .nav-card-icon.services {
  background-color: #d1fae5;
  color: #059669;
}

.nav-card.active .nav-card-icon.options {
  background-color: #fef3c7;
  color: #d97706;
}

.nav-card.active .nav-card-icon.features {
  background-color: #e9d5ff;
  color: #7c3aed;
}

.nav-card.disabled .nav-card-icon {
  background-color: #f3f4f6;
  color: #9ca3af;
}

.nav-card.inactive .nav-card-icon {
  background-color: #f9fafb;
  color: #6b7280;
}

.nav-card.inactive:hover .nav-card-icon {
  background-color: #f3f4f6;
}

/* Navigation Card Counter */
.nav-card-counter {
  font-size: 1.125rem;
  font-weight: 700;
  transition: color 0.3s ease;
}

.nav-card.active .nav-card-counter {
  color: #111827;
}

.nav-card.disabled .nav-card-counter {
  color: #9ca3af;
}

.nav-card.inactive .nav-card-counter {
  color: #374151;
}

.nav-card.inactive:hover .nav-card-counter {
  color: #111827;
}

/* Navigation Card Title */
.nav-card-title {
  font-size: 1.125rem;
  font-weight: 700;
  transition: color 0.3s ease;
}

.nav-card.active .nav-card-title {
  color: #111827;
}

.nav-card.disabled .nav-card-title {
  color: #9ca3af;
}

.nav-card.inactive .nav-card-title {
  color: #374151;
}

.nav-card.inactive:hover .nav-card-title {
  color: #111827;
}

/* Navigation Card Description */
.nav-card-description {
  margin-bottom: 0.25rem;
}

.nav-card-description p {
  font-size: 0.75rem;
  line-height: 1.5;
  transition: color 0.3s ease;
}

.nav-card.active .nav-card-description p {
  color: #4b5563;
}

.nav-card.disabled .nav-card-description p {
  color: #9ca3af;
}

.nav-card.inactive .nav-card-description p {
  color: #6b7280;
}

.nav-card.inactive:hover .nav-card-description p {
  color: #4b5563;
}

/* Navigation Card Bottom Section */
.nav-card-bottom {
  margin-top: auto;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 0;
}

/* Navigation Card Selected Name */
.nav-card-selected-name {
  flex: 1;
  min-width: 0;
}

.nav-card-selected-name-container {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  max-width: 100%;
}

.nav-card.active .nav-card-selected-name-container.clients {
  background-color: rgba(219, 234, 254, 0.8);
  color: #1e3a8a;
}

.nav-card.active .nav-card-selected-name-container.projects {
  background-color: rgba(209, 250, 229, 0.8);
  color: #064e3b;
}

.nav-card.active .nav-card-selected-name-container.invoices {
  background-color: rgba(254, 243, 199, 0.8);
  color: #92400e;
}

.nav-card.active .nav-card-selected-name-container.payments {
  background-color: rgba(233, 213, 255, 0.8);
  color: #581c87;
}

.nav-card.inactive .nav-card-selected-name-container.clients {
  background-color: rgba(219, 234, 254, 0.8);
  color: #1e40af;
}

.nav-card.inactive .nav-card-selected-name-container.projects {
  background-color: rgba(209, 250, 229, 0.8);
  color: #047857;
}

.nav-card.inactive .nav-card-selected-name-container.invoices {
  background-color: rgba(254, 243, 199, 0.8);
  color: #b45309;
}

.nav-card.inactive .nav-card-selected-name-container.payments {
  background-color: rgba(233, 213, 255, 0.8);
  color: #6b21a8;
}

.nav-card.active .nav-card-selected-name-container.categories {
  background-color: rgba(219, 234, 254, 0.8);
  color: #1e3a8a;
}

.nav-card.active .nav-card-selected-name-container.services {
  background-color: rgba(209, 250, 229, 0.8);
  color: #064e3b;
}

.nav-card.active .nav-card-selected-name-container.options {
  background-color: rgba(254, 243, 199, 0.8);
  color: #92400e;
}

.nav-card.active .nav-card-selected-name-container.features {
  background-color: rgba(233, 213, 255, 0.8);
  color: #581c87;
}

.nav-card.inactive .nav-card-selected-name-container.categories {
  background-color: rgba(219, 234, 254, 0.8);
  color: #1e40af;
}

.nav-card.inactive .nav-card-selected-name-container.services {
  background-color: rgba(209, 250, 229, 0.8);
  color: #047857;
}

.nav-card.inactive .nav-card-selected-name-container.options {
  background-color: rgba(254, 243, 199, 0.8);
  color: #b45309;
}

.nav-card.inactive .nav-card-selected-name-container.features {
  background-color: rgba(233, 213, 255, 0.8);
  color: #6b21a8;
}

.nav-card-selected-name-content {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  min-width: 0;
}

.nav-card-selected-name-dot {
  width: 0.375rem;
  height: 0.375rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.nav-card-selected-name-dot.clients {
  background-color: #2563eb;
}

.nav-card-selected-name-dot.projects {
  background-color: #059669;
}

.nav-card-selected-name-dot.invoices {
  background-color: #d97706;
}

.nav-card-selected-name-dot.payments {
  background-color: #7c3aed;
}

.nav-card-selected-name-dot.categories {
  background-color: #2563eb;
}

.nav-card-selected-name-dot.services {
  background-color: #059669;
}

.nav-card-selected-name-dot.options {
  background-color: #d97706;
}

.nav-card-selected-name-dot.features {
  background-color: #7c3aed;
}

.nav-card-selected-name-text {
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

/* Navigation Card Row Counter (for client cards only) */
.nav-card-row-counter {
  text-align: right;
  flex-shrink: 0;
}

.nav-card-row-counter-text {
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
}

.nav-card.active .nav-card-row-counter-text.clients {
  color: #2563eb;
}

.nav-card.active .nav-card-row-counter-text.categories {
  color: #2563eb;
}

.nav-card.disabled .nav-card-row-counter-text {
  color: #9ca3af;
}

.nav-card.inactive .nav-card-row-counter-text {
  color: #6b7280;
}

/* Navigation Card Triangle Arrow */
.nav-card-arrow {
  position: absolute;
  top: 50%;
  right: -20px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 20px solid transparent;
  border-bottom: 20px solid transparent;
  border-left: 20px solid;
}

.nav-card.active .nav-card-arrow.clients {
  border-left-color: #dbeafe;
}

.nav-card.active .nav-card-arrow.projects {
  border-left-color: #d1fae5;
}

.nav-card.active .nav-card-arrow.invoices {
  border-left-color: #fef3c7;
}

.nav-card.active .nav-card-arrow.payments {
  border-left-color: #e9d5ff;
}

.nav-card.active .nav-card-arrow.categories {
  border-left-color: #dbeafe;
}

.nav-card.active .nav-card-arrow.services {
  border-left-color: #d1fae5;
}

.nav-card.active .nav-card-arrow.options {
  border-left-color: #fef3c7;
}

.nav-card.active .nav-card-arrow.features {
  border-left-color: #e9d5ff;
}

.nav-card.inactive .nav-card-arrow {
  border-left-color: #ffffff;
}

.nav-card-arrow-shadow {
  filter: drop-shadow(1px 0 0 #d1d5db);
}

/* ========================================
   RESPONSIVE CARDS
   ======================================== */

@media (max-width: 768px) {
  .card {
    padding: 1rem;
  }
  
  .card-lg {
    padding: 1.5rem;
  }
  
  .card-xl {
    padding: 2rem;
  }
  
  .feature-card {
    padding: 1.5rem 1rem;
  }
  
  .feature-card .feature-icon {
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
  }
  
  .stat-card .stat-value {
    font-size: 1.5rem;
  }
  
  /* Navigation Cards Responsive */
  .nav-card {
    min-height: 120px;
  }
  
  .nav-card-content {
    padding: 0.5rem;
  }
  
  .nav-card-header-left {
    gap: 0.5rem;
  }
  
  .nav-card-icon {
    padding: 0.375rem;
  }
  
  .nav-card-counter {
    font-size: 1rem;
  }
  
  .nav-card-title {
    font-size: 1rem;
  }
  
  .nav-card-selected-name-container {
    padding: 0.375rem 0.5rem;
  }
  
  .nav-card-selected-name-content {
    gap: 0.25rem;
  }
  
  .nav-card-arrow {
    right: -15px;
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
    border-left: 15px solid;
  }
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/components/index.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/* ========================================
   COMPONENTS INDEX
   Import all component styles in one place
   ======================================== */
/* sidebar.css is imported directly in admin-dashboard/layout.tsx */

/* ========================================
   RESPONSIVE TABLE SCROLLBAR STYLING
   ======================================== */

/* Dark gray scrollbar for responsive tables */
.overflow-x-auto::-webkit-scrollbar {
  height: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #4b5563;
}

/* Firefox scrollbar styling */
.overflow-x-auto {
  scrollbar-width: thin;
  scrollbar-color: #6b7280 #f1f5f9;
}

/* ========================================
   MAIN PAGE VERTICAL SCROLLBAR STYLING
   ======================================== */

/* Dark gray scrollbar for main page vertical scroll */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4b5563;
}

/* Firefox main scrollbar styling */
html {
  scrollbar-width: thin;
  scrollbar-color: #6b7280 #f1f5f9;
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/components/sidebar.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/* Sidebar-specific styles - completely isolated from admin page content */

/* Desktop Sidebar */
.sidebar-desktop {
  /* Main navigation items */
  .sidebar-main-nav {
    font-size: 1.125rem; /* 18px - bigger than text-base */
    font-weight: 600;
  }
  
  .admin-page .sidebar-main-nav .sidebar-icon {
    width: 1.5rem; /* 24px */
    height: 1.5rem; /* 24px */
  }
  
  /* Sub-navigation items */
  .sidebar-sub-nav {
    font-size: 1rem; /* 16px - bigger than text-sm */
    font-weight: 500;
  }
  
  .admin-page .sidebar-sub-nav .sidebar-icon {
    width: 1.25rem; /* 20px */
    height: 1.25rem; /* 20px */
  }
  
  /* Section headers */
  .sidebar-section-header {
    font-size: 0.75rem; /* 12px */
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  /* Logo text */
  .sidebar-logo-text {
    font-size: 1.125rem; /* 18px */
    font-weight: 700;
  }
  
  .sidebar-logo-subtext {
    font-size: 0.75rem; /* 12px */
    font-weight: 500;
  }
  
  /* Colorful Icons - Main Navigation */
  .admin-page .sidebar-main-nav .sidebar-icon {
    color: #3b82f6; /* Blue for main nav */
  }
  
  /* Colorful Icons - Sub Navigation */
  .admin-page .sidebar-sub-nav .sidebar-icon {
    color: #6b7280; /* Gray for sub nav */
  }
  
  /* Specific icon colors for different sections */
  /* Content Management Icons */
  .admin-page .sidebar-sub-nav[href*="visual-editor"] .sidebar-icon {
    color: #8b5cf6; /* Purple */
  }
  .admin-page .sidebar-sub-nav[href*="services"] .sidebar-icon {
    color: #10b981; /* Emerald */
  }
  .admin-page .sidebar-sub-nav[href*="team-members"] .sidebar-icon {
    color: #f59e0b; /* Amber */
  }
  .admin-page .sidebar-sub-nav[href*="technologies"] .sidebar-icon {
    color: #ef4444; /* Red */
  }
  .admin-page .sidebar-sub-nav[href*="testimonials"] .sidebar-icon {
    color: #06b6d4; /* Cyan */
  }
  .admin-page .sidebar-sub-nav[href*="blog"] .sidebar-icon {
    color: #84cc16; /* Lime */
  }
  .admin-page .sidebar-sub-nav[href*="legal-pages"] .sidebar-icon {
    color: #f97316; /* Orange */
  }
  
  /* Business Icons */
  .admin-page .sidebar-sub-nav[href*="jobs"] .sidebar-icon {
    color: #6366f1; /* Indigo */
  }
  .admin-page .sidebar-sub-nav[href*="clients"] .sidebar-icon {
    color: #ec4899; /* Pink */
  }
  .admin-page .sidebar-sub-nav[href*="clients-manager"] .sidebar-icon {
    color: #14b8a6; /* Teal */
  }
  .admin-page .sidebar-sub-nav[href*="projects"] .sidebar-icon {
    color: #8b5cf6; /* Purple */
  }
  .admin-page .sidebar-sub-nav[href*="invoices"] .sidebar-icon {
    color: #10b981; /* Emerald */
  }
  .admin-page .sidebar-sub-nav[href*="contact-forms"] .sidebar-icon {
    color: #f59e0b; /* Amber */
  }
  
  /* System Icons */
  .admin-page .sidebar-sub-nav[href*="data-upload"] .sidebar-icon {
    color: #06b6d4; /* Cyan */
  }
  .admin-page .sidebar-sub-nav[href*="chatbot"] .sidebar-icon {
    color: #84cc16; /* Lime */
  }
  .admin-page .sidebar-sub-nav[href*="users"] .sidebar-icon {
    color: #f97316; /* Orange */
  }
  .admin-page .sidebar-sub-nav[href*="settings"] .sidebar-icon {
    color: #6b7280; /* Gray */
  }
}

/* Mobile Sidebar */
.sidebar-mobile {
  /* Main navigation items */
  .sidebar-main-nav {
    font-size: 1.125rem; /* 18px - bigger than text-base */
    font-weight: 600;
  }
  
  .admin-page .sidebar-main-nav .sidebar-icon {
    width: 1.5rem; /* 24px */
    height: 1.5rem; /* 24px */
  }
  
  /* Sub-navigation items */
  .sidebar-sub-nav {
    font-size: 1rem; /* 16px - bigger than text-sm */
    font-weight: 500;
  }
  
  .admin-page .sidebar-sub-nav .sidebar-icon {
    width: 1.25rem; /* 20px */
    height: 1.25rem; /* 20px */
  }
  
  /* Section headers */
  .sidebar-section-header {
    font-size: 0.75rem; /* 12px */
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  /* Logo text */
  .sidebar-logo-text {
    font-size: 1.125rem; /* 18px */
    font-weight: 700;
  }
  
  .sidebar-logo-subtext {
    font-size: 0.75rem; /* 12px */
    font-weight: 500;
  }
  
  /* Colorful Icons - Main Navigation */
  .admin-page .sidebar-main-nav .sidebar-icon {
    color: #3b82f6; /* Blue for main nav */
  }
  
  /* Colorful Icons - Sub Navigation */
  .admin-page .sidebar-sub-nav .sidebar-icon {
    color: #6b7280; /* Gray for sub nav */
  }
  
  /* Specific icon colors for different sections */
  /* Content Management Icons */
  .admin-page .sidebar-sub-nav[href*="visual-editor"] .sidebar-icon {
    color: #8b5cf6; /* Purple */
  }
  .admin-page .sidebar-sub-nav[href*="services"] .sidebar-icon {
    color: #10b981; /* Emerald */
  }
  .admin-page .sidebar-sub-nav[href*="team-members"] .sidebar-icon {
    color: #f59e0b; /* Amber */
  }
  .admin-page .sidebar-sub-nav[href*="technologies"] .sidebar-icon {
    color: #ef4444; /* Red */
  }
  .admin-page .sidebar-sub-nav[href*="testimonials"] .sidebar-icon {
    color: #06b6d4; /* Cyan */
  }
  .admin-page .sidebar-sub-nav[href*="blog"] .sidebar-icon {
    color: #84cc16; /* Lime */
  }
  .admin-page .sidebar-sub-nav[href*="legal-pages"] .sidebar-icon {
    color: #f97316; /* Orange */
  }
  
  /* Business Icons */
  .admin-page .sidebar-sub-nav[href*="jobs"] .sidebar-icon {
    color: #6366f1; /* Indigo */
  }
  .admin-page .sidebar-sub-nav[href*="clients"] .sidebar-icon {
    color: #ec4899; /* Pink */
  }
  .admin-page .sidebar-sub-nav[href*="clients-manager"] .sidebar-icon {
    color: #14b8a6; /* Teal */
  }
  .admin-page .sidebar-sub-nav[href*="projects"] .sidebar-icon {
    color: #8b5cf6; /* Purple */
  }
  .admin-page .sidebar-sub-nav[href*="invoices"] .sidebar-icon {
    color: #10b981; /* Emerald */
  }
  .admin-page .sidebar-sub-nav[href*="contact-forms"] .sidebar-icon {
    color: #f59e0b; /* Amber */
  }
  
  /* System Icons */
  .admin-page .sidebar-sub-nav[href*="data-upload"] .sidebar-icon {
    color: #06b6d4; /* Cyan */
  }
  .admin-page .sidebar-sub-nav[href*="chatbot"] .sidebar-icon {
    color: #84cc16; /* Lime */
  }
  .admin-page .sidebar-sub-nav[href*="users"] .sidebar-icon {
    color: #f97316; /* Orange */
  }
  .admin-page .sidebar-sub-nav[href*="settings"] .sidebar-icon {
    color: #6b7280; /* Gray */
  }
}

/* Ensure sidebar styles don't affect admin page content */

/* Force sidebar visibility - ULTIMATE SIDEBAR FIX */
.admin-page .hidden.lg\:fixed.lg\:inset-y-0.lg\:flex.lg\:w-64.lg\:flex-col {
  display: flex;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  /* width: 16rem; - Removed to allow dynamic width */
  flex-direction: column;
  background-color: white;
  border-right: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  /* Remove any duplicate borders or shadows */
  border-left: none;
  border-top: none;
  border-bottom: none;
}

/* Force sidebar visibility on desktop screens */
@media (min-width: 1024px) {
  .admin-page .hidden.lg\:flex {
    display: flex;
  }
  
  .admin-page .sidebar-desktop {
    display: flex;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    /* width: 16rem; - Removed to allow dynamic width */
    flex-direction: column;
    background-color: white;
    border-right: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    /* Remove duplicate borders and shadows */
    border-left: none;
    border-top: none;
    border-bottom: none;
  }
}

/* Admin Page Title Icons - Same colors as sidebar */
/* Override default blue color for title icons to match sidebar colors */

/* Services Management - Emerald */
.admin-page .h-14.w-14.text-blue-600 {
  color: #10b981; /* Emerald - same as sidebar */
}

/* Clients Management - Teal (UserIcon) */
.admin-page .h-14.w-14.text-teal-600 {
  color: #14b8a6; /* Teal - same as sidebar */
}

/* Visual Editor - Purple */
.admin-page .h-14.w-14.text-purple-600 {
  color: #8b5cf6; /* Purple - same as sidebar */
}

/* Team Members - Amber */
.admin-page .h-14.w-14.text-amber-600 {
  color: #f59e0b; /* Amber - same as sidebar */
}

/* Technologies - Red */
.admin-page .h-14.w-14.text-red-600 {
  color: #ef4444; /* Red - same as sidebar */
}

/* Testimonials - Cyan */
.admin-page .h-14.w-14.text-cyan-600 {
  color: #06b6d4; /* Cyan - same as sidebar */
}

/* Blog - Lime */
.admin-page .h-14.w-14.text-lime-600 {
  color: #84cc16; /* Lime - same as sidebar */
}

/* Legal Pages - Orange */
.admin-page .h-14.w-14.text-orange-600 {
  color: #f97316; /* Orange - same as sidebar */
}

/* Jobs - Indigo */
.admin-page .h-14.w-14.text-indigo-600 {
  color: #6366f1; /* Indigo - same as sidebar */
}

/* Clients - Pink */
.admin-page .h-14.w-14.text-pink-600 {
  color: #ec4899; /* Pink - same as sidebar */
}

/* Projects - Purple */
.admin-page .h-14.w-14.text-purple-600 {
  color: #8b5cf6; /* Purple - same as sidebar */
}

/* Invoices - Emerald */
.admin-page .h-14.w-14.text-emerald-600 {
  color: #10b981; /* Emerald - same as sidebar */
}

/* Contact Forms - Amber */
.admin-page .h-14.w-14.text-amber-600 {
  color: #f59e0b; /* Amber - same as sidebar */
}

/* Data Upload - Cyan */
.admin-page .h-14.w-14.text-cyan-600 {
  color: #06b6d4; /* Cyan - same as sidebar */
}

/* Chatbot - Lime */
.admin-page .h-14.w-14.text-lime-600 {
  color: #84cc16; /* Lime - same as sidebar */
}

/* Users - Orange */
.admin-page .h-14.w-14.text-orange-600 {
  color: #f97316; /* Orange - same as sidebar */
}

/* Settings - Gray */
.admin-page .h-14.w-14.text-gray-600 {
  color: #6b7280; /* Gray - same as sidebar */
}

/* Dashboard - Blue */
.admin-page .h-14.w-14.text-blue-600 {
  color: #3b82f6; /* Blue - same as sidebar */
}

/* Sidebar Collapse Support */
.admin-page .sidebar-collapsed {
  width: 4rem;
}

.admin-page .sidebar-expanded {
  width: 16rem;
}

/* Responsive Sidebar Behavior */
/* Hide desktop sidebar on mobile/tablet */
@media (max-width: 1023px) {
  .admin-page .hidden.lg\:flex {
    display: none !important;
  }
}

/* Show desktop sidebar only on desktop */
@media (min-width: 1024px) {
  .admin-page .hidden.lg\:flex {
    display: flex !important;
  }
}

/* Mobile sidebar overlay and positioning */
.admin-page .sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 40;
}

/* Ensure mobile sidebar is hidden on desktop */
@media (min-width: 1024px) {
  .admin-page .sidebar-mobile {
    display: none !important;
  }
}

/* Ensure sidebar is above any background containers */
.admin-page .sidebar-collapsed,
.admin-page .sidebar-expanded {
  position: fixed;
  background-color: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  /* Ensure no duplicate backgrounds or borders */
  background-image: none;
  background-clip: padding-box;
}

/* Prevent sidebar styles from leaking to admin content */
.admin-page main,
.admin-page .admin-content,
.admin-page .admin-table,
.admin-page .admin-form {
  /* Reset any potential font inheritance */
  font-size: inherit;
  font-weight: inherit;
}

/* FORCE COLORFUL ICONS - ULTIMATE OVERRIDE */
.admin-page .sidebar-main-nav .sidebar-icon,
.admin-page .sidebar-sub-nav .sidebar-icon {
  color: inherit;
}

/* Specific colorful overrides for each section */
.admin-page .sidebar-sub-nav[href*="visual-editor"] .sidebar-icon { color: #8b5cf6; }
.admin-page .sidebar-sub-nav[href*="services"] .sidebar-icon { color: #10b981; }
.admin-page .sidebar-sub-nav[href*="team-members"] .sidebar-icon { color: #f59e0b; }
.admin-page .sidebar-sub-nav[href*="technologies"] .sidebar-icon { color: #ef4444; }
.admin-page .sidebar-sub-nav[href*="testimonials"] .sidebar-icon { color: #06b6d4; }
.admin-page .sidebar-sub-nav[href*="blog"] .sidebar-icon { color: #84cc16; }
.admin-page .sidebar-sub-nav[href*="legal-pages"] .sidebar-icon { color: #f97316; }
.admin-page .sidebar-sub-nav[href*="jobs"] .sidebar-icon { color: #6366f1; }
.admin-page .sidebar-sub-nav[href*="clients"] .sidebar-icon { color: #ec4899; }
.admin-page .sidebar-sub-nav[href*="clients-manager"] .sidebar-icon { color: #14b8a6; }
.admin-page .sidebar-sub-nav[href*="projects"] .sidebar-icon { color: #8b5cf6; }
.admin-page .sidebar-sub-nav[href*="invoices"] .sidebar-icon { color: #10b981; }
.admin-page .sidebar-sub-nav[href*="contact-forms"] .sidebar-icon { color: #f59e0b; }
.admin-page .sidebar-sub-nav[href*="data-upload"] .sidebar-icon { color: #06b6d4; }
.admin-page .sidebar-sub-nav[href*="chatbot"] .sidebar-icon { color: #84cc16; }
.admin-page .sidebar-sub-nav[href*="users"] .sidebar-icon { color: #f97316; }
.admin-page .sidebar-sub-nav[href*="settings"] .sidebar-icon { color: #6b7280; }

/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/globals.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/* Global styles and Tailwind CSS imports */

/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/admin.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/* ========================================
   ADMIN PAGES GLOBAL STYLES
   Clean, organized CSS for admin interfaces only
   ======================================== */

/* ========================================
   ADMIN PAGE CONTAINER - RESTORED ORIGINAL
   ======================================== */

.admin-page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f9fafb;
  color: #111827;
  min-height: 100vh;
  /* Override any global theme variables */
  --primary-color: #1C1B2B;
  --secondary-color: #F8F9FA;
  --text-color: #495057;
  --bg-color: #FFFFFF;
  --accent-color: #4185DD;
  --accent-secondary-color: #B42FDA;
  --divider-color: #E9ECEF;
  --error-color: rgb(230, 87, 87);
  --default-font: "Poppins", sans-serif;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --border-color: #E9ECEF;
  --card-bg: #FFFFFF;
  --hover-bg: #F8F9FA;
  /* Next.js Light Theme */
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

/* ========================================
   ADMIN PAGE LAYOUT
   ======================================== */

.admin-page * {
  box-sizing: border-box;
}

/* ========================================
   ADMIN CURSOR STYLES
   ======================================== */

.admin-page * {
  cursor: default;
}

.admin-page button,
.admin-page a,
.admin-page [role="button"],
.admin-page [onclick],
.admin-page [onClick],
.admin-page .cursor-pointer {
  cursor: pointer;
}

.admin-page button:disabled,
.admin-page a:disabled,
.admin-page [disabled] {
  cursor: not-allowed;
}

/* ========================================
   ADMIN SIDEBAR CLASSES - MOVED TO sidebar.css
   ======================================== */

/* Sidebar classes are now handled by the dedicated sidebar.css file */

/* ========================================
   ADMIN LAYOUT CONTAINERS
   ======================================== */

.admin-page .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Sidebar styles moved to sidebar.css */

.admin-page .main-content {
  flex: 1;
  padding: 2rem;
  background-color: #f9fafb;
}

/* ========================================
   ADMIN TYPOGRAPHY
   ======================================== */

.admin-page h1,
.admin-page h2,
.admin-page h3,
.admin-page h4,
.admin-page h5,
.admin-page h6 {
  margin: 0;
  font-weight: 600;
  line-height: 1.25;
  color: #111827;
}

.admin-page h1 { font-size: 1.6rem; }
.admin-page h2 { font-size: 1.6rem; }
.admin-page h3 { font-size: 1.125rem; }
.admin-page h4 { font-size: 1.25rem; }
.admin-page h5 { font-size: 1.125rem; }
.admin-page h6 { font-size: 1rem; }

.admin-page p {
  margin: 0 0 1rem 0;
  line-height: 1.6;
  color: #374151;
}

/* ========================================
   ADMIN TABLES
   ======================================== */

/* Table styles removed to allow Tailwind classes to work properly */

/* Table header and cell styles removed to allow Tailwind classes to work properly */

/* Removed tr:hover override to allow Tailwind classes to work */

/* Selection styling overrides removed to allow Tailwind classes to work properly */

/* ========================================
   ADMIN NAVIGATION
   ======================================== */

.admin-page .nav {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.admin-page .nav-item {
  display: block;
  padding: 0.75rem 1rem;
  color: #374151;
  text-decoration: none;
  border-radius: 0.375rem;
  transition: all 0.15s ease;
}

.admin-page .nav-item:hover {
  background-color: #f3f4f6;
  color: #111827;
}

.admin-page .nav-item.active {
  background-color: #dbeafe;
  color: #1e40af;
}

/* ========================================
   ADMIN BADGES AND LABELS
   ======================================== */

.admin-page .badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0.25rem;
}

.admin-page .badge-primary {
  background-color: #dbeafe;
  color: #1e40af;
}

.admin-page .badge-success {
  background-color: #d1fae5;
  color: #065f46;
}

.admin-page .badge-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.admin-page .badge-danger {
  background-color: #fecaca;
  color: #dc2626;
}

/* ========================================
   ADMIN UTILITIES
   ======================================== */

/* Utility classes removed to prevent circular dependencies */

/* All utility classes removed to prevent circular dependencies with @apply */

/* Display utility classes removed to prevent circular dependencies */

/* ========================================
   ADMIN PAGE TITLE ICONS
   ======================================== */

/* Override default blue color for title icons to match sidebar colors */

/* Icon color overrides removed to prevent circular dependencies */

/* ========================================
   ADMIN MODAL CLASSES - RESTORED ORIGINAL
   ======================================== */

.admin-page .modal-container {
  position: fixed;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid #e5e7eb;
  padding: 0;
  overflow: hidden;
  cursor: move;
  display: flex;
  flex-direction: column;
}

.admin-page .modal-container-motion {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid #e5e7eb;
  padding: 0;
  overflow: hidden;
  cursor: move;
  display: flex;
  flex-direction: column;
}

.admin-page .modal-header-motion {
  background: linear-gradient(to right, #3b82f6, #2563eb);
  border-bottom: 2px solid #1d4ed8;
  padding: 0.75rem 1.25rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: -0.5rem;
  cursor: move;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.admin-page .modal-header-motion.blue-gradient {
  background: linear-gradient(to right, #3b82f6, #2563eb);
  border-color: #1d4ed8;
}

.admin-page .modal-header-icon {
  padding: 0.25rem;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 0.375rem;
}

.admin-page .modal-header-icon-svg {
  height: 2rem;
  width: 2rem;
  color: transparent;
}

.admin-page .modal-header-content {
  flex: 1;
}

.admin-page .modal-header-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: unset;
}

.admin-page .modal-header-subtitle {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: unset;
}

.admin-page .modal-close-button {
  padding: 0.25rem;
  border-radius: 0.375rem;
  transition: background-color 0.15s ease;
}

.admin-page .modal-close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.admin-page .modal-close-icon {
  height: 1.5rem;
  width: 1.5rem;
  color: white;
}

.admin-page .modal-content {
  display: flex;
  flex-direction: column;
  border: 1px solid #e5e7eb;
  padding: 0;
}

.admin-page .modal-content-body {
  flex: 1;
  overflow-y: auto;
  cursor: default;
}

.admin-page .modal-form {
  padding: 1rem;
}

.admin-page .modal-form > * + * {
  margin-top: 1rem;
}

.admin-page .modal-form-section {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.admin-page .modal-form-section-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.admin-page .modal-form-section-icon {
  height: 1rem;
  width: 1rem;
  color: #2563eb;
}

.admin-page .modal-form-section-title {
  font-weight: 600;
  color: #111827;
}

.admin-page .modal-form-section-content > * + * {
  margin-top: 0.75rem;
}

/* Modal footer overrides removed - using sample-style classes instead */

/* Corner resize handle removed - using border handles only like sample modal */

/* ========================================
   ADMIN FORM CLASSES - RESTORED ORIGINAL
   ======================================== */

.admin-page .form-group {
  margin-bottom: 1rem;
}

.admin-page .form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.admin-page .form-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: #ffffff;
  color: #111827;
}

.admin-page .form-input:focus {
  outline: none;
  border-color: transparent;
  box-shadow: 0 0 0 2px #3b82f6;
}

.admin-page .form-input::-moz-placeholder {
  color: #9ca3af;
}

.admin-page .form-input::placeholder {
  color: #9ca3af;
}


.admin-page .form-select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: #ffffff;
  color: #111827;
}

.admin-page .form-select:focus {
  outline: none;
  border-color: transparent;
  box-shadow: 0 0 0 2px #3b82f6;
}

.admin-page .form-textarea {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: #ffffff;
  color: #111827;
  resize: none;
}

.admin-page .form-textarea::-moz-placeholder {
  color: #9ca3af;
}

.admin-page .form-textarea::placeholder {
  color: #9ca3af;
}

.admin-page .form-textarea:focus {
  outline: none;
  border-color: transparent;
  box-shadow: 0 0 0 2px #3b82f6;
}

.admin-page .form-feedback {
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.admin-page .form-feedback.invalid {
  color: #dc2626;
}

.admin-page .form-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .admin-page .form-row {
    grid-template-columns: 1fr 1fr;
  }
}

.admin-page .form-col {
  display: flex;
  flex-direction: column;
}

/* ========================================
   ADMIN BUTTON CLASSES - RESTORED ORIGINAL
   ======================================== */

/* Button overrides removed - using sample-style classes from buttons.css instead */

/* ========================================
   ADMIN RESPONSIVE DESIGN
   ======================================== */

@media (max-width: 768px) {
  .admin-page .main-content {
    padding: 1rem;
  }
  
  .admin-page .container {
    padding: 0 0.5rem;
  }
}

/* ========================================
   ADMIN DARK MODE SUPPORT
   ======================================== */

@media (prefers-color-scheme: dark) {
  .admin-page {
    background-color: #111827;
    color: #f9fafb;
  }
  
  .admin-page th {
    background-color: #374151;
   color: #f9fafb;
  }
  
  .admin-page td {
   color: #d1d5db;
  }
} 

/* ========================================
   PREVENT SIDEBAR STYLES FROM LEAKING
   ======================================== */

.admin-page main,
.admin-page .admin-content,
.admin-page .admin-table,
.admin-page .admin-form {
  /* Reset any potential font inheritance */
  font-size: inherit;
  font-weight: inherit;
}
/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/main.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/* stylelint-disable at-rule-no-unknown */
/* ========================================
   MAIN APPLICATION STYLES
   Consolidated styles for the main application
   ======================================== */

/* Tailwind CSS directives - these are processed by PostCSS */
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}
::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}
/* ! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com */
/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/
*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}
::before,
::after {
  --tw-content: '';
}
/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/
html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}
/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/
body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}
/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/
hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}
/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/
abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}
/*
Remove the default font size and weight for headings.
*/
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}
/*
Reset links to optimize for opt-in styling instead of opt-out.
*/
a {
  color: inherit;
  text-decoration: inherit;
}
/*
Add the correct font weight in Edge and Safari.
*/
b,
strong {
  font-weight: bolder;
}
/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/
code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}
/*
Add the correct font size in all browsers.
*/
small {
  font-size: 80%;
}
/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/
table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}
/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/
button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}
/*
Remove the inheritance of text transform in Edge and Firefox.
*/
button,
select {
  text-transform: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/
button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}
/*
Use the modern Firefox focus style for all focusable elements.
*/
:-moz-focusring {
  outline: auto;
}
/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/
:-moz-ui-invalid {
  box-shadow: none;
}
/*
Add the correct vertical alignment in Chrome and Firefox.
*/
progress {
  vertical-align: baseline;
}
/*
Correct the cursor style of increment and decrement buttons in Safari.
*/
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}
/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/
[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}
/*
Remove the inner padding in Chrome and Safari on macOS.
*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/
::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}
/*
Add the correct display in Chrome and Safari.
*/
summary {
  display: list-item;
}
/*
Removes the default spacing and border for appropriate elements.
*/
blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}
fieldset {
  margin: 0;
  padding: 0;
}
legend {
  padding: 0;
}
ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}
/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}
/*
Prevent resizing textareas horizontally by default.
*/
textarea {
  resize: vertical;
}
/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/
input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}
input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}
/*
Set the default cursor for buttons.
*/
button,
[role="button"] {
  cursor: pointer;
}
/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}
/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/
img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}
/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/
img,
video {
  max-width: 100%;
  height: auto;
}
/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
.container {
  width: 100%;
}
@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.pointer-events-auto {
  pointer-events: auto;
}
.visible {
  visibility: visible;
}
.collapse {
  visibility: collapse;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.sticky {
  position: sticky;
}
.inset-0 {
  inset: 0px;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.-right-1 {
  right: -0.25rem;
}
.-right-2 {
  right: -0.5rem;
}
.-top-1 {
  top: -0.25rem;
}
.-top-2 {
  top: -0.5rem;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-2 {
  bottom: 0.5rem;
}
.bottom-3 {
  bottom: 0.75rem;
}
.bottom-4 {
  bottom: 1rem;
}
.bottom-full {
  bottom: 100%;
}
.left-0 {
  left: 0px;
}
.left-1\/2 {
  left: 50%;
}
.left-10 {
  left: 2.5rem;
}
.left-3 {
  left: 0.75rem;
}
.right-0 {
  right: 0px;
}
.right-2 {
  right: 0.5rem;
}
.right-3 {
  right: 0.75rem;
}
.right-4 {
  right: 1rem;
}
.right-\[-20px\] {
  right: -20px;
}
.top-0 {
  top: 0px;
}
.top-1\/2 {
  top: 50%;
}
.top-2 {
  top: 0.5rem;
}
.top-2\.5 {
  top: 0.625rem;
}
.top-20 {
  top: 5rem;
}
.top-3 {
  top: 0.75rem;
}
.top-4 {
  top: 1rem;
}
.top-full {
  top: 100%;
}
.left-2 {
  left: 0.5rem;
}
.bottom-1 {
  bottom: 0.25rem;
}
.right-1 {
  right: 0.25rem;
}
.top-1 {
  top: 0.25rem;
}
.top-10 {
  top: 2.5rem;
}
.bottom-12 {
  bottom: 3rem;
}
.bottom-10 {
  bottom: 2.5rem;
}
.isolate {
  isolation: isolate;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.z-\[100000\] {
  z-index: 100000;
}
.z-30 {
  z-index: 30;
}
.col-span-1 {
  grid-column: span 1 / span 1;
}
.col-span-2 {
  grid-column: span 2 / span 2;
}
.col-span-5 {
  grid-column: span 5 / span 5;
}
.-m-4 {
  margin: -1rem;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-0\.5 {
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.-ml-1 {
  margin-left: -0.25rem;
}
.-mt-1 {
  margin-top: -0.25rem;
}
.-mt-2 {
  margin-top: -0.5rem;
}
.mb-0 {
  margin-bottom: 0px;
}
.mb-0\.5 {
  margin-bottom: 0.125rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.ml-0\.5 {
  margin-left: 0.125rem;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-4 {
  margin-left: 1rem;
}
.ml-6 {
  margin-left: 1.5rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-0 {
  margin-right: 0px;
}
.mr-0\.5 {
  margin-right: 0.125rem;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-1\.5 {
  margin-right: 0.375rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mr-auto {
  margin-right: auto;
}
.mt-0\.5 {
  margin-top: 0.125rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-5 {
  margin-top: 1.25rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-auto {
  margin-top: auto;
}
.mb-1\.5 {
  margin-bottom: 0.375rem;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.aspect-video {
  aspect-ratio: 16 / 9;
}
.h-0 {
  height: 0px;
}
.h-1\.5 {
  height: 0.375rem;
}
.h-1\/2 {
  height: 50%;
}
.h-10 {
  height: 2.5rem;
}
.h-12 {
  height: 3rem;
}
.h-14 {
  height: 3.5rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-2\.5 {
  height: 0.625rem;
}
.h-20 {
  height: 5rem;
}
.h-24 {
  height: 6rem;
}
.h-3 {
  height: 0.75rem;
}
.h-32 {
  height: 8rem;
}
.h-4 {
  height: 1rem;
}
.h-48 {
  height: 12rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-64 {
  height: 16rem;
}
.h-7 {
  height: 1.75rem;
}
.h-8 {
  height: 2rem;
}
.h-9 {
  height: 2.25rem;
}
.h-\[200px\] {
  height: 200px;
}
.h-\[36px\] {
  height: 36px;
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.max-h-48 {
  max-height: 12rem;
}
.max-h-64 {
  max-height: 16rem;
}
.max-h-96 {
  max-height: 24rem;
}
.max-h-\[80vh\] {
  max-height: 80vh;
}
.min-h-\[320px\] {
  min-height: 320px;
}
.min-h-screen {
  min-height: 100vh;
}
.w-0 {
  width: 0px;
}
.w-1\.5 {
  width: 0.375rem;
}
.w-1\/12 {
  width: 8.333333%;
}
.w-1\/2 {
  width: 50%;
}
.w-1\/4 {
  width: 25%;
}
.w-1\/6 {
  width: 16.666667%;
}
.w-10 {
  width: 2.5rem;
}
.w-11\/12 {
  width: 91.666667%;
}
.w-12 {
  width: 3rem;
}
.w-14 {
  width: 3.5rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-2\.5 {
  width: 0.625rem;
}
.w-20 {
  width: 5rem;
}
.w-24 {
  width: 6rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\/4 {
  width: 75%;
}
.w-32 {
  width: 8rem;
}
.w-4 {
  width: 1rem;
}
.w-40 {
  width: 10rem;
}
.w-48 {
  width: 12rem;
}
.w-5 {
  width: 1.25rem;
}
.w-56 {
  width: 14rem;
}
.w-6 {
  width: 1.5rem;
}
.w-64 {
  width: 16rem;
}
.w-7 {
  width: 1.75rem;
}
.w-8 {
  width: 2rem;
}
.w-80 {
  width: 20rem;
}
.w-9 {
  width: 2.25rem;
}
.w-full {
  width: 100%;
}
.w-px {
  width: 1px;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-\[100px\] {
  min-width: 100px;
}
.min-w-\[120px\] {
  min-width: 120px;
}
.min-w-\[150px\] {
  min-width: 150px;
}
.min-w-\[180px\] {
  min-width: 180px;
}
.min-w-\[200px\] {
  min-width: 200px;
}
.min-w-\[640px\] {
  min-width: 640px;
}
.min-w-\[80px\] {
  min-width: 80px;
}
.min-w-full {
  min-width: 100%;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-7xl {
  max-width: 80rem;
}
.max-w-full {
  max-width: 100%;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-sm {
  max-width: 24rem;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-shrink {
  flex-shrink: 1;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.grow {
  flex-grow: 1;
}
.border-collapse {
  border-collapse: collapse;
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes pulse {
  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-default {
  cursor: default;
}
.cursor-move {
  cursor: move;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.cursor-se-resize {
  cursor: se-resize;
}
.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.resize-none {
  resize: none;
}
.resize {
  resize: both;
}
.list-disc {
  list-style-type: disc;
}
.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.flex-col {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-0\.5 {
  gap: 0.125rem;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.-space-y-px > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(-1px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(-1px * var(--tw-space-y-reverse));
}
.space-x-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.125rem * var(--tw-space-x-reverse));
  margin-left: calc(0.125rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.375rem * var(--tw-space-x-reverse));
  margin-left: calc(0.375rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}
.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-gray-100 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-divide-opacity, 1));
}
.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-visible {
  overflow: visible;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
.break-words {
  overflow-wrap: break-word;
}
.break-all {
  word-break: break-all;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-3xl {
  border-radius: 1.5rem;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-none {
  border-radius: 0px;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-b-md {
  border-bottom-right-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}
.rounded-l-lg {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.rounded-t-2xl {
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
}
.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}
.rounded-t-md {
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
}
.rounded-tl-lg {
  border-top-left-radius: 0.5rem;
}
.border {
  border-width: 1px;
}
.border-0 {
  border-width: 0px;
}
.border-2 {
  border-width: 2px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-b-\[20px\] {
  border-bottom-width: 20px;
}
.border-l {
  border-left-width: 1px;
}
.border-l-4 {
  border-left-width: 4px;
}
.border-l-\[20px\] {
  border-left-width: 20px;
}
.border-r {
  border-right-width: 1px;
}
.border-r-4 {
  border-right-width: 4px;
}
.border-t {
  border-top-width: 1px;
}
.border-t-4 {
  border-top-width: 4px;
}
.border-t-\[20px\] {
  border-top-width: 20px;
}
.border-dashed {
  border-style: dashed;
}
.border-none {
  border-style: none;
}
.border-amber-200 {
  --tw-border-opacity: 1;
  border-color: rgb(253 230 138 / var(--tw-border-opacity, 1));
}
.border-amber-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 211 77 / var(--tw-border-opacity, 1));
}
.border-amber-500 {
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));
}
.border-blue-100 {
  --tw-border-opacity: 1;
  border-color: rgb(219 234 254 / var(--tw-border-opacity, 1));
}
.border-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}
.border-blue-300 {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}
.border-blue-700 {
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
}
.border-current {
  border-color: currentColor;
}
.border-emerald-200 {
  --tw-border-opacity: 1;
  border-color: rgb(167 243 208 / var(--tw-border-opacity, 1));
}
.border-emerald-300 {
  --tw-border-opacity: 1;
  border-color: rgb(110 231 183 / var(--tw-border-opacity, 1));
}
.border-emerald-500 {
  --tw-border-opacity: 1;
  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1));
}
.border-emerald-600 {
  --tw-border-opacity: 1;
  border-color: rgb(5 150 105 / var(--tw-border-opacity, 1));
}
.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-200\/50 {
  border-color: rgb(229 231 235 / 0.5);
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-gray-500 {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}
.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}
.border-green-300 {
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));
}
.border-green-500 {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}
.border-green-700 {
  --tw-border-opacity: 1;
  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));
}
.border-orange-300 {
  --tw-border-opacity: 1;
  border-color: rgb(253 186 116 / var(--tw-border-opacity, 1));
}
.border-orange-400 {
  --tw-border-opacity: 1;
  border-color: rgb(251 146 60 / var(--tw-border-opacity, 1));
}
.border-orange-500 {
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}
.border-purple-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 232 255 / var(--tw-border-opacity, 1));
}
.border-purple-200 {
  --tw-border-opacity: 1;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
}
.border-purple-300 {
  --tw-border-opacity: 1;
  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));
}
.border-purple-500 {
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.border-red-100 {
  --tw-border-opacity: 1;
  border-color: rgb(254 226 226 / var(--tw-border-opacity, 1));
}
.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}
.border-red-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}
.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-red-600 {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}
.border-red-700 {
  --tw-border-opacity: 1;
  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));
}
.border-slate-200 {
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
}
.border-slate-200\/50 {
  border-color: rgb(226 232 240 / 0.5);
}
.border-slate-300 {
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));
}
.border-teal-200 {
  --tw-border-opacity: 1;
  border-color: rgb(153 246 228 / var(--tw-border-opacity, 1));
}
.border-transparent {
  border-color: transparent;
}
.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-white\/20 {
  border-color: rgb(255 255 255 / 0.2);
}
.border-yellow-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}
.border-yellow-300 {
  --tw-border-opacity: 1;
  border-color: rgb(253 224 71 / var(--tw-border-opacity, 1));
}
.border-yellow-500 {
  --tw-border-opacity: 1;
  border-color: rgb(234 179 8 / var(--tw-border-opacity, 1));
}
.border-yellow-700 {
  --tw-border-opacity: 1;
  border-color: rgb(161 98 7 / var(--tw-border-opacity, 1));
}
.border-indigo-500 {
  --tw-border-opacity: 1;
  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
}
.border-b-transparent {
  border-bottom-color: transparent;
}
.border-l-amber-400 {
  --tw-border-opacity: 1;
  border-left-color: rgb(251 191 36 / var(--tw-border-opacity, 1));
}
.border-l-amber-50 {
  --tw-border-opacity: 1;
  border-left-color: rgb(255 251 235 / var(--tw-border-opacity, 1));
}
.border-l-blue-400 {
  --tw-border-opacity: 1;
  border-left-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.border-l-blue-50 {
  --tw-border-opacity: 1;
  border-left-color: rgb(239 246 255 / var(--tw-border-opacity, 1));
}
.border-l-blue-500 {
  --tw-border-opacity: 1;
  border-left-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-l-emerald-50 {
  --tw-border-opacity: 1;
  border-left-color: rgb(236 253 245 / var(--tw-border-opacity, 1));
}
.border-l-gray-400 {
  --tw-border-opacity: 1;
  border-left-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}
.border-l-green-400 {
  --tw-border-opacity: 1;
  border-left-color: rgb(74 222 128 / var(--tw-border-opacity, 1));
}
.border-l-red-400 {
  --tw-border-opacity: 1;
  border-left-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}
.border-l-white {
  --tw-border-opacity: 1;
  border-left-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-t-blue-600 {
  --tw-border-opacity: 1;
  border-top-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}
.border-t-gray-300 {
  --tw-border-opacity: 1;
  border-top-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-t-transparent {
  border-top-color: transparent;
}
.\!bg-white {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1)) !important;
}
.bg-amber-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));
}
.bg-amber-100\/80 {
  background-color: rgb(254 243 199 / 0.8);
}
.bg-amber-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}
.bg-amber-50\/50 {
  background-color: rgb(255 251 235 / 0.5);
}
.bg-amber-50\/80 {
  background-color: rgb(255 251 235 / 0.8);
}
.bg-amber-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}
.bg-amber-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(217 119 6 / var(--tw-bg-opacity, 1));
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/50 {
  background-color: rgb(0 0 0 / 0.5);
}
.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-100\/80 {
  background-color: rgb(219 234 254 / 0.8);
}
.bg-blue-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}
.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-50\/50 {
  background-color: rgb(239 246 255 / 0.5);
}
.bg-blue-50\/80 {
  background-color: rgb(239 246 255 / 0.8);
}
.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-500\/30 {
  background-color: rgb(59 130 246 / 0.3);
}
.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.bg-cyan-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));
}
.bg-cyan-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));
}
.bg-emerald-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 250 229 / var(--tw-bg-opacity, 1));
}
.bg-emerald-100\/80 {
  background-color: rgb(209 250 229 / 0.8);
}
.bg-emerald-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));
}
.bg-emerald-50\/80 {
  background-color: rgb(236 253 245 / 0.8);
}
.bg-emerald-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));
}
.bg-emerald-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(5 150 105 / var(--tw-bg-opacity, 1));
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-50\/50 {
  background-color: rgb(249 250 251 / 0.5);
}
.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-100\/80 {
  background-color: rgb(220 252 231 / 0.8);
}
.bg-green-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));
}
.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}
.bg-green-50\/50 {
  background-color: rgb(240 253 244 / 0.5);
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.bg-indigo-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));
}
.bg-indigo-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));
}
.bg-indigo-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}
.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}
.bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}
.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.bg-orange-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}
.bg-pink-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 242 248 / var(--tw-bg-opacity, 1));
}
.bg-pink-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 72 153 / var(--tw-bg-opacity, 1));
}
.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-100\/80 {
  background-color: rgb(243 232 255 / 0.8);
}
.bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-50\/50 {
  background-color: rgb(250 245 255 / 0.5);
}
.bg-purple-50\/80 {
  background-color: rgb(250 245 255 / 0.8);
}
.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}
.bg-purple-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-red-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));
}
.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.bg-teal-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));
}
.bg-teal-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 250 / var(--tw-bg-opacity, 1));
}
.bg-teal-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(20 184 166 / var(--tw-bg-opacity, 1));
}
.bg-transparent {
  background-color: transparent;
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}
.bg-white\/20 {
  background-color: rgb(255 255 255 / 0.2);
}
.bg-white\/50 {
  background-color: rgb(255 255 255 / 0.5);
}
.bg-white\/80 {
  background-color: rgb(255 255 255 / 0.8);
}
.bg-white\/95 {
  background-color: rgb(255 255 255 / 0.95);
}
.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}
.bg-yellow-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));
}
.bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}
.bg-yellow-50\/50 {
  background-color: rgb(254 252 232 / 0.5);
}
.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}
.bg-yellow-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
}
.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}
.bg-opacity-75 {
  --tw-bg-opacity: 0.75;
}
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.from-amber-50 {
  --tw-gradient-from: #fffbeb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 251 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-50\/30 {
  --tw-gradient-from: rgb(255 251 235 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 251 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-600 {
  --tw-gradient-from: #d97706 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(217 119 6 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-50 {
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-50\/30 {
  --tw-gradient-from: rgb(239 246 255 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500 {
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-600 {
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-emerald-50 {
  --tw-gradient-from: #ecfdf5 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(236 253 245 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-emerald-50\/30 {
  --tw-gradient-from: rgb(236 253 245 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(236 253 245 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-emerald-600 {
  --tw-gradient-from: #059669 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(5 150 105 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-50 {
  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500 {
  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-50\/30 {
  --tw-gradient-from: rgb(238 242 255 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(238 242 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-600 {
  --tw-gradient-from: #4f46e5 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(79 70 229 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-50 {
  --tw-gradient-from: #faf5ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-50\/30 {
  --tw-gradient-from: rgb(250 245 255 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-500 {
  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-600 {
  --tw-gradient-from: #9333ea var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-50 {
  --tw-gradient-from: #f8fafc var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-teal-50\/30 {
  --tw-gradient-from: rgb(240 253 250 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(240 253 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-teal-600 {
  --tw-gradient-from: #0d9488 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(13 148 136 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-transparent {
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-blue-50 {
  --tw-gradient-to: rgb(239 246 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #eff6ff var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-blue-700 {
  --tw-gradient-to: rgb(29 78 216 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #1d4ed8 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-pink-500 {
  --tw-gradient-to: rgb(236 72 153 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #ec4899 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-transparent {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-white {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-white\/50 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.5) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-amber-100 {
  --tw-gradient-to: #fef3c7 var(--tw-gradient-to-position);
}
.to-amber-50\/20 {
  --tw-gradient-to: rgb(255 251 235 / 0.2) var(--tw-gradient-to-position);
}
.to-amber-700 {
  --tw-gradient-to: #b45309 var(--tw-gradient-to-position);
}
.to-blue-100 {
  --tw-gradient-to: #dbeafe var(--tw-gradient-to-position);
}
.to-blue-50\/20 {
  --tw-gradient-to: rgb(239 246 255 / 0.2) var(--tw-gradient-to-position);
}
.to-blue-600 {
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}
.to-blue-700 {
  --tw-gradient-to: #1d4ed8 var(--tw-gradient-to-position);
}
.to-emerald-100 {
  --tw-gradient-to: #d1fae5 var(--tw-gradient-to-position);
}
.to-emerald-50\/20 {
  --tw-gradient-to: rgb(236 253 245 / 0.2) var(--tw-gradient-to-position);
}
.to-emerald-600 {
  --tw-gradient-to: #059669 var(--tw-gradient-to-position);
}
.to-emerald-700 {
  --tw-gradient-to: #047857 var(--tw-gradient-to-position);
}
.to-indigo-50 {
  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);
}
.to-indigo-50\/20 {
  --tw-gradient-to: rgb(238 242 255 / 0.2) var(--tw-gradient-to-position);
}
.to-indigo-600 {
  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);
}
.to-indigo-700 {
  --tw-gradient-to: #4338ca var(--tw-gradient-to-position);
}
.to-purple-100 {
  --tw-gradient-to: #f3e8ff var(--tw-gradient-to-position);
}
.to-purple-50\/20 {
  --tw-gradient-to: rgb(250 245 255 / 0.2) var(--tw-gradient-to-position);
}
.to-purple-600 {
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}
.to-purple-700 {
  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);
}
.to-red-500 {
  --tw-gradient-to: #ef4444 var(--tw-gradient-to-position);
}
.to-teal-50\/20 {
  --tw-gradient-to: rgb(240 253 250 / 0.2) var(--tw-gradient-to-position);
}
.to-teal-700 {
  --tw-gradient-to: #0f766e var(--tw-gradient-to-position);
}
.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}
.to-white {
  --tw-gradient-to: #fff var(--tw-gradient-to-position);
}
.bg-\[size\:20px_20px\] {
  background-size: 20px 20px;
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.p-0 {
  padding: 0px;
}
.p-0\.5 {
  padding: 0.125rem;
}
.p-1 {
  padding: 0.25rem;
}
.p-1\.5 {
  padding: 0.375rem;
}
.p-12 {
  padding: 3rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.pb-1 {
  padding-bottom: 0.25rem;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pb-20 {
  padding-bottom: 5rem;
}
.pl-1 {
  padding-left: 0.25rem;
}
.pl-10 {
  padding-left: 2.5rem;
}
.pl-2 {
  padding-left: 0.5rem;
}
.pl-3 {
  padding-left: 0.75rem;
}
.pl-4 {
  padding-left: 1rem;
}
.pl-6 {
  padding-left: 1.5rem;
}
.pr-10 {
  padding-right: 2.5rem;
}
.pr-12 {
  padding-right: 3rem;
}
.pr-20 {
  padding-right: 5rem;
}
.pr-3 {
  padding-right: 0.75rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pr-6 {
  padding-right: 1.5rem;
}
.pt-0\.5 {
  padding-top: 0.125rem;
}
.pt-1 {
  padding-top: 0.25rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-20 {
  padding-top: 5rem;
}
.pt-3 {
  padding-top: 0.75rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-6 {
  padding-top: 1.5rem;
}
.pt-1\.5 {
  padding-top: 0.375rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.align-middle {
  vertical-align: middle;
}
.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-5xl {
  font-size: 3rem;
  line-height: 1;
}
.text-\[10px\] {
  font-size: 10px;
}
.text-\[8px\] {
  font-size: 8px;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-extrabold {
  font-weight: 800;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.lowercase {
  text-transform: lowercase;
}
.capitalize {
  text-transform: capitalize;
}
.italic {
  font-style: italic;
}
.leading-relaxed {
  line-height: 1.625;
}
.leading-tight {
  line-height: 1.25;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.tracking-wide {
  letter-spacing: 0.025em;
}
.tracking-wider {
  letter-spacing: 0.05em;
}
.text-amber-500 {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}
.text-amber-600 {
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}
.text-amber-700 {
  --tw-text-opacity: 1;
  color: rgb(180 83 9 / var(--tw-text-opacity, 1));
}
.text-amber-800 {
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity, 1));
}
.text-amber-900 {
  --tw-text-opacity: 1;
  color: rgb(120 53 15 / var(--tw-text-opacity, 1));
}
.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.text-blue-100 {
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}
.text-blue-300 {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-blue-900 {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}
.text-cyan-600 {
  --tw-text-opacity: 1;
  color: rgb(8 145 178 / var(--tw-text-opacity, 1));
}
.text-emerald-600 {
  --tw-text-opacity: 1;
  color: rgb(5 150 105 / var(--tw-text-opacity, 1));
}
.text-emerald-700 {
  --tw-text-opacity: 1;
  color: rgb(4 120 87 / var(--tw-text-opacity, 1));
}
.text-emerald-800 {
  --tw-text-opacity: 1;
  color: rgb(6 95 70 / var(--tw-text-opacity, 1));
}
.text-emerald-900 {
  --tw-text-opacity: 1;
  color: rgb(6 78 59 / var(--tw-text-opacity, 1));
}
.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green-400 {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}
.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-green-900 {
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}
.text-indigo-500 {
  --tw-text-opacity: 1;
  color: rgb(99 102 241 / var(--tw-text-opacity, 1));
}
.text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}
.text-lime-600 {
  --tw-text-opacity: 1;
  color: rgb(101 163 13 / var(--tw-text-opacity, 1));
}
.text-orange-400 {
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}
.text-orange-500 {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}
.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}
.text-orange-700 {
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity, 1));
}
.text-orange-800 {
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}
.text-pink-500 {
  --tw-text-opacity: 1;
  color: rgb(236 72 153 / var(--tw-text-opacity, 1));
}
.text-pink-600 {
  --tw-text-opacity: 1;
  color: rgb(219 39 119 / var(--tw-text-opacity, 1));
}
.text-purple-400 {
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}
.text-purple-500 {
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}
.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.text-purple-700 {
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}
.text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}
.text-purple-900 {
  --tw-text-opacity: 1;
  color: rgb(88 28 135 / var(--tw-text-opacity, 1));
}
.text-red-400 {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-red-900 {
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}
.text-slate-300 {
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity, 1));
}
.text-slate-400 {
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}
.text-slate-500 {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}
.text-slate-600 {
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}
.text-slate-700 {
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity, 1));
}
.text-slate-900 {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity, 1));
}
.text-teal-500 {
  --tw-text-opacity: 1;
  color: rgb(20 184 166 / var(--tw-text-opacity, 1));
}
.text-teal-600 {
  --tw-text-opacity: 1;
  color: rgb(13 148 136 / var(--tw-text-opacity, 1));
}
.text-teal-800 {
  --tw-text-opacity: 1;
  color: rgb(17 94 89 / var(--tw-text-opacity, 1));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-white\/70 {
  color: rgb(255 255 255 / 0.7);
}
.text-white\/80 {
  color: rgb(255 255 255 / 0.8);
}
.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}
.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}
.text-yellow-700 {
  --tw-text-opacity: 1;
  color: rgb(161 98 7 / var(--tw-text-opacity, 1));
}
.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.underline {
  text-decoration-line: underline;
}
.line-through {
  text-decoration-line: line-through;
}
.placeholder-gray-400::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-400::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-500::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-500::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}
.placeholder-red-300::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(252 165 165 / var(--tw-placeholder-opacity, 1));
}
.placeholder-red-300::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(252 165 165 / var(--tw-placeholder-opacity, 1));
}
.placeholder-slate-400::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(148 163 184 / var(--tw-placeholder-opacity, 1));
}
.placeholder-slate-400::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(148 163 184 / var(--tw-placeholder-opacity, 1));
}
.opacity-0 {
  opacity: 0;
}
.opacity-100 {
  opacity: 1;
}
.opacity-25 {
  opacity: 0.25;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-60 {
  opacity: 0.6;
}
.opacity-75 {
  opacity: 0.75;
}
.opacity-80 {
  opacity: 0.8;
}
.opacity-90 {
  opacity: 0.9;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline {
  outline-style: solid;
}
.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-amber-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(245 158 11 / var(--tw-ring-opacity, 1));
}
.ring-black {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1));
}
.ring-blue-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}
.ring-green-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}
.ring-purple-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));
}
.ring-yellow-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(234 179 8 / var(--tw-ring-opacity, 1));
}
.ring-opacity-5 {
  --tw-ring-opacity: 0.05;
}
.ring-offset-2 {
  --tw-ring-offset-width: 2px;
}
.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow {
  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-xl {
  --tw-backdrop-blur: blur(24px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* ========================================
   FONT FACE DECLARATIONS
   ======================================== */

@font-face {
  font-family: 'VarinonormalRegular';
  src: url('/webfonts/VarinonormalRegular-1GXaM.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* ========================================
   CSS CUSTOM PROPERTIES (VARIABLES)
   ======================================== */

:root {
  /* Color Variables */
  --primary-color: #FFFFFF;
  --secondary-color: #FFFFFF0A;
  --text-color: #D1D1D1;
  --bg-color: #1C1B2B;
  --accent-color: #4185DD;
  --accent-secondary-color: #B42FDA;
  --divider-color: #FFFFFF0A;
  --error-color: rgb(230, 87, 87);
  --default-font: "Poppins", sans-serif;
  --shadow-color: rgba(255, 255, 255, 0.1);
  --border-color: #FFFFFF0A;
  --card-bg: #2A2938;
  --hover-bg: #FFFFFF0A;

  /* Next.js Default Variables */
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  /* Animation Variables */
  --animate-in: fadeIn 0.3s ease-out;
  --slide-in-from-top-1: slideInFromTop 0.2s ease-out;
  --slide-in-from-top-2: slideInFromTop 0.3s ease-out;
}

/* Admin/Client Dashboard Theme Override */
.admin-page,
.client-page {
  --primary-color: #1C1B2B;
  --secondary-color: #F8F9FA;
  --text-color: #495057;
  --bg-color: #FFFFFF;
  --accent-color: #4185DD;
  --accent-secondary-color: #B42FDA;
  --divider-color: #E9ECEF;
  --error-color: rgb(230, 87, 87);
  --default-font: "Poppins", sans-serif;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --border-color: #E9ECEF;
  --card-bg: #FFFFFF;
  --hover-bg: #F8F9FA;

  /* Next.js Light Theme */
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

/* ========================================
   BASE STYLES
   ======================================== */

* {
  box-sizing: border-box;
}

body {
  font-family: var(--default-font);
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
  margin: 0;
  padding: 0;
}

/* ========================================
   IMAGE LOADING FIXES
   ======================================== */

.reveal {
  visibility: visible;
  opacity: 1;
}

.image-anime {
  visibility: visible;
  opacity: 1;
}

.reveal img,
.image-anime img {
  visibility: visible;
  opacity: 1;
}

img {
  visibility: visible;
  opacity: 1;
}

/* ========================================
   GRID PATTERN BACKGROUNDS
   ======================================== */

.bg-grid-slate-100 {
  background-image: 
    linear-gradient(to right, rgb(241 245 249 / 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgb(241 245 249 / 0.1) 1px, transparent 1px);
}

.bg-grid-slate-700-25 {
  background-image: 
    linear-gradient(to right, rgb(51 65 85 / 0.25) 1px, transparent 1px),
    linear-gradient(to bottom, rgb(51 65 85 / 0.25) 1px, transparent 1px);
}

/* ========================================
   ANIMATIONS
   ======================================== */

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-in {
  animation: var(--animate-in);
}

.slide-in-from-top-1 {
  animation: var(--slide-in-from-top-1);
}

.slide-in-from-top-2 {
  animation: var(--slide-in-from-top-2);
}

/* ========================================
   THEME TOGGLE STYLES
   ======================================== */

.theme-toggle-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color);
}

.theme-toggle-btn:hover {
  background-color: var(--secondary-color);
  transform: scale(1.1);
}

.theme-toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ========================================
   COMPONENT STYLES
   ======================================== */

/* Service Items */
.service-item {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 10px var(--shadow-color);
  transition: all 0.3s ease;
}

.service-item:hover {
  background-color: var(--hover-bg);
  transform: translateY(-5px);
  box-shadow: 0 5px 20px var(--shadow-color);
}

/* ========================================
   UTILITY CLASSES
   ======================================== */

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }

.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.flex { display: flex; }
.grid { display: grid; }

/* Additional spacing utilities */
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }

/* Density padding classes with higher specificity to override Bootstrap */
.py-1\.5 { padding-top: 0.375rem !important; padding-bottom: 0.375rem !important; }
.py-3 { padding-top: 0.75rem !important; padding-bottom: 0.75rem !important; }
.py-4\.5 { padding-top: 1.125rem !important; padding-bottom: 1.125rem !important; }

.mx-auto { margin-left: auto; margin-right: auto; }
.my-2 { margin-top: 0.5rem; margin-bottom: 0.5rem; }
.my-4 { margin-top: 1rem; margin-bottom: 1rem; }

/* Width utilities */
.w-full { width: 100%; }
.w-auto { width: auto; }
.h-full { height: 100%; }
.h-auto { height: auto; }

/* Color utilities */
.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-accent { color: var(--accent-color); }
.text-error { color: var(--error-color); }

.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-accent { background-color: var(--accent-color); }
.bg-card { background-color: var(--card-bg); }

.border-primary { border-color: var(--primary-color); }
.border-secondary { border-color: var(--secondary-color); }
.border-accent { border-color: var(--accent-color); }

/* Border radius utilities */
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-full { border-radius: 9999px; }

/* Shadow utilities */
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */

/* Mobile-first responsive utilities */
@media (max-width: 640px) {
  .sm\:hidden { display: none; }
  .sm\:block { display: block; }
  .sm\:flex { display: flex; }
  .sm\:text-sm { font-size: 0.875rem; }
  .sm\:text-lg { font-size: 1.125rem; }
  .sm\:p-2 { padding: 0.5rem; }
  .sm\:p-4 { padding: 1rem; }
}

@media (max-width: 768px) {
  .md\:hidden { display: none; }
  .md\:block { display: block; }
  .md\:flex { display: flex; }
  .md\:text-sm { font-size: 0.875rem; }
  .md\:text-lg { font-size: 1.125rem; }
  .md\:p-2 { padding: 0.5rem; }
  .md\:p-4 { padding: 1rem; }
  
  .container {
    padding: 0 0.5rem;
  }
}

@media (min-width: 1024px) {
  .lg\:hidden { display: none; }
  .lg\:block { display: block; }
  .lg\:flex { display: flex; }
  .lg\:text-lg { font-size: 1.125rem; }
  .lg\:text-xl { font-size: 1.25rem; }
  .lg\:p-4 { padding: 1rem; }
  .lg\:p-6 { padding: 1.5rem; }
}

/* ========================================
   DARK MODE SUPPORT
   ======================================== */

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

.selection\:bg-blue-500 *::-moz-selection {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.selection\:bg-blue-500 *::selection {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.selection\:text-white *::-moz-selection {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.selection\:text-white *::selection {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.selection\:bg-blue-500::-moz-selection {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.selection\:bg-blue-500::selection {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.selection\:text-white::-moz-selection {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.selection\:text-white::selection {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:scale-110:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.02\]:hover {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:transform:hover {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:cursor-pointer:hover {
  cursor: pointer;
}

.hover\:border:hover {
  border-width: 1px;
}

.hover\:border-amber-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(252 211 77 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}

.hover\:border-emerald-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(110 231 183 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}

.hover\:border-green-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));
}

.hover\:border-orange-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}

.hover\:border-orange-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(234 88 12 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));
}

.hover\:border-red-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}

.hover\:border-indigo-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(79 70 229 / var(--tw-border-opacity, 1));
}

.hover\:border-yellow-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(202 138 4 / var(--tw-border-opacity, 1));
}

.hover\:bg-amber-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(253 230 138 / var(--tw-bg-opacity, 1));
}

.hover\:bg-amber-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(180 83 9 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-emerald-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(209 250 229 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50\/50:hover {
  background-color: rgb(249 250 251 / 0.5);
}

.hover\:bg-gray-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(67 56 202 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 215 170 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(194 65 12 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(233 213 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.hover\:bg-white\/10:hover {
  background-color: rgb(255 255 255 / 0.1);
}

.hover\:bg-yellow-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(161 98 7 / var(--tw-bg-opacity, 1));
}

.hover\:from-amber-700:hover {
  --tw-gradient-from: #b45309 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(180 83 9 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-blue-700:hover {
  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-purple-700:hover {
  --tw-gradient-from: #7e22ce var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(126 34 206 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:to-amber-800:hover {
  --tw-gradient-to: #92400e var(--tw-gradient-to-position);
}

.hover\:to-indigo-700:hover {
  --tw-gradient-to: #4338ca var(--tw-gradient-to-position);
}

.hover\:to-purple-700:hover {
  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);
}

.hover\:to-purple-800:hover {
  --tw-gradient-to: #6b21a8 var(--tw-gradient-to-position);
}

.hover\:text-blue-500:hover {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-700:hover {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-800:hover {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-900:hover {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-200:hover {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-500:hover {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.hover\:text-green-800:hover {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}

.hover\:text-green-900:hover {
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-500:hover {
  --tw-text-opacity: 1;
  color: rgb(99 102 241 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-800:hover {
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-900:hover {
  --tw-text-opacity: 1;
  color: rgb(88 28 135 / var(--tw-text-opacity, 1));
}

.hover\:text-red-600:hover {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.hover\:text-red-800:hover {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.hover\:text-red-900:hover {
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-600:hover {
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-700:hover {
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-800:hover {
  --tw-text-opacity: 1;
  color: rgb(30 41 59 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-900:hover {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:text-white\/80:hover {
  color: rgb(255 255 255 / 0.8);
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:opacity-80:hover {
  opacity: 0.8;
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-sm:hover {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:z-10:focus {
  z-index: 10;
}

.focus\:border-amber-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));
}

.focus\:border-blue-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.focus\:border-emerald-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1));
}

.focus\:border-indigo-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
}

.focus\:border-purple-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}

.focus\:border-transparent:focus {
  border-color: transparent;
}

.focus\:bg-gray-100:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.focus\:bg-white:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-inset:focus {
  --tw-ring-inset: inset;
}

.focus\:ring-amber-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(245 158 11 / var(--tw-ring-opacity, 1));
}

.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.focus\:ring-emerald-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(16 185 129 / var(--tw-ring-opacity, 1));
}

.focus\:ring-gray-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(107 114 128 / var(--tw-ring-opacity, 1));
}

.focus\:ring-green-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.focus\:ring-green-600:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(22 163 74 / var(--tw-ring-opacity, 1));
}

.focus\:ring-indigo-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));
}

.focus\:ring-orange-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity, 1));
}

.focus\:ring-purple-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));
}

.focus\:ring-red-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}

.focus\:ring-red-600:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(220 38 38 / var(--tw-ring-opacity, 1));
}

.focus\:ring-yellow-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(234 179 8 / var(--tw-ring-opacity, 1));
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus\:ring-offset-green-50:focus {
  --tw-ring-offset-color: #f0fdf4;
}

.focus\:ring-offset-red-50:focus {
  --tw-ring-offset-color: #fef2f2;
}

.active\:scale-\[0\.98\]:active {
  --tw-scale-x: 0.98;
  --tw-scale-y: 0.98;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:opacity-30:disabled {
  opacity: 0.3;
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.group\/btn:hover .group-hover\/btn\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.group:hover .group-hover\:text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

@media (min-width: 640px) {
  .sm\:mx-6 {
    margin-left: 1.5rem;
    margin-right: 1.5rem;
  }
  .sm\:block {
    display: block;
  }
  .sm\:table-cell {
    display: table-cell;
  }
  .sm\:hidden {
    display: none;
  }
  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .sm\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
  .sm\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 768px) {
  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }
  .md\:ml-6 {
    margin-left: 1.5rem;
  }
  .md\:table-cell {
    display: table-cell;
  }
  .md\:hidden {
    display: none;
  }
  .md\:w-3\/4 {
    width: 75%;
  }
  .md\:min-w-0 {
    min-width: 0px;
  }
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:fixed {
    position: fixed;
  }
  .lg\:inset-y-0 {
    top: 0px;
    bottom: 0px;
  }
  .lg\:mx-8 {
    margin-left: 2rem;
    margin-right: 2rem;
  }
  .lg\:block {
    display: block;
  }
  .lg\:flex {
    display: flex;
  }
  .lg\:table-cell {
    display: table-cell;
  }
  .lg\:hidden {
    display: none;
  }
  .lg\:w-1\/2 {
    width: 50%;
  }
  .lg\:w-16 {
    width: 4rem;
  }
  .lg\:w-64 {
    width: 16rem;
  }
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .lg\:flex-row {
    flex-direction: row;
  }
  .lg\:flex-col {
    flex-direction: column;
  }
  .lg\:items-center {
    align-items: center;
  }
  .lg\:justify-between {
    justify-content: space-between;
  }
  .lg\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }
  .lg\:pl-0 {
    padding-left: 0px;
  }
  .lg\:pl-16 {
    padding-left: 4rem;
  }
  .lg\:pl-64 {
    padding-left: 16rem;
  }
}

@media (min-width: 1280px) {
  .xl\:mx-12 {
    margin-left: 3rem;
    margin-right: 3rem;
  }
  .xl\:table-cell {
    display: table-cell;
  }
  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (min-width: 1536px) {
  .\32xl\:mx-16 {
    margin-left: 4rem;
    margin-right: 4rem;
  }
}

@media (prefers-color-scheme: dark) {
  .dark\:border-red-600 {
    --tw-border-opacity: 1;
    border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
  }
  .dark\:border-red-800\/50 {
    border-color: rgb(153 27 27 / 0.5);
  }
  .dark\:border-slate-600 {
    --tw-border-opacity: 1;
    border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));
  }
  .dark\:border-slate-700 {
    --tw-border-opacity: 1;
    border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));
  }
  .dark\:border-slate-700\/50 {
    border-color: rgb(51 65 85 / 0.5);
  }
  .dark\:bg-blue-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
  }
  .dark\:bg-red-900\/20 {
    background-color: rgb(127 29 29 / 0.2);
  }
  .dark\:bg-slate-700 {
    --tw-bg-opacity: 1;
    background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));
  }
  .dark\:bg-slate-700\/50 {
    background-color: rgb(51 65 85 / 0.5);
  }
  .dark\:bg-slate-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));
  }
  .dark\:bg-slate-800\/80 {
    background-color: rgb(30 41 59 / 0.8);
  }
  .dark\:from-slate-900 {
    --tw-gradient-from: #0f172a var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(15 23 42 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }
  .dark\:via-slate-800 {
    --tw-gradient-to: rgb(30 41 59 / 0)  var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), #1e293b var(--tw-gradient-via-position), var(--tw-gradient-to);
  }
  .dark\:via-slate-900\/50 {
    --tw-gradient-to: rgb(15 23 42 / 0)  var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), rgb(15 23 42 / 0.5) var(--tw-gradient-via-position), var(--tw-gradient-to);
  }
  .dark\:to-slate-900 {
    --tw-gradient-to: #0f172a var(--tw-gradient-to-position);
  }
  .dark\:text-blue-400 {
    --tw-text-opacity: 1;
    color: rgb(96 165 250 / var(--tw-text-opacity, 1));
  }
  .dark\:text-red-200 {
    --tw-text-opacity: 1;
    color: rgb(254 202 202 / var(--tw-text-opacity, 1));
  }
  .dark\:text-red-400 {
    --tw-text-opacity: 1;
    color: rgb(248 113 113 / var(--tw-text-opacity, 1));
  }
  .dark\:text-slate-300 {
    --tw-text-opacity: 1;
    color: rgb(203 213 225 / var(--tw-text-opacity, 1));
  }
  .dark\:text-slate-400 {
    --tw-text-opacity: 1;
    color: rgb(148 163 184 / var(--tw-text-opacity, 1));
  }
  .dark\:text-slate-600 {
    --tw-text-opacity: 1;
    color: rgb(71 85 105 / var(--tw-text-opacity, 1));
  }
  .dark\:text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }
  .dark\:hover\:text-blue-300:hover {
    --tw-text-opacity: 1;
    color: rgb(147 197 253 / var(--tw-text-opacity, 1));
  }
  .dark\:hover\:text-slate-200:hover {
    --tw-text-opacity: 1;
    color: rgb(226 232 240 / var(--tw-text-opacity, 1));
  }
  .dark\:hover\:text-slate-300:hover {
    --tw-text-opacity: 1;
    color: rgb(203 213 225 / var(--tw-text-opacity, 1));
  }
}

