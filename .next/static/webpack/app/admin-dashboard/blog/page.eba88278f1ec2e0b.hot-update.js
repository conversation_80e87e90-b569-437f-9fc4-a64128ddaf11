"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/blog/page",{

/***/ "(app-pages-browser)/./src/components/admin/blog/blogs-management.tsx":
/*!********************************************************!*\
  !*** ./src/components/admin/blog/blogs-management.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlogsManagement: () => (/* binding */ BlogsManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EllipsisVerticalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PowerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentDuplicateIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArchiveBoxIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js\");\n/* harmony import */ var _blog_modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./blog-modal */ \"(app-pages-browser)/./src/components/admin/blog/blog-modal.tsx\");\n/* harmony import */ var _components_providers_notification_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/notification-provider */ \"(app-pages-browser)/./src/components/providers/notification-provider.tsx\");\n/* harmony import */ var _shared_confirmation_modal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/confirmation-modal */ \"(app-pages-browser)/./src/components/admin/shared/confirmation-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ BlogsManagement auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nfunction BlogAvatar(param) {\n    let { title, featuredImageUrl, size = 'md', className = '', style = {} } = param;\n    _s();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [imageLoading, setImageLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // Size configurations\n    const sizeClasses = {\n        xs: 'w-6 h-6',\n        sm: 'w-8 h-8',\n        md: 'w-12 h-12',\n        lg: 'w-16 h-16',\n        xl: 'w-24 h-24',\n        'full-height': 'w-full h-full'\n    };\n    const iconSizes = {\n        xs: 'w-3 h-3',\n        sm: 'w-4 h-4',\n        md: 'w-6 h-6',\n        lg: 'w-8 h-8',\n        xl: 'w-12 h-12',\n        'full-height': 'w-16 h-16'\n    };\n    const textSizes = {\n        xs: 'text-xs',\n        sm: 'text-sm',\n        md: 'text-base',\n        lg: 'text-lg',\n        xl: 'text-xl',\n        'full-height': 'text-4xl'\n    };\n    // Generate initials from blog title\n    const getInitials = (title)=>{\n        return title.split(' ').map((word)=>word.charAt(0)).join('').toUpperCase().slice(0, 2);\n    };\n    // Generate a consistent color based on the blog title\n    const getBackgroundColor = (title)=>{\n        const colors = [\n            'bg-blue-500',\n            'bg-green-500',\n            'bg-purple-500',\n            'bg-pink-500',\n            'bg-indigo-500',\n            'bg-yellow-500',\n            'bg-red-500',\n            'bg-teal-500',\n            'bg-orange-500',\n            'bg-cyan-500'\n        ];\n        let hash = 0;\n        for(let i = 0; i < title.length; i++){\n            hash = title.charCodeAt(i) + ((hash << 5) - hash);\n        }\n        return colors[Math.abs(hash) % colors.length];\n    };\n    const handleImageLoad = ()=>{\n        setImageLoading(false);\n        setImageError(false);\n    };\n    const handleImageError = ()=>{\n        setImageLoading(false);\n        setImageError(true);\n    };\n    const baseClasses = \"\\n    \".concat(sizeClasses[size], \" \\n    rounded-lg \\n    flex \\n    items-center \\n    justify-center \\n    overflow-hidden \\n    \").concat(size === 'full-height' ? 'min-h-[320px]' : '', \"\\n    \").concat(className, \"\\n  \");\n    // If we have a valid featured image URL and no error, show the image\n    if (featuredImageUrl && !imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(baseClasses, \" bg-gray-100 relative\"),\n            style: style,\n            children: [\n                imageLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 w-6 h-6\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: featuredImageUrl,\n                    alt: \"\".concat(title, \" featured image\"),\n                    className: \"\\n            \".concat(size === 'full-height' ? 'w-full h-full object-cover' : 'w-full h-full object-cover', \"\\n            \").concat(imageLoading ? 'opacity-0' : 'opacity-100', \"\\n            transition-opacity duration-200\\n          \"),\n                    onLoad: handleImageLoad,\n                    onError: handleImageError\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 283,\n            columnNumber: 7\n        }, this);\n    }\n    // Fallback: Show initials with colored background\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\\n        \".concat(baseClasses, \" \\n        \").concat(getBackgroundColor(title), \" \\n        text-white \\n        font-semibold \\n        \").concat(textSizes[size], \"\\n        shadow-sm\\n      \"),\n        style: style,\n        title: title,\n        children: size === 'full-height' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-24 h-24 text-white opacity-80\"\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: getInitials(title)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm opacity-90 break-words line-clamp-3\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 322,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            children: getInitials(title)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 330,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n        lineNumber: 309,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogAvatar, \"yA6MC4/13YXgE42AlKw5vrWMK58=\");\n_c = BlogAvatar;\nfunction BlogsManagement(param) {\n    let { config } = param;\n    var _config_defaultSort, _config_defaultSort1, _config_defaultViewSettings, _config_defaultViewSettings1, _config_defaultViewSettings2, _config_defaultViewSettings3, _config_defaultViewSettings4, _config_defaultViewSettings5, _config_filters, _config_filters1, _config_permissions, _deleteConfirmation_bulkPosts, _deleteConfirmation_bulkPosts1, _deleteConfirmation_post;\n    _s1();\n    // Notification system\n    const { showSuccess, showError, showWarning, showInfo, showLoading, clearLoadingNotifications } = (0,_components_providers_notification_provider__WEBPACK_IMPORTED_MODULE_4__.useNotifications)();\n    // State management\n    const [blogPosts, setBlogPosts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [debouncedSearchQuery, setDebouncedSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultSort = config.defaultSort) === null || _config_defaultSort === void 0 ? void 0 : _config_defaultSort.field) || 'updatedAt');\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultSort1 = config.defaultSort) === null || _config_defaultSort1 === void 0 ? void 0 : _config_defaultSort1.direction) === 'asc' ? 'asc' : 'desc');\n    const [selectedPosts, setSelectedPosts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultViewSettings = config.defaultViewSettings) === null || _config_defaultViewSettings === void 0 ? void 0 : _config_defaultViewSettings.mode) === 'list' || ((_config_defaultViewSettings1 = config.defaultViewSettings) === null || _config_defaultViewSettings1 === void 0 ? void 0 : _config_defaultViewSettings1.mode) === 'grid' ? config.defaultViewSettings.mode : 'list');\n    const [density, setDensity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultViewSettings2 = config.defaultViewSettings) === null || _config_defaultViewSettings2 === void 0 ? void 0 : _config_defaultViewSettings2.density) === 'compact' || ((_config_defaultViewSettings3 = config.defaultViewSettings) === null || _config_defaultViewSettings3 === void 0 ? void 0 : _config_defaultViewSettings3.density) === 'comfortable' || ((_config_defaultViewSettings4 = config.defaultViewSettings) === null || _config_defaultViewSettings4 === void 0 ? void 0 : _config_defaultViewSettings4.density) === 'spacious' ? config.defaultViewSettings.density : 'comfortable');\n    const [activeActionMenu, setActiveActionMenu] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [visibleColumns, setVisibleColumns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(Array.isArray((_config_defaultViewSettings5 = config.defaultViewSettings) === null || _config_defaultViewSettings5 === void 0 ? void 0 : _config_defaultViewSettings5.visibleColumns) ? config.defaultViewSettings.visibleColumns : []);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showColumnSelector, setShowColumnSelector] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showWindowList, setShowWindowList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [gridColumns, setGridColumns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(3);\n    // Modal states\n    const [isCreateModalOpen, setIsCreateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isEditModalOpen, setIsEditModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [editingPost, setEditingPost] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Confirmation modal state\n    const [deleteConfirmation, setDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        isOpen: false,\n        post: null,\n        isBulkDelete: false,\n        bulkPosts: []\n    });\n    // Debounce search query\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BlogsManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"BlogsManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchQuery(searchQuery);\n                    setCurrentPage(1); // Reset to first page when searching\n                }\n            }[\"BlogsManagement.useEffect.timer\"], 300);\n            return ({\n                \"BlogsManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"BlogsManagement.useEffect\"];\n        }\n    }[\"BlogsManagement.useEffect\"], [\n        searchQuery\n    ]);\n    // Handle clicking outside to close action menu\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BlogsManagement.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"BlogsManagement.useEffect.handleClickOutside\": (event)=>{\n                    if (activeActionMenu) {\n                        const target = event.target;\n                        const actionMenu = document.querySelector('[data-post-id=\"'.concat(activeActionMenu, '\"] .action-menu'));\n                        const actionButton = document.querySelector('[data-post-id=\"'.concat(activeActionMenu, '\"] .action-button'));\n                        if (actionMenu && actionButton && !actionMenu.contains(target) && !actionButton.contains(target)) {\n                            setActiveActionMenu(null);\n                            // Hide the action menu\n                            const actionMenuElement = actionMenu;\n                            actionMenuElement.style.opacity = '0';\n                            actionMenuElement.style.transform = 'translateX(100%)';\n                            actionMenuElement.style.pointerEvents = 'none';\n                        }\n                    }\n                }\n            }[\"BlogsManagement.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"BlogsManagement.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"BlogsManagement.useEffect\"];\n        }\n    }[\"BlogsManagement.useEffect\"], [\n        activeActionMenu\n    ]);\n    // Fetch blog posts\n    const fetchBlogPosts = async function() {\n        let preserveFocus = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        setLoading(true);\n        if (!preserveFocus) {\n            showLoading('Loading Blog Posts', 'Retrieving blog posts...');\n        }\n        try {\n            const params = new URLSearchParams({\n                page: currentPage.toString(),\n                limit: (config.pageSize || 10).toString(),\n                search: debouncedSearchQuery,\n                sortBy,\n                sortOrder\n            });\n            // Add filters to params\n            Object.entries(filters).forEach((param)=>{\n                let [key, value] = param;\n                if (value) {\n                    params.append(key, value);\n                }\n            });\n            console.log('Fetching blog posts with params:', params.toString()); // Debug log\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"?\").concat(params));\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('API Error Response:', errorText);\n                throw new Error(\"HTTP error! status: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const data = await response.json();\n            console.log('Received blog posts data:', data); // Debug log\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to fetch blog posts');\n            }\n            setBlogPosts(data.posts || []);\n            setTotalPages(Math.ceil((data.totalCount || 0) / (config.pageSize || 10)));\n            setError(null); // Clear any previous errors on successful fetch\n            if (!preserveFocus) {\n                var _data_posts, _data_posts1;\n                showSuccess('Blog Posts Loaded', \"Loaded \".concat(((_data_posts = data.posts) === null || _data_posts === void 0 ? void 0 : _data_posts.length) || 0, \" blog post\").concat(((_data_posts1 = data.posts) === null || _data_posts1 === void 0 ? void 0 : _data_posts1.length) === 1 ? '' : 's'));\n            }\n        } catch (err) {\n            console.error('Error fetching blog posts:', err); // Debug log\n            setError(err instanceof Error ? err.message : 'Failed to fetch blog posts');\n            if (!preserveFocus) {\n                showError('Failed to Load Blog Posts', 'Unable to retrieve blog posts');\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BlogsManagement.useEffect\": ()=>{\n            // Preserve focus when searching\n            const isSearching = debouncedSearchQuery !== '';\n            fetchBlogPosts(isSearching);\n        }\n    }[\"BlogsManagement.useEffect\"], [\n        currentPage,\n        debouncedSearchQuery,\n        sortBy,\n        sortOrder,\n        filters\n    ]);\n    // Handle create\n    const handleCreate = async (formData)=>{\n        try {\n            showLoading('Creating Blog Post', 'Saving new blog post...');\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to create blog post (\".concat(response.status, \")\"));\n            }\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to create blog post');\n            }\n            setIsCreateModalOpen(false);\n            fetchBlogPosts();\n            showSuccess('Blog Post Created', '\"'.concat(formData.title || 'New blog post', '\" created successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to create blog post';\n            setError(errorMessage);\n            showError('Failed to Create Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Handle add button click\n    const handleAddClick = ()=>{\n        showInfo('Opening Create Form', 'Preparing to create a new blog post...');\n        setIsCreateModalOpen(true);\n    };\n    // Handle update\n    const handleUpdate = async (id, formData)=>{\n        try {\n            showLoading('Updating Blog Post', 'Saving changes...');\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to update blog post (\".concat(response.status, \")\"));\n            }\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to update blog post');\n            }\n            setIsEditModalOpen(false);\n            setEditingPost(null);\n            fetchBlogPosts();\n            showSuccess('Blog Post Updated', '\"'.concat(formData.title || 'Blog post', '\" updated successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to update blog post';\n            setError(errorMessage);\n            showError('Failed to Update Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Handle delete\n    const handleDelete = async (id)=>{\n        try {\n            const postToDelete = blogPosts.find((post)=>post.id === id);\n            showLoading('Deleting Blog Post', 'Removing \"'.concat((postToDelete === null || postToDelete === void 0 ? void 0 : postToDelete.title) || 'blog post', '\"...'));\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete blog post');\n            fetchBlogPosts();\n            showSuccess('Blog Post Deleted', '\"'.concat((postToDelete === null || postToDelete === void 0 ? void 0 : postToDelete.title) || 'Blog post', '\" deleted successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to delete blog post';\n            setError(errorMessage);\n            showError('Failed to Delete Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Show delete confirmation\n    const showDeleteConfirmation = (post)=>{\n        setDeleteConfirmation({\n            isOpen: true,\n            post,\n            isBulkDelete: false,\n            bulkPosts: []\n        });\n    };\n    // Show bulk delete confirmation\n    const showBulkDeleteConfirmation = ()=>{\n        const postsToDelete = blogPosts.filter((post)=>selectedPosts.includes(post.id));\n        setDeleteConfirmation({\n            isOpen: true,\n            post: null,\n            isBulkDelete: true,\n            bulkPosts: postsToDelete\n        });\n    };\n    // Confirm delete\n    const confirmDelete = async ()=>{\n        try {\n            if (deleteConfirmation.isBulkDelete && deleteConfirmation.bulkPosts) {\n                // Bulk delete\n                const promises = deleteConfirmation.bulkPosts.map((post)=>fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(post.id), {\n                        method: 'DELETE'\n                    }));\n                await Promise.all(promises);\n                setSelectedPosts([]);\n                fetchBlogPosts();\n                showSuccess('Blog Posts Deleted', \"\".concat(deleteConfirmation.bulkPosts.length, \" blog post(s) deleted successfully!\"));\n            } else if (deleteConfirmation.post) {\n                // Single delete\n                await handleDelete(deleteConfirmation.post.id);\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to delete blog post(s)';\n            showError('Failed to Delete Blog Post(s)', errorMessage);\n        } finally{\n            setDeleteConfirmation({\n                isOpen: false,\n                post: null,\n                isBulkDelete: false,\n                bulkPosts: []\n            });\n        }\n    };\n    // Cancel delete\n    const cancelDelete = ()=>{\n        setDeleteConfirmation({\n            isOpen: false,\n            post: null,\n            isBulkDelete: false,\n            bulkPosts: []\n        });\n    };\n    // Handle bulk actions\n    const handleBulkAction = async (action)=>{\n        if (selectedPosts.length === 0) return;\n        setActionLoading(action);\n        try {\n            if (action === 'delete') {\n                showBulkDeleteConfirmation();\n                return;\n            }\n            showLoading(\"Bulk \".concat(action), \"Processing \".concat(selectedPosts.length, \" blog post\").concat(selectedPosts.length === 1 ? '' : 's', \"...\"));\n            const promises = selectedPosts.map(async (id)=>{\n                switch(action){\n                    case 'publish':\n                        return fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                            method: 'PUT',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                isPublished: true\n                            })\n                        });\n                    case 'unpublish':\n                        return fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                            method: 'PUT',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                isPublished: false\n                            })\n                        });\n                    default:\n                        throw new Error(\"Unknown bulk action: \".concat(action));\n                }\n            });\n            await Promise.all(promises);\n            setSelectedPosts([]);\n            fetchBlogPosts();\n            showSuccess(\"Bulk \".concat(action, \" completed\"), \"\".concat(selectedPosts.length, \" blog post\").concat(selectedPosts.length === 1 ? '' : 's', \" \").concat(action, \"ed successfully!\"));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"Failed to \".concat(action, \" blog posts\");\n            setError(errorMessage);\n            showError(\"Failed to \".concat(action, \" blog posts\"), errorMessage);\n        } finally{\n            setActionLoading(null);\n        }\n    };\n    // Handle individual actions\n    const handleAction = async (action, item)=>{\n        setActionLoading(\"\".concat(action, \"-\").concat(item.id));\n        try {\n            switch(action){\n                case 'edit':\n                    showInfo('Opening Editor', 'Editing \"'.concat(item.title, '\"'));\n                    setEditingPost(item);\n                    setIsEditModalOpen(true);\n                    break;\n                case 'view':\n                    showInfo('Opening View', 'Viewing \"'.concat(item.title, '\"'));\n                    // TODO: Implement view functionality\n                    showInfo('View Blog Post', 'Opening \"'.concat(item.title, '\" in new tab'));\n                    break;\n                case 'delete':\n                    showInfo('Delete Confirmation', 'Preparing to delete \"'.concat(item.title, '\"'));\n                    showDeleteConfirmation(item);\n                    break;\n                case 'toggle-published':\n                    const newStatus = !item.isPublished;\n                    showLoading(newStatus ? 'Publishing Blog Post' : 'Unpublishing Blog Post', \"\".concat(newStatus ? 'Publishing' : 'Unpublishing', ' \"').concat(item.title, '\"...'));\n                    await handleUpdate(item.id, {\n                        isPublished: newStatus\n                    });\n                    showSuccess(newStatus ? 'Blog Post Published' : 'Blog Post Unpublished', '\"'.concat(item.title, '\" ').concat(newStatus ? 'published' : 'unpublished', \" successfully!\"));\n                    break;\n                case 'duplicate':\n                    showLoading('Duplicating Blog Post', 'Creating copy of \"'.concat(item.title, '\"...'));\n                    // TODO: Implement duplicate functionality\n                    showSuccess('Blog Post Duplicated', '\"'.concat(item.title, '\" duplicated successfully!'));\n                    break;\n                case 'archive':\n                    showLoading('Archiving Blog Post', 'Archiving \"'.concat(item.title, '\"...'));\n                    // TODO: Implement archive functionality\n                    showSuccess('Blog Post Archived', '\"'.concat(item.title, '\" archived successfully!'));\n                    break;\n                default:\n                    console.warn(\"Unknown action: \".concat(action));\n            }\n        } finally{\n            setActionLoading(null);\n        }\n    };\n    // Handle sorting\n    const handleSort = (field)=>{\n        const newOrder = sortBy === field ? sortOrder === 'asc' ? 'desc' : 'asc' : 'asc';\n        const newField = sortBy === field ? field : field;\n        setSortBy(newField);\n        setSortOrder(newOrder);\n        setCurrentPage(1);\n        showInfo('Sorting Blog Posts', \"Sorting by \".concat(field, \" (\").concat(newOrder, \")\"));\n    };\n    // Handle selection\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedPosts(blogPosts.map((post)=>post.id));\n            showInfo('Selection Updated', \"Selected all \".concat(blogPosts.length, \" blog post\").concat(blogPosts.length === 1 ? '' : 's'));\n        } else {\n            setSelectedPosts([]);\n            showInfo('Selection Cleared', 'Deselected all blog posts');\n        }\n    };\n    const handleSelectPost = (id, checked)=>{\n        if (checked) {\n            setSelectedPosts([\n                ...selectedPosts,\n                id\n            ]);\n            const post = blogPosts.find((p)=>p.id === id);\n            showInfo('Post Selected', '\"'.concat((post === null || post === void 0 ? void 0 : post.title) || 'Blog post', '\" added to selection'));\n        } else {\n            setSelectedPosts(selectedPosts.filter((postId)=>postId !== id));\n            const post = blogPosts.find((p)=>p.id === id);\n            showInfo('Post Deselected', '\"'.concat((post === null || post === void 0 ? void 0 : post.title) || 'Blog post', '\" removed from selection'));\n        }\n    };\n    // Get visible fields for table\n    const getVisibleFields = ()=>{\n        if (visibleColumns.length > 0) {\n            var _config_fields;\n            return ((_config_fields = config.fields) === null || _config_fields === void 0 ? void 0 : _config_fields.filter((field)=>visibleColumns.includes(field.key))) || [];\n        }\n        return config.fields || [];\n    };\n    // Handle view mode change\n    const handleViewModeChange = (mode)=>{\n        setViewMode(mode);\n        showInfo('View Mode Changed', \"Switched to \".concat(mode, \" view\"));\n    };\n    // Handle density change\n    const handleDensityChange = (newDensity)=>{\n        setDensity(newDensity);\n        showInfo('Density Updated', \"Changed to \".concat(newDensity, \" density\"));\n    };\n    // Handle grid columns change\n    const handleGridColumnsChange = (columns)=>{\n        setGridColumns(columns);\n        showInfo('Grid Layout Updated', \"Changed to \".concat(columns, \" column\").concat(columns === 1 ? '' : 's', \" layout\"));\n    };\n    // Handle filter changes\n    const handleFilterChange = (key, value)=>{\n        const newFilters = {\n            ...filters\n        };\n        if (value) {\n            newFilters[key] = value;\n        } else {\n            delete newFilters[key];\n        }\n        setFilters(newFilters);\n        showInfo('Filter Applied', \"Filter updated: \".concat(key, \" = \").concat(value || 'all'));\n    };\n    // Close all action menus\n    const closeAllActionMenus = ()=>{\n        setActiveActionMenu(null);\n        document.querySelectorAll('.action-menu').forEach((menu)=>{\n            const menuElement = menu;\n            menuElement.style.opacity = '0';\n            menuElement.style.transform = 'translateX(100%)';\n            menuElement.style.pointerEvents = 'none';\n        });\n    };\n    // Grid Card Component\n    const GridCard = (param)=>{\n        let { post } = param;\n        const isSelected = selectedPosts.includes(post.id);\n        const classes = getGridDensityClasses();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            \"data-post-id\": post.id,\n            className: \"group relative bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 \".concat(isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : '', \" \").concat(classes.card),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"aspect-video bg-gray-100 rounded-t-lg overflow-hidden\",\n                    children: post.featuredImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: post.featuredImageUrl,\n                        alt: post.title,\n                        className: \"w-full h-full object-cover\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 851,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-12 h-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 858,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 857,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 849,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: classes.content,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"\".concat(classes.title, \" text-gray-900 line-clamp-2\"),\n                            children: post.title\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 866,\n                            columnNumber: 11\n                        }, this),\n                        post.excerpt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"\".concat(classes.excerpt, \" text-gray-600 line-clamp-3\"),\n                            children: post.excerpt\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 872,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: classes.meta,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex \".concat(classes.status, \" font-semibold rounded-full \").concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                            children: post.isPublished ? 'Published' : 'Draft'\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 881,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"\".concat(classes.date, \" text-gray-500\"),\n                                            children: formatDate(post.updatedAt)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 888,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 880,\n                                    columnNumber: 17\n                                }, this),\n                                post.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap \".concat(classes.categories),\n                                    children: post.categories.split(',').slice(0, 2).map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex \".concat(classes.category, \" bg-blue-100 text-blue-800 rounded\"),\n                                            children: category.trim()\n                                        }, index, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 897,\n                                            columnNumber: 23\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 895,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 878,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between \".concat(density === 'compact' ? 'mt-3 pt-2' : density === 'spacious' ? 'mt-5 pt-4' : 'mt-4 pt-3', \" border-t border-gray-200\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: [\n                                        \"Updated \",\n                                        formatDate(post.updatedAt)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 907,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"action-button p-2 text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors\",\n                                        onClick: (e)=>{\n                                            var _e_currentTarget_closest;\n                                            e.stopPropagation();\n                                            const actionMenu = (_e_currentTarget_closest = e.currentTarget.closest('[data-post-id]')) === null || _e_currentTarget_closest === void 0 ? void 0 : _e_currentTarget_closest.querySelector('.action-menu');\n                                            if (actionMenu) {\n                                                const isVisible = activeActionMenu === post.id;\n                                                if (isVisible) {\n                                                    // Hide menu\n                                                    setActiveActionMenu(null);\n                                                    actionMenu.style.opacity = '0';\n                                                    actionMenu.style.transform = 'translateX(100%)';\n                                                    actionMenu.style.pointerEvents = 'none';\n                                                } else {\n                                                    // Show menu and hide any other open menus\n                                                    setActiveActionMenu(post.id);\n                                                    actionMenu.style.opacity = '1';\n                                                    actionMenu.style.transform = 'translateX(0)';\n                                                    actionMenu.style.pointerEvents = 'auto';\n                                                }\n                                            }\n                                        },\n                                        title: \"Show Actions\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 937,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 913,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 912,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 906,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"action-menu absolute bg-white rounded-lg border-2 border-red-500 shadow-lg flex flex-col items-center justify-center transition-all duration-200 z-50 \".concat(density === 'compact' ? 'top-1 right-1 bottom-1 w-12 space-y-1.5 px-1' : density === 'spacious' ? 'top-3 right-3 bottom-3 w-16 space-y-3 px-2' : 'top-2 right-2 bottom-2 w-14 space-y-2 px-1.5'),\n                            style: {\n                                opacity: '0',\n                                transform: 'translateX(100%)',\n                                pointerEvents: 'none'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAction('edit', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 border border-blue-500 hover:border-blue-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: \"Edit Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 965,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 955,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAction('view', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-indigo-600 hover:bg-indigo-700 border border-indigo-500 hover:border-indigo-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: \"View Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 979,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 969,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAction('toggle-published', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center \".concat(post.isPublished ? 'bg-orange-600 hover:bg-orange-700 border-orange-500 hover:border-orange-600' : 'bg-green-600 hover:bg-green-700 border-green-500 hover:border-green-600', \" border text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \").concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: post.isPublished ? 'Unpublish Blog Post' : 'Publish Blog Post',\n                                    children: post.isPublished ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 998,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1000,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 983,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAction('duplicate', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-purple-600 hover:bg-purple-700 border border-purple-500 hover:border-purple-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: \"Duplicate Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1015,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1005,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAction('archive', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-yellow-600 hover:bg-yellow-700 border border-yellow-500 hover:border-yellow-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: \"Archive Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1029,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1019,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAction('delete', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-red-600 hover:bg-red-700 border border-red-500 hover:border-red-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: \"Delete Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1043,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1033,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 943,\n                            columnNumber: 11\n                        }, this),\n                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 left-2 \".concat(classes.checkbox),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                checked: isSelected,\n                                onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white \".concat(classes.checkbox),\n                                style: {\n                                    backgroundColor: 'white'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1051,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1050,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 864,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 842,\n            columnNumber: 7\n        }, this);\n    };\n    // Grid density styling helper\n    const getGridDensityClasses = ()=>{\n        const baseClasses = {\n            container: {\n                compact: 'p-1',\n                comfortable: 'p-3',\n                spacious: 'p-4'\n            },\n            grid: {\n                compact: 'gap-2',\n                comfortable: 'gap-3',\n                spacious: 'gap-4'\n            },\n            card: {\n                compact: 'p-1',\n                comfortable: 'p-2',\n                spacious: 'p-3'\n            },\n            content: {\n                compact: 'p-1',\n                comfortable: 'p-2',\n                spacious: 'p-3'\n            },\n            title: {\n                compact: 'text-sm font-semibold mb-0.5',\n                comfortable: 'text-lg font-semibold mb-1',\n                spacious: 'text-xl font-semibold mb-2'\n            },\n            excerpt: {\n                compact: 'text-xs mb-1',\n                comfortable: 'text-sm mb-1.5',\n                spacious: 'text-base mb-2'\n            },\n            meta: {\n                compact: 'space-y-0.5 mb-1',\n                comfortable: 'space-y-1 mb-2',\n                spacious: 'space-y-2 mb-3'\n            },\n            status: {\n                compact: 'px-1 py-0.5 text-xs',\n                comfortable: 'px-1.5 py-0.5 text-xs',\n                spacious: 'px-2 py-1 text-sm'\n            },\n            date: {\n                compact: 'text-xs',\n                comfortable: 'text-xs',\n                spacious: 'text-sm'\n            },\n            categories: {\n                compact: 'gap-0.5',\n                comfortable: 'gap-0.5',\n                spacious: 'gap-1'\n            },\n            category: {\n                compact: 'px-1 py-0.5 text-xs',\n                comfortable: 'px-1.5 py-0.5 text-xs',\n                spacious: 'px-2 py-1 text-sm'\n            },\n            actions: {\n                compact: 'pt-1',\n                comfortable: 'pt-1.5',\n                spacious: 'pt-2'\n            },\n            buttons: {\n                compact: 'space-x-0.5',\n                comfortable: 'space-x-1',\n                spacious: 'space-x-2'\n            },\n            button: {\n                compact: 'p-0.5',\n                comfortable: 'p-1',\n                spacious: 'p-1.5'\n            },\n            icon: {\n                compact: 'w-2.5 h-2.5',\n                comfortable: 'w-3 h-3',\n                spacious: 'w-4 h-4'\n            },\n            checkbox: {\n                compact: 'h-2.5 w-2.5',\n                comfortable: 'h-3 w-3',\n                spacious: 'h-4 w-4'\n            }\n        };\n        return {\n            container: baseClasses.container[density],\n            grid: baseClasses.grid[density],\n            card: baseClasses.card[density],\n            content: baseClasses.content[density],\n            title: baseClasses.title[density],\n            excerpt: baseClasses.excerpt[density],\n            meta: baseClasses.meta[density],\n            status: baseClasses.status[density],\n            date: baseClasses.date[density],\n            categories: baseClasses.categories[density],\n            category: baseClasses.category[density],\n            actions: baseClasses.actions[density],\n            buttons: baseClasses.buttons[density],\n            button: baseClasses.button[density],\n            icon: baseClasses.icon[density],\n            checkbox: baseClasses.checkbox[density]\n        };\n    };\n    // Format date\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString();\n    };\n    // Truncate text\n    const truncateText = (text, maxLength)=>{\n        if (text.length <= maxLength) return text;\n        return text.substring(0, maxLength) + '...';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-a2cd0907138c409\" + \" \" + \"h-full flex flex-col space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"a2cd0907138c409\",\n                children: \".action-menu{opacity:0;transform:translatex(100%);pointer-events:none;transition:all.2s ease-in-out}.group:hover .action-menu{opacity:1;transform:translatex(0);pointer-events:auto}.group:hover .action-menu *{pointer-events:auto}@media(max-width:1024px){.group:hover .action-menu{opacity:0;transform:translatex(100%);pointer-events:none}}.density-compact th,.density-compact td{padding-top:4px!important;padding-bottom:4px!important}.density-comfortable th,.density-comfortable td{padding-top:16px!important;padding-bottom:16px!important}.density-spacious th,.density-spacious td{padding-top:32px!important;padding-bottom:32px!important}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-a2cd0907138c409\" + \" \" + \"relative rounded-lg shadow-sm border border-gray-200/50 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-a2cd0907138c409\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-indigo-50/20\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 1224,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-a2cd0907138c409\" + \" \" + \"relative p-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-14 w-14 text-lime-600 -mt-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1229,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a2cd0907138c409\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"text-3xl font-bold text-gray-900 mt-2\",\n                                                    children: \"Blog Management\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1231,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"text-sm font-medium text-gray-600\",\n                                                    children: \"Create, edit, and manage your blog content.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1234,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1230,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1228,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"hidden lg:flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAddClick,\n                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors shadow-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1245,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Add Blog Post\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1241,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1240,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1227,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 1226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 1223,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                role: \"main\",\n                \"aria-label\": \"Blog management section\",\n                className: \"jsx-a2cd0907138c409\" + \" \" + \"flex-1 rounded-lg shadow-sm border border-gray-200/50 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        paddingBottom: '0'\n                    },\n                    className: \"jsx-a2cd0907138c409\" + \" \" + \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-a2cd0907138c409\" + \" \" + \"space-y-3 p-2 bg-white rounded-lg border border-gray-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"flex flex-col space-y-3 lg:hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1268,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"Search blog posts...\",\n                                                        value: searchQuery,\n                                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"pl-10 pr-4 py-2.5 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1269,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1267,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1266,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-0.5 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleViewModeChange('list'),\n                                                            title: \"List view\",\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 \".concat(viewMode === 'list' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1292,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"text-xs font-medium hidden xs:inline\",\n                                                                    children: \"List\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1293,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1283,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleViewModeChange('grid'),\n                                                            title: \"Grid view\",\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 \".concat(viewMode === 'grid' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1304,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"text-xs font-medium hidden xs:inline\",\n                                                                    children: \"Grid\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1305,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1295,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1282,\n                                                    columnNumber: 15\n                                                }, this),\n                                                viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-0.5 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"text-xs font-medium text-gray-700 px-1\",\n                                                            children: \"Col:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1312,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center gap-0.5 flex-1\",\n                                                            children: [\n                                                                1,\n                                                                2,\n                                                                3,\n                                                                4\n                                                            ].map((num)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(num),\n                                                                    title: \"\".concat(num, \" column\").concat(num > 1 ? 's' : ''),\n                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"flex-1 px-1.5 py-1 rounded text-xs font-medium \".concat(gridColumns === num ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: num\n                                                                }, num, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1315,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1313,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1311,\n                                                    columnNumber: 17\n                                                }, this),\n                                                viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowColumnSelector(!showColumnSelector),\n                                                        title: \"Columns\",\n                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1340,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"hidden xs:inline\",\n                                                                children: \"Col\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1341,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-3 w-3 ml-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1342,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1335,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1334,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowFilters(!showFilters),\n                                                        title: \"Filters\",\n                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium rounded-lg border \".concat(showFilters || Object.keys(filters).some((key)=>filters[key]) ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1358,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"hidden xs:inline\",\n                                                                children: \"Filter\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1359,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            Object.keys(filters).some((key)=>filters[key]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"ml-0.5 inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                children: Object.values(filters).filter(Boolean).length\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1361,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1349,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1348,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowWindowList(!showWindowList),\n                                                        title: \"Density\",\n                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1375,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"hidden xs:inline\",\n                                                                children: density.charAt(0).toUpperCase() + density.slice(1)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1376,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-3 w-3 ml-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1377,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1370,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1369,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1280,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1264,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"hidden lg:flex items-center justify-between gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center gap-3 flex-1 max-w-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"relative flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1389,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"Search blog posts by title, content, excerpt...\",\n                                                            value: searchQuery,\n                                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1390,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1388,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowFilters(!showFilters),\n                                                            title: \"Show/hide filters\",\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(showFilters || Object.keys(filters).some((key)=>filters[key]) ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1410,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Filters\",\n                                                                Object.keys(filters).some((key)=>filters[key]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                    children: Object.values(filters).filter(Boolean).length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1413,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1417,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1401,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"absolute top-full right-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center justify-between mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                                                children: \"Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1425,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>setShowFilters(false),\n                                                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"text-gray-400 hover:text-gray-600\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1430,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1426,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1424,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"space-y-4\",\n                                                                        children: (_config_filters = config.filters) === null || _config_filters === void 0 ? void 0 : _config_filters.map((filter)=>{\n                                                                            var _filter_options;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-a2cd0907138c409\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"block text-xs font-medium text-gray-700 mb-2\",\n                                                                                        children: filter.label\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1437,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                        value: filters[filter.key] || '',\n                                                                                        onChange: (e)=>{\n                                                                                            const newFilters = {\n                                                                                                ...filters\n                                                                                            };\n                                                                                            if (e.target.value) {\n                                                                                                newFilters[filter.key] = e.target.value;\n                                                                                            } else {\n                                                                                                delete newFilters[filter.key];\n                                                                                            }\n                                                                                            setFilters(newFilters);\n                                                                                        },\n                                                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                                                        children: (_filter_options = filter.options) === null || _filter_options === void 0 ? void 0 : _filter_options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: option.value,\n                                                                                                className: \"jsx-a2cd0907138c409\",\n                                                                                                children: option.label\n                                                                                            }, option.value, false, {\n                                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                                lineNumber: 1454,\n                                                                                                columnNumber: 37\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1440,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, filter.key, true, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1436,\n                                                                                columnNumber: 31\n                                                                            }, this);\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1434,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"flex justify-end mt-4 pt-4 border-t border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                setFilters({});\n                                                                                setShowFilters(false);\n                                                                            },\n                                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg\",\n                                                                            children: \"Clear All\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1464,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1463,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1423,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1422,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1400,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1387,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"text-sm font-medium text-gray-700\",\n                                                            children: \"View:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1484,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-1 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleViewModeChange('list'),\n                                                                    title: \"List view\",\n                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"px-3 py-2 rounded-md flex items-center gap-2 \".concat(viewMode === 'list' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-5 w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1495,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"text-sm font-medium\",\n                                                                            children: \"List\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1496,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1486,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleViewModeChange('grid'),\n                                                                    title: \"Grid view\",\n                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"px-3 py-2 rounded-md flex items-center gap-2 \".concat(viewMode === 'grid' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-5 w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1507,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"text-sm font-medium\",\n                                                                            children: \"Grid\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1508,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1498,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1485,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1483,\n                                                    columnNumber: 19\n                                                }, this),\n                                                viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"text-sm font-medium text-gray-700\",\n                                                            children: \"Columns:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1516,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-1 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(1),\n                                                                    title: \"1 column\",\n                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 1 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1518,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(2),\n                                                                    title: \"2 columns\",\n                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 2 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1529,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(3),\n                                                                    title: \"3 columns\",\n                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 3 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1540,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(4),\n                                                                    title: \"4 columns\",\n                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 4 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1551,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1517,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1515,\n                                                    columnNumber: 21\n                                                }, this),\n                                                viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowColumnSelector(!showColumnSelector),\n                                                            title: \"Select columns to display\",\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1574,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Columns\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1576,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1569,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        showColumnSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"text-xs font-medium text-gray-500 uppercase tracking-wide mb-2\",\n                                                                        children: \"Show Columns\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1583,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    config.columns.map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center space-x-2 py-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: visibleColumns.includes(column.key),\n                                                                                    onChange: (e)=>{\n                                                                                        if (e.target.checked) {\n                                                                                            setVisibleColumns((prev)=>[\n                                                                                                    ...prev,\n                                                                                                    column.key\n                                                                                                ]);\n                                                                                        } else {\n                                                                                            setVisibleColumns((prev)=>prev.filter((col)=>col !== column.key));\n                                                                                        }\n                                                                                    },\n                                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1586,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"text-sm text-gray-700\",\n                                                                                    children: column.label\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1598,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, column.key, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1585,\n                                                                            columnNumber: 31\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1582,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1581,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1568,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowWindowList(!showWindowList),\n                                                            title: \"Select density\",\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1616,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                density.charAt(0).toUpperCase() + density.slice(1),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1618,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1611,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showWindowList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"absolute top-full left-0 mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"p-1\",\n                                                                children: [\n                                                                    'compact',\n                                                                    'comfortable',\n                                                                    'spacious'\n                                                                ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            handleDensityChange(option);\n                                                                            setShowWindowList(false);\n                                                                        },\n                                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 \".concat(density === option ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'),\n                                                                        children: option.charAt(0).toUpperCase() + option.slice(1)\n                                                                    }, option, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1626,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1624,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1623,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1610,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1481,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1385,\n                                    columnNumber: 15\n                                }, this),\n                                showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                    children: \"Filters\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1652,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowFilters(false),\n                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"text-gray-400 hover:text-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1657,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1653,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1651,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"space-y-4\",\n                                            children: (_config_filters1 = config.filters) === null || _config_filters1 === void 0 ? void 0 : _config_filters1.map((filter)=>{\n                                                var _filter_options;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a2cd0907138c409\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"block text-xs font-medium text-gray-700 mb-2\",\n                                                            children: filter.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1664,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: filters[filter.key] || '',\n                                                            onChange: (e)=>{\n                                                                const newFilters = {\n                                                                    ...filters\n                                                                };\n                                                                if (e.target.value) {\n                                                                    newFilters[filter.key] = e.target.value;\n                                                                } else {\n                                                                    delete newFilters[filter.key];\n                                                                }\n                                                                setFilters(newFilters);\n                                                            },\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                            children: (_filter_options = filter.options) === null || _filter_options === void 0 ? void 0 : _filter_options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: option.value,\n                                                                    className: \"jsx-a2cd0907138c409\",\n                                                                    children: option.label\n                                                                }, option.value, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1681,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1667,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, filter.key, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1663,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1661,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"flex justify-end mt-4 pt-4 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setFilters({});\n                                                    setShowFilters(false);\n                                                },\n                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg\",\n                                                children: \"Clear All\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1691,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1690,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1650,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1262,\n                            columnNumber: 11\n                        }, this),\n                        config.enableBulkActions && selectedPosts.length > 0 && (viewMode === 'list' || viewMode === 'grid') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-a2cd0907138c409\" + \" \" + \"bg-blue-50 border border-blue-200 rounded-lg px-4 py-2 shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"text-blue-600 font-semibold text-xs\",\n                                                            children: selectedPosts.length\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1712,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1711,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"text-xs font-medium text-blue-900\",\n                                                        children: [\n                                                            \"blog post\",\n                                                            selectedPosts.length === 1 ? '' : 's',\n                                                            \" selected\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1716,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1710,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center space-x-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleBulkAction('publish'),\n                                                        title: \"Publish selected blog posts\",\n                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 border border-green-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1728,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Publish\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1723,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleBulkAction('unpublish'),\n                                                        title: \"Unpublish selected blog posts\",\n                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1737,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Unpublish\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1732,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>showBulkDeleteConfirmation(),\n                                                        title: \"Delete selected blog posts\",\n                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 border border-red-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1746,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Delete\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1741,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1722,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1709,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center space-x-1.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedPosts([]),\n                                            title: \"Clear selection\",\n                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gray-500 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1759,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Clear\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1754,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1753,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1708,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1707,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-a2cd0907138c409\" + \" \" + \"px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-a2cd0907138c409\" + \" \" + \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-5 w-5 text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1773,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1772,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"text-sm font-medium text-red-800\",\n                                                    children: \"Error\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1776,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"mt-2 text-sm text-red-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-a2cd0907138c409\",\n                                                        children: error\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1778,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1777,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setError(null);\n                                                            fetchBlogPosts();\n                                                        },\n                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200\",\n                                                        children: \"Try Again\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1781,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1780,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1775,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1771,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1770,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1769,\n                            columnNumber: 15\n                        }, this),\n                        loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-a2cd0907138c409\" + \" \" + \"px-6 py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1801,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"ml-3 text-gray-600\",\n                                        children: \"Loading blog posts...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1802,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1800,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1799,\n                            columnNumber: 15\n                        }, this),\n                        !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-a2cd0907138c409\",\n                            children: blogPosts.length === 0 ? /* Empty State */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-a2cd0907138c409\" + \" \" + \"p-12 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"mx-auto h-12 w-12 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1813,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"mt-2 text-sm font-medium text-gray-900\",\n                                        children: \"No blog posts found\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1814,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"mt-1 text-sm text-gray-500\",\n                                        children: debouncedSearchQuery ? 'Try adjusting your search terms.' : 'Get started by creating your first blog post.'\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1815,\n                                        columnNumber: 21\n                                    }, this),\n                                    ((_config_permissions = config.permissions) === null || _config_permissions === void 0 ? void 0 : _config_permissions.create) && !debouncedSearchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleAddClick,\n                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: \"Add Blog Post\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1820,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1819,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1812,\n                                columnNumber: 19\n                            }, this) : viewMode === 'list' ? /* Table View */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-a2cd0907138c409\" + \" \" + \"overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"min-w-full divide-y divide-gray-200 density-\".concat(density),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"bg-gray-200 border-b border-gray-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"jsx-a2cd0907138c409\",\n                                                    children: [\n                                                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            scope: \"col\",\n                                                            style: {\n                                                                width: '6px'\n                                                            },\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"relative pl-2 py-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: selectedPosts.length === blogPosts.length && blogPosts.length > 0,\n                                                                onChange: (e)=>handleSelectAll(e.target.checked),\n                                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"text-blue-600 focus:ring-blue-500 border-gray-300 rounded \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-5 w-5' : 'h-4 w-4')\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1839,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1838,\n                                                            columnNumber: 31\n                                                        }, this),\n                                                        getVisibleFields().map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                scope: \"col\",\n                                                                onClick: ()=>handleSort(field.key),\n                                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"px-6 py-2 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 text-xs\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-a2cd0907138c409\",\n                                                                            children: field.label\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1859,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        sortBy === field.key ? sortOrder === 'asc' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-black\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1862,\n                                                                            columnNumber: 39\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-black\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1864,\n                                                                            columnNumber: 39\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1867,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1858,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, field.key, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1852,\n                                                                columnNumber: 31\n                                                            }, this)),\n                                                        config.actions && config.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            scope: \"col\",\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"px-6 py-2 text-right font-medium text-gray-900 uppercase tracking-wider text-xs min-w-[120px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-a2cd0907138c409\",\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1876,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1875,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1835,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1834,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"bg-white divide-y divide-gray-200\",\n                                                children: blogPosts.map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"hover:bg-gray-50 \".concat(selectedPosts.includes(post.id) ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''),\n                                                        children: [\n                                                            config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"px-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: selectedPosts.includes(post.id),\n                                                                    onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                                                    style: {\n                                                                        backgroundColor: 'white'\n                                                                    },\n                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1889,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1888,\n                                                                columnNumber: 33\n                                                            }, this),\n                                                            getVisibleFields().map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"px-6 whitespace-nowrap\",\n                                                                    children: field.key === 'title' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BlogAvatar, {\n                                                                                title: post.title,\n                                                                                featuredImageUrl: post.featuredImageUrl,\n                                                                                size: \"sm\",\n                                                                                className: \"mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1904,\n                                                                                columnNumber: 39\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-a2cd0907138c409\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                                                        children: post.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1911,\n                                                                                        columnNumber: 41\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"text-sm text-gray-500\",\n                                                                                        children: post.slug\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1912,\n                                                                                        columnNumber: 41\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1910,\n                                                                                columnNumber: 39\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1903,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'isPublished' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                        children: post.isPublished ? 'Published' : 'Draft'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1916,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'excerpt' || field.key === 'content' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        title: post[field.key],\n                                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"text-sm text-gray-900 max-w-xs truncate\",\n                                                                        children: truncateText(post[field.key] || '', 50)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1924,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'tags' || field.key === 'categories' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"text-sm text-gray-900\",\n                                                                        children: post[field.key] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"flex flex-wrap gap-1\",\n                                                                            children: [\n                                                                                post[field.key].split(',').slice(0, 2).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\",\n                                                                                        children: tag.trim()\n                                                                                    }, index, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1932,\n                                                                                        columnNumber: 45\n                                                                                    }, this)),\n                                                                                post[field.key].split(',').length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"text-xs text-gray-500\",\n                                                                                    children: [\n                                                                                        \"+\",\n                                                                                        post[field.key].split(',').length - 2,\n                                                                                        \" more\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1937,\n                                                                                    columnNumber: 45\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1930,\n                                                                            columnNumber: 41\n                                                                        }, this) : '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1928,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'createdAt' || field.key === 'updatedAt' || field.key === 'publishedAt' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"text-sm text-gray-500\",\n                                                                        children: post[field.key] ? formatDate(post[field.key]) : '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1943,\n                                                                        columnNumber: 37\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"text-sm text-gray-900\",\n                                                                        children: post[field.key] || '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1947,\n                                                                        columnNumber: 37\n                                                                    }, this)\n                                                                }, field.key, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1901,\n                                                                    columnNumber: 33\n                                                                }, this)),\n                                                            config.actions && config.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"px-6 whitespace-nowrap text-right text-sm font-medium\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center space-x-2\",\n                                                                    children: config.actions.map((action)=>{\n                                                                        const isLoading = actionLoading === \"\".concat(action.action, \"-\").concat(post.id);\n                                                                        const IconComponent = action.icon === 'EyeIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"] : action.icon === 'PencilIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"] : action.icon === 'PowerIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"] : action.icon === 'TrashIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"] : _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleAction(action.action, post),\n                                                                            disabled: isLoading,\n                                                                            title: action.tooltip,\n                                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"p-1 rounded-md transition-colors \".concat(action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' : action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' : action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' : action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' : 'text-gray-600 hover:bg-gray-50', \" \").concat(isLoading ? 'opacity-50 cursor-not-allowed' : ''),\n                                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"animate-spin rounded-full h-4 w-4 border-b-2 border-current\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1980,\n                                                                                columnNumber: 45\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1982,\n                                                                                columnNumber: 45\n                                                                            }, this)\n                                                                        }, action.action, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1966,\n                                                                            columnNumber: 41\n                                                                        }, this);\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1957,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1956,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        ]\n                                                    }, post.id, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1883,\n                                                        columnNumber: 29\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1881,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1833,\n                                        columnNumber: 23\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1832,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1831,\n                                columnNumber: 19\n                            }, this) : viewMode === 'grid' ? /* Grid View - Restructured */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-a2cd0907138c409\" + \" \" + (getGridDensityClasses().container || \"\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"grid \".concat(getGridDensityClasses().grid, \" \").concat(gridColumns === 1 ? 'grid-cols-1' : gridColumns === 2 ? 'grid-cols-1 md:grid-cols-2' : gridColumns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'),\n                                    children: blogPosts.map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridCard, {\n                                            post: post\n                                        }, post.id, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 2006,\n                                            columnNumber: 25\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1999,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1998,\n                                columnNumber: 19\n                            }, this) : /* Card View */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-a2cd0907138c409\" + \" \" + \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"space-y-4\",\n                                    children: blogPosts.map((post)=>{\n                                        var _config_actions;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow \".concat(selectedPosts.includes(post.id) ? 'ring-2 ring-blue-500 bg-blue-50' : ''),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"w-48 h-32 bg-gray-100 rounded-l-lg overflow-hidden flex-shrink-0\",\n                                                        children: post.featuredImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: post.featuredImageUrl,\n                                                            alt: post.title,\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"w-full h-full object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 2022,\n                                                            columnNumber: 33\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"w-full h-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-8 h-8 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 2029,\n                                                                columnNumber: 35\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 2028,\n                                                            columnNumber: 33\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 2020,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-a2cd0907138c409\" + \" \" + \"flex-1 p-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-start justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"text-lg font-semibold text-gray-900 mb-1\",\n                                                                            children: post.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2039,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"text-sm text-gray-500 mb-2\",\n                                                                            children: post.slug\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2042,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        post.excerpt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"text-sm text-gray-600 mb-3 line-clamp-2\",\n                                                                            children: post.excerpt\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2048,\n                                                                            columnNumber: 37\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center space-x-4 text-sm text-gray-500\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                                    children: post.isPublished ? 'Published' : 'Draft'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2055,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-a2cd0907138c409\",\n                                                                                    children: [\n                                                                                        \"Updated: \",\n                                                                                        formatDate(post.updatedAt)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2062,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                post.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-a2cd0907138c409\",\n                                                                                    children: [\n                                                                                        \"Category: \",\n                                                                                        post.categories.split(',')[0].trim()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2064,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2054,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 2037,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"flex items-center space-x-2 ml-4\",\n                                                                    children: [\n                                                                        (_config_actions = config.actions) === null || _config_actions === void 0 ? void 0 : _config_actions.map((action)=>{\n                                                                            const isLoading = actionLoading === \"\".concat(action.action, \"-\").concat(post.id);\n                                                                            const IconComponent = action.icon === 'EyeIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"] : action.icon === 'PencilIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"] : action.icon === 'PowerIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"] : action.icon === 'TrashIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"] : _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleAction(action.action, post),\n                                                                                disabled: isLoading,\n                                                                                title: action.tooltip,\n                                                                                className: \"jsx-a2cd0907138c409\" + \" \" + \"p-2 rounded-md transition-colors \".concat(action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' : action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' : action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' : action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' : 'text-gray-600 hover:bg-gray-50', \" \").concat(isLoading ? 'opacity-50 cursor-not-allowed' : ''),\n                                                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"animate-spin rounded-full h-4 w-4 border-b-2 border-current\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2093,\n                                                                                    columnNumber: 43\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                    className: \"jsx-a2cd0907138c409\" + \" \" + \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2095,\n                                                                                    columnNumber: 43\n                                                                                }, this)\n                                                                            }, action.action, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 2079,\n                                                                                columnNumber: 39\n                                                                            }, this);\n                                                                        }),\n                                                                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: selectedPosts.includes(post.id),\n                                                                            onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                                                            style: {\n                                                                                backgroundColor: 'white'\n                                                                            },\n                                                                            className: \"jsx-a2cd0907138c409\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2103,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 2070,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 2036,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 2035,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 2018,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, post.id, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 2015,\n                                            columnNumber: 25\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 2013,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 2012,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1809,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 1260,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 1255,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_modal__WEBPACK_IMPORTED_MODULE_3__.BlogModal, {\n                isOpen: isCreateModalOpen,\n                onClose: ()=>setIsCreateModalOpen(false),\n                onSubmit: handleCreate,\n                title: \"Create New Blog Post\"\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 2126,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_modal__WEBPACK_IMPORTED_MODULE_3__.BlogModal, {\n                isOpen: isEditModalOpen,\n                onClose: ()=>{\n                    setIsEditModalOpen(false);\n                    setEditingPost(null);\n                },\n                onSubmit: async (formData)=>{\n                    if (editingPost) {\n                        await handleUpdate(editingPost.id, formData);\n                    }\n                },\n                title: \"Edit Blog Post\",\n                initialData: editingPost !== null && editingPost !== void 0 ? editingPost : undefined\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 2133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_confirmation_modal__WEBPACK_IMPORTED_MODULE_5__.ConfirmationModal, {\n                isOpen: deleteConfirmation.isOpen,\n                title: \"Delete Confirmation\",\n                message: \"Are you sure you want to delete this blog post?\",\n                details: (()=>{\n                    if (deleteConfirmation.isBulkDelete) {\n                        var _deleteConfirmation_bulkPosts;\n                        const count = ((_deleteConfirmation_bulkPosts = deleteConfirmation.bulkPosts) === null || _deleteConfirmation_bulkPosts === void 0 ? void 0 : _deleteConfirmation_bulkPosts.length) || 0;\n                        return \"This action will permanently delete \".concat(count, \" blog post\").concat(count === 1 ? '' : 's', \". This cannot be undone.\");\n                    }\n                    const post = deleteConfirmation.post;\n                    return 'This action will permanently delete \"'.concat((post === null || post === void 0 ? void 0 : post.title) || 'this blog post', '\". This cannot be undone.');\n                })(),\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                onConfirm: confirmDelete,\n                onCancel: cancelDelete,\n                type: \"danger\",\n                showVerification: true,\n                verificationData: {\n                    canDelete: true,\n                    reason: deleteConfirmation.isBulkDelete ? \"\".concat(((_deleteConfirmation_bulkPosts = deleteConfirmation.bulkPosts) === null || _deleteConfirmation_bulkPosts === void 0 ? void 0 : _deleteConfirmation_bulkPosts.length) || 0, \" blog post\").concat((((_deleteConfirmation_bulkPosts1 = deleteConfirmation.bulkPosts) === null || _deleteConfirmation_bulkPosts1 === void 0 ? void 0 : _deleteConfirmation_bulkPosts1.length) || 0) === 1 ? '' : 's', \" selected for deletion\") : 'Blog post \"'.concat(((_deleteConfirmation_post = deleteConfirmation.post) === null || _deleteConfirmation_post === void 0 ? void 0 : _deleteConfirmation_post.title) || 'Unknown', '\" ready for deletion')\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 2149,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n        lineNumber: 1182,\n        columnNumber: 5\n    }, this);\n}\n_s1(BlogsManagement, \"QEtb5WNEZkZ0Ju9QpO6VSUTPhK8=\", false, function() {\n    return [\n        _components_providers_notification_provider__WEBPACK_IMPORTED_MODULE_4__.useNotifications\n    ];\n});\n_c1 = BlogsManagement;\nvar _c, _c1;\n$RefreshReg$(_c, \"BlogAvatar\");\n$RefreshReg$(_c1, \"BlogsManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/blog/blogs-management.tsx\n"));

/***/ })

});