"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/blog/page",{

/***/ "(app-pages-browser)/./src/components/admin/blog/blogs-management.tsx":
/*!********************************************************!*\
  !*** ./src/components/admin/blog/blogs-management.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlogsManagement: () => (/* binding */ BlogsManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PowerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentDuplicateIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArchiveBoxIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js\");\n/* harmony import */ var _blog_modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./blog-modal */ \"(app-pages-browser)/./src/components/admin/blog/blog-modal.tsx\");\n/* harmony import */ var _components_providers_notification_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/notification-provider */ \"(app-pages-browser)/./src/components/providers/notification-provider.tsx\");\n/* harmony import */ var _shared_confirmation_modal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/confirmation-modal */ \"(app-pages-browser)/./src/components/admin/shared/confirmation-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ BlogsManagement auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nfunction BlogAvatar(param) {\n    let { title, featuredImageUrl, size = 'md', className = '', style = {} } = param;\n    _s();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [imageLoading, setImageLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // Size configurations\n    const sizeClasses = {\n        xs: 'w-6 h-6',\n        sm: 'w-8 h-8',\n        md: 'w-12 h-12',\n        lg: 'w-16 h-16',\n        xl: 'w-24 h-24',\n        'full-height': 'w-full h-full'\n    };\n    const iconSizes = {\n        xs: 'w-3 h-3',\n        sm: 'w-4 h-4',\n        md: 'w-6 h-6',\n        lg: 'w-8 h-8',\n        xl: 'w-12 h-12',\n        'full-height': 'w-16 h-16'\n    };\n    const textSizes = {\n        xs: 'text-xs',\n        sm: 'text-sm',\n        md: 'text-base',\n        lg: 'text-lg',\n        xl: 'text-xl',\n        'full-height': 'text-4xl'\n    };\n    // Generate initials from blog title\n    const getInitials = (title)=>{\n        return title.split(' ').map((word)=>word.charAt(0)).join('').toUpperCase().slice(0, 2);\n    };\n    // Generate a consistent color based on the blog title\n    const getBackgroundColor = (title)=>{\n        const colors = [\n            'bg-blue-500',\n            'bg-green-500',\n            'bg-purple-500',\n            'bg-pink-500',\n            'bg-indigo-500',\n            'bg-yellow-500',\n            'bg-red-500',\n            'bg-teal-500',\n            'bg-orange-500',\n            'bg-cyan-500'\n        ];\n        let hash = 0;\n        for(let i = 0; i < title.length; i++){\n            hash = title.charCodeAt(i) + ((hash << 5) - hash);\n        }\n        return colors[Math.abs(hash) % colors.length];\n    };\n    const handleImageLoad = ()=>{\n        setImageLoading(false);\n        setImageError(false);\n    };\n    const handleImageError = ()=>{\n        setImageLoading(false);\n        setImageError(true);\n    };\n    const baseClasses = \"\\n    \".concat(sizeClasses[size], \" \\n    rounded-lg \\n    flex \\n    items-center \\n    justify-center \\n    overflow-hidden \\n    \").concat(size === 'full-height' ? 'min-h-[320px]' : '', \"\\n    \").concat(className, \"\\n  \");\n    // If we have a valid featured image URL and no error, show the image\n    if (featuredImageUrl && !imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(baseClasses, \" bg-gray-100 relative\"),\n            style: style,\n            children: [\n                imageLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 w-6 h-6\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: featuredImageUrl,\n                    alt: \"\".concat(title, \" featured image\"),\n                    className: \"\\n            \".concat(size === 'full-height' ? 'w-full h-full object-cover' : 'w-full h-full object-cover', \"\\n            \").concat(imageLoading ? 'opacity-0' : 'opacity-100', \"\\n            transition-opacity duration-200\\n          \"),\n                    onLoad: handleImageLoad,\n                    onError: handleImageError\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 283,\n            columnNumber: 7\n        }, this);\n    }\n    // Fallback: Show initials with colored background\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\\n        \".concat(baseClasses, \" \\n        \").concat(getBackgroundColor(title), \" \\n        text-white \\n        font-semibold \\n        \").concat(textSizes[size], \"\\n        shadow-sm\\n      \"),\n        style: style,\n        title: title,\n        children: size === 'full-height' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-24 h-24 text-white opacity-80\"\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: getInitials(title)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm opacity-90 break-words line-clamp-3\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 322,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            children: getInitials(title)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 330,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n        lineNumber: 309,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogAvatar, \"yA6MC4/13YXgE42AlKw5vrWMK58=\");\n_c = BlogAvatar;\nfunction BlogsManagement(param) {\n    let { config } = param;\n    var _config_defaultSort, _config_defaultSort1, _config_defaultViewSettings, _config_defaultViewSettings1, _config_defaultViewSettings2, _config_defaultViewSettings3, _config_defaultViewSettings4, _config_defaultViewSettings5, _config_filters, _config_filters1, _config_permissions, _deleteConfirmation_bulkPosts, _deleteConfirmation_bulkPosts1, _deleteConfirmation_post;\n    _s1();\n    // Notification system\n    const { showSuccess, showError, showWarning, showInfo, showLoading, clearLoadingNotifications } = (0,_components_providers_notification_provider__WEBPACK_IMPORTED_MODULE_4__.useNotifications)();\n    // State management\n    const [blogPosts, setBlogPosts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [debouncedSearchQuery, setDebouncedSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultSort = config.defaultSort) === null || _config_defaultSort === void 0 ? void 0 : _config_defaultSort.field) || 'updatedAt');\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultSort1 = config.defaultSort) === null || _config_defaultSort1 === void 0 ? void 0 : _config_defaultSort1.direction) === 'asc' ? 'asc' : 'desc');\n    const [selectedPosts, setSelectedPosts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultViewSettings = config.defaultViewSettings) === null || _config_defaultViewSettings === void 0 ? void 0 : _config_defaultViewSettings.mode) === 'list' || ((_config_defaultViewSettings1 = config.defaultViewSettings) === null || _config_defaultViewSettings1 === void 0 ? void 0 : _config_defaultViewSettings1.mode) === 'grid' ? config.defaultViewSettings.mode : 'list');\n    const [density, setDensity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultViewSettings2 = config.defaultViewSettings) === null || _config_defaultViewSettings2 === void 0 ? void 0 : _config_defaultViewSettings2.density) === 'compact' || ((_config_defaultViewSettings3 = config.defaultViewSettings) === null || _config_defaultViewSettings3 === void 0 ? void 0 : _config_defaultViewSettings3.density) === 'comfortable' || ((_config_defaultViewSettings4 = config.defaultViewSettings) === null || _config_defaultViewSettings4 === void 0 ? void 0 : _config_defaultViewSettings4.density) === 'spacious' ? config.defaultViewSettings.density : 'comfortable');\n    const [visibleColumns, setVisibleColumns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(Array.isArray((_config_defaultViewSettings5 = config.defaultViewSettings) === null || _config_defaultViewSettings5 === void 0 ? void 0 : _config_defaultViewSettings5.visibleColumns) ? config.defaultViewSettings.visibleColumns : []);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showColumnSelector, setShowColumnSelector] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showWindowList, setShowWindowList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [gridColumns, setGridColumns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(3);\n    // Modal states\n    const [isCreateModalOpen, setIsCreateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isEditModalOpen, setIsEditModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [editingPost, setEditingPost] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Confirmation modal state\n    const [deleteConfirmation, setDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        isOpen: false,\n        post: null,\n        isBulkDelete: false,\n        bulkPosts: []\n    });\n    // Debounce search query\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BlogsManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"BlogsManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchQuery(searchQuery);\n                    setCurrentPage(1); // Reset to first page when searching\n                }\n            }[\"BlogsManagement.useEffect.timer\"], 300);\n            return ({\n                \"BlogsManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"BlogsManagement.useEffect\"];\n        }\n    }[\"BlogsManagement.useEffect\"], [\n        searchQuery\n    ]);\n    // Fetch blog posts\n    const fetchBlogPosts = async function() {\n        let preserveFocus = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        setLoading(true);\n        if (!preserveFocus) {\n            showLoading('Loading Blog Posts', 'Retrieving blog posts...');\n        }\n        try {\n            const params = new URLSearchParams({\n                page: currentPage.toString(),\n                limit: (config.pageSize || 10).toString(),\n                search: debouncedSearchQuery,\n                sortBy,\n                sortOrder\n            });\n            // Add filters to params\n            Object.entries(filters).forEach((param)=>{\n                let [key, value] = param;\n                if (value) {\n                    params.append(key, value);\n                }\n            });\n            console.log('Fetching blog posts with params:', params.toString()); // Debug log\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"?\").concat(params));\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('API Error Response:', errorText);\n                throw new Error(\"HTTP error! status: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const data = await response.json();\n            console.log('Received blog posts data:', data); // Debug log\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to fetch blog posts');\n            }\n            setBlogPosts(data.posts || []);\n            setTotalPages(Math.ceil((data.totalCount || 0) / (config.pageSize || 10)));\n            setError(null); // Clear any previous errors on successful fetch\n            if (!preserveFocus) {\n                var _data_posts, _data_posts1;\n                showSuccess('Blog Posts Loaded', \"Loaded \".concat(((_data_posts = data.posts) === null || _data_posts === void 0 ? void 0 : _data_posts.length) || 0, \" blog post\").concat(((_data_posts1 = data.posts) === null || _data_posts1 === void 0 ? void 0 : _data_posts1.length) === 1 ? '' : 's'));\n            }\n        } catch (err) {\n            console.error('Error fetching blog posts:', err); // Debug log\n            setError(err instanceof Error ? err.message : 'Failed to fetch blog posts');\n            if (!preserveFocus) {\n                showError('Failed to Load Blog Posts', 'Unable to retrieve blog posts');\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BlogsManagement.useEffect\": ()=>{\n            // Preserve focus when searching\n            const isSearching = debouncedSearchQuery !== '';\n            fetchBlogPosts(isSearching);\n        }\n    }[\"BlogsManagement.useEffect\"], [\n        currentPage,\n        debouncedSearchQuery,\n        sortBy,\n        sortOrder,\n        filters\n    ]);\n    // Handle create\n    const handleCreate = async (formData)=>{\n        try {\n            showLoading('Creating Blog Post', 'Saving new blog post...');\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to create blog post (\".concat(response.status, \")\"));\n            }\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to create blog post');\n            }\n            setIsCreateModalOpen(false);\n            fetchBlogPosts();\n            showSuccess('Blog Post Created', '\"'.concat(formData.title || 'New blog post', '\" created successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to create blog post';\n            setError(errorMessage);\n            showError('Failed to Create Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Handle add button click\n    const handleAddClick = ()=>{\n        showInfo('Opening Create Form', 'Preparing to create a new blog post...');\n        setIsCreateModalOpen(true);\n    };\n    // Handle update\n    const handleUpdate = async (id, formData)=>{\n        try {\n            showLoading('Updating Blog Post', 'Saving changes...');\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to update blog post (\".concat(response.status, \")\"));\n            }\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to update blog post');\n            }\n            setIsEditModalOpen(false);\n            setEditingPost(null);\n            fetchBlogPosts();\n            showSuccess('Blog Post Updated', '\"'.concat(formData.title || 'Blog post', '\" updated successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to update blog post';\n            setError(errorMessage);\n            showError('Failed to Update Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Handle delete\n    const handleDelete = async (id)=>{\n        try {\n            const postToDelete = blogPosts.find((post)=>post.id === id);\n            showLoading('Deleting Blog Post', 'Removing \"'.concat((postToDelete === null || postToDelete === void 0 ? void 0 : postToDelete.title) || 'blog post', '\"...'));\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete blog post');\n            fetchBlogPosts();\n            showSuccess('Blog Post Deleted', '\"'.concat((postToDelete === null || postToDelete === void 0 ? void 0 : postToDelete.title) || 'Blog post', '\" deleted successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to delete blog post';\n            setError(errorMessage);\n            showError('Failed to Delete Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Show delete confirmation\n    const showDeleteConfirmation = (post)=>{\n        setDeleteConfirmation({\n            isOpen: true,\n            post,\n            isBulkDelete: false,\n            bulkPosts: []\n        });\n    };\n    // Show bulk delete confirmation\n    const showBulkDeleteConfirmation = ()=>{\n        const postsToDelete = blogPosts.filter((post)=>selectedPosts.includes(post.id));\n        setDeleteConfirmation({\n            isOpen: true,\n            post: null,\n            isBulkDelete: true,\n            bulkPosts: postsToDelete\n        });\n    };\n    // Confirm delete\n    const confirmDelete = async ()=>{\n        try {\n            if (deleteConfirmation.isBulkDelete && deleteConfirmation.bulkPosts) {\n                // Bulk delete\n                const promises = deleteConfirmation.bulkPosts.map((post)=>fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(post.id), {\n                        method: 'DELETE'\n                    }));\n                await Promise.all(promises);\n                setSelectedPosts([]);\n                fetchBlogPosts();\n                showSuccess('Blog Posts Deleted', \"\".concat(deleteConfirmation.bulkPosts.length, \" blog post(s) deleted successfully!\"));\n            } else if (deleteConfirmation.post) {\n                // Single delete\n                await handleDelete(deleteConfirmation.post.id);\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to delete blog post(s)';\n            showError('Failed to Delete Blog Post(s)', errorMessage);\n        } finally{\n            setDeleteConfirmation({\n                isOpen: false,\n                post: null,\n                isBulkDelete: false,\n                bulkPosts: []\n            });\n        }\n    };\n    // Cancel delete\n    const cancelDelete = ()=>{\n        setDeleteConfirmation({\n            isOpen: false,\n            post: null,\n            isBulkDelete: false,\n            bulkPosts: []\n        });\n    };\n    // Handle bulk actions\n    const handleBulkAction = async (action)=>{\n        if (selectedPosts.length === 0) return;\n        setActionLoading(action);\n        try {\n            if (action === 'delete') {\n                showBulkDeleteConfirmation();\n                return;\n            }\n            showLoading(\"Bulk \".concat(action), \"Processing \".concat(selectedPosts.length, \" blog post\").concat(selectedPosts.length === 1 ? '' : 's', \"...\"));\n            const promises = selectedPosts.map(async (id)=>{\n                switch(action){\n                    case 'publish':\n                        return fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                            method: 'PUT',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                isPublished: true\n                            })\n                        });\n                    case 'unpublish':\n                        return fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                            method: 'PUT',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                isPublished: false\n                            })\n                        });\n                    default:\n                        throw new Error(\"Unknown bulk action: \".concat(action));\n                }\n            });\n            await Promise.all(promises);\n            setSelectedPosts([]);\n            fetchBlogPosts();\n            showSuccess(\"Bulk \".concat(action, \" completed\"), \"\".concat(selectedPosts.length, \" blog post\").concat(selectedPosts.length === 1 ? '' : 's', \" \").concat(action, \"ed successfully!\"));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"Failed to \".concat(action, \" blog posts\");\n            setError(errorMessage);\n            showError(\"Failed to \".concat(action, \" blog posts\"), errorMessage);\n        } finally{\n            setActionLoading(null);\n        }\n    };\n    // Handle individual actions\n    const handleAction = async (action, item)=>{\n        setActionLoading(\"\".concat(action, \"-\").concat(item.id));\n        try {\n            switch(action){\n                case 'edit':\n                    showInfo('Opening Editor', 'Editing \"'.concat(item.title, '\"'));\n                    setEditingPost(item);\n                    setIsEditModalOpen(true);\n                    break;\n                case 'view':\n                    showInfo('Opening View', 'Viewing \"'.concat(item.title, '\"'));\n                    // TODO: Implement view functionality\n                    showInfo('View Blog Post', 'Opening \"'.concat(item.title, '\" in new tab'));\n                    break;\n                case 'delete':\n                    showInfo('Delete Confirmation', 'Preparing to delete \"'.concat(item.title, '\"'));\n                    showDeleteConfirmation(item);\n                    break;\n                case 'toggle-published':\n                    const newStatus = !item.isPublished;\n                    showLoading(newStatus ? 'Publishing Blog Post' : 'Unpublishing Blog Post', \"\".concat(newStatus ? 'Publishing' : 'Unpublishing', ' \"').concat(item.title, '\"...'));\n                    await handleUpdate(item.id, {\n                        isPublished: newStatus\n                    });\n                    showSuccess(newStatus ? 'Blog Post Published' : 'Blog Post Unpublished', '\"'.concat(item.title, '\" ').concat(newStatus ? 'published' : 'unpublished', \" successfully!\"));\n                    break;\n                case 'duplicate':\n                    showLoading('Duplicating Blog Post', 'Creating copy of \"'.concat(item.title, '\"...'));\n                    // TODO: Implement duplicate functionality\n                    showSuccess('Blog Post Duplicated', '\"'.concat(item.title, '\" duplicated successfully!'));\n                    break;\n                case 'archive':\n                    showLoading('Archiving Blog Post', 'Archiving \"'.concat(item.title, '\"...'));\n                    // TODO: Implement archive functionality\n                    showSuccess('Blog Post Archived', '\"'.concat(item.title, '\" archived successfully!'));\n                    break;\n                default:\n                    console.warn(\"Unknown action: \".concat(action));\n            }\n        } finally{\n            setActionLoading(null);\n        }\n    };\n    // Handle sorting\n    const handleSort = (field)=>{\n        const newOrder = sortBy === field ? sortOrder === 'asc' ? 'desc' : 'asc' : 'asc';\n        const newField = sortBy === field ? field : field;\n        setSortBy(newField);\n        setSortOrder(newOrder);\n        setCurrentPage(1);\n        showInfo('Sorting Blog Posts', \"Sorting by \".concat(field, \" (\").concat(newOrder, \")\"));\n    };\n    // Handle selection\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedPosts(blogPosts.map((post)=>post.id));\n            showInfo('Selection Updated', \"Selected all \".concat(blogPosts.length, \" blog post\").concat(blogPosts.length === 1 ? '' : 's'));\n        } else {\n            setSelectedPosts([]);\n            showInfo('Selection Cleared', 'Deselected all blog posts');\n        }\n    };\n    const handleSelectPost = (id, checked)=>{\n        if (checked) {\n            setSelectedPosts([\n                ...selectedPosts,\n                id\n            ]);\n            const post = blogPosts.find((p)=>p.id === id);\n            showInfo('Post Selected', '\"'.concat((post === null || post === void 0 ? void 0 : post.title) || 'Blog post', '\" added to selection'));\n        } else {\n            setSelectedPosts(selectedPosts.filter((postId)=>postId !== id));\n            const post = blogPosts.find((p)=>p.id === id);\n            showInfo('Post Deselected', '\"'.concat((post === null || post === void 0 ? void 0 : post.title) || 'Blog post', '\" removed from selection'));\n        }\n    };\n    // Get visible fields for table\n    const getVisibleFields = ()=>{\n        if (visibleColumns.length > 0) {\n            var _config_fields;\n            return ((_config_fields = config.fields) === null || _config_fields === void 0 ? void 0 : _config_fields.filter((field)=>visibleColumns.includes(field.key))) || [];\n        }\n        return config.fields || [];\n    };\n    // Handle view mode change\n    const handleViewModeChange = (mode)=>{\n        setViewMode(mode);\n        showInfo('View Mode Changed', \"Switched to \".concat(mode, \" view\"));\n    };\n    // Handle density change\n    const handleDensityChange = (newDensity)=>{\n        setDensity(newDensity);\n        showInfo('Density Updated', \"Changed to \".concat(newDensity, \" density\"));\n    };\n    // Handle grid columns change\n    const handleGridColumnsChange = (columns)=>{\n        setGridColumns(columns);\n        showInfo('Grid Layout Updated', \"Changed to \".concat(columns, \" column\").concat(columns === 1 ? '' : 's', \" layout\"));\n    };\n    // Handle filter changes\n    const handleFilterChange = (key, value)=>{\n        const newFilters = {\n            ...filters\n        };\n        if (value) {\n            newFilters[key] = value;\n        } else {\n            delete newFilters[key];\n        }\n        setFilters(newFilters);\n        showInfo('Filter Applied', \"Filter updated: \".concat(key, \" = \").concat(value || 'all'));\n    };\n    // Grid Card Component\n    const GridCard = (param)=>{\n        let { post } = param;\n        const isSelected = selectedPosts.includes(post.id);\n        const classes = getGridDensityClasses();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"group relative bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 \".concat(isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : '', \" \").concat(classes.card),\n            onClick: (e)=>{\n                // Hide action menu when clicking on card (mobile only)\n                if (window.innerWidth <= 1024) {\n                    const actionMenu = e.currentTarget.querySelector('.action-menu');\n                    if (actionMenu && actionMenu.classList.contains('show')) {\n                        actionMenu.classList.remove('show');\n                        actionMenu.style.opacity = '0';\n                        actionMenu.style.transform = 'translateX(100%)';\n                        actionMenu.style.pointerEvents = 'none';\n                    }\n                }\n            },\n            onMouseLeave: (e)=>{\n                // Hide action menu when mouse leaves (all screen sizes)\n                const actionMenu = e.currentTarget.querySelector('.action-menu');\n                if (actionMenu && actionMenu.classList.contains('show')) {\n                    actionMenu.classList.remove('show');\n                    actionMenu.style.opacity = '0';\n                    actionMenu.style.transform = 'translateX(100%)';\n                    actionMenu.style.pointerEvents = 'none';\n                }\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"aspect-video bg-gray-100 rounded-t-lg overflow-hidden\",\n                    children: post.featuredImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: post.featuredImageUrl,\n                        alt: post.title,\n                        className: \"w-full h-full object-cover\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 833,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-12 h-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 840,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 839,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 831,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: classes.content,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"\".concat(classes.title, \" text-gray-900 line-clamp-2\"),\n                            children: post.title\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 848,\n                            columnNumber: 11\n                        }, this),\n                        post.excerpt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"\".concat(classes.excerpt, \" text-gray-600 line-clamp-3\"),\n                            children: post.excerpt\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 854,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: classes.meta,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex \".concat(classes.status, \" font-semibold rounded-full \").concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                            children: post.isPublished ? 'Published' : 'Draft'\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 863,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"\".concat(classes.date, \" text-gray-500\"),\n                                            children: formatDate(post.updatedAt)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 870,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 862,\n                                    columnNumber: 17\n                                }, this),\n                                post.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap \".concat(classes.categories),\n                                    children: post.categories.split(',').slice(0, 2).map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex \".concat(classes.category, \" bg-blue-100 text-blue-800 rounded\"),\n                                            children: category.trim()\n                                        }, index, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 879,\n                                            columnNumber: 23\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 877,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 860,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between \".concat(density === 'compact' ? 'mt-3 pt-2' : density === 'spacious' ? 'mt-5 pt-4' : 'mt-4 pt-3', \" border-t border-gray-200\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: [\n                                        \"Updated \",\n                                        formatDate(post.updatedAt)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 889,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            var _e_currentTarget_closest;\n                                            e.stopPropagation();\n                                            const actionMenu = (_e_currentTarget_closest = e.currentTarget.closest('.group')) === null || _e_currentTarget_closest === void 0 ? void 0 : _e_currentTarget_closest.querySelector('.action-menu');\n                                            if (actionMenu) {\n                                                const isVisible = actionMenu.style.opacity === '1';\n                                                actionMenu.style.opacity = isVisible ? '0' : '1';\n                                                actionMenu.style.transform = isVisible ? 'translateX(100%)' : 'translateX(0)';\n                                                actionMenu.style.pointerEvents = isVisible ? 'none' : 'auto';\n                                            }\n                                        },\n                                        className: \"p-2 text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors\",\n                                        title: \"Show Actions\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-4 w-4\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 910,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 909,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 895,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 894,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 888,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"action-menu absolute bg-white rounded-lg border border-gray-200 flex flex-col items-center justify-center transition-all duration-200 z-10 \".concat(density === 'compact' ? 'top-1 right-1 bottom-1 w-12 space-y-1.5 px-1' : density === 'spacious' ? 'top-3 right-3 bottom-3 w-16 space-y-3 px-2' : 'top-2 right-2 bottom-2 w-14 space-y-2 px-1.5'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAction('edit', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 border border-blue-500 hover:border-blue-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: \"Edit Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 935,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 925,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAction('view', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-indigo-600 hover:bg-indigo-700 border border-indigo-500 hover:border-indigo-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: \"View Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 949,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 939,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAction('toggle-published', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center \".concat(post.isPublished ? 'bg-orange-600 hover:bg-orange-700 border-orange-500 hover:border-orange-600' : 'bg-green-600 hover:bg-green-700 border-green-500 hover:border-green-600', \" border text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \").concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: post.isPublished ? 'Unpublish Blog Post' : 'Publish Blog Post',\n                                    children: post.isPublished ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 968,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 970,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 953,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAction('duplicate', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-purple-600 hover:bg-purple-700 border border-purple-500 hover:border-purple-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: \"Duplicate Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 985,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 975,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAction('archive', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-yellow-600 hover:bg-yellow-700 border border-yellow-500 hover:border-yellow-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: \"Archive Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 999,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 989,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAction('delete', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-red-600 hover:bg-red-700 border border-red-500 hover:border-red-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: \"Delete Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1013,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1003,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 917,\n                            columnNumber: 11\n                        }, this),\n                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 left-2 \".concat(classes.checkbox),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                checked: isSelected,\n                                onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white \".concat(classes.checkbox),\n                                style: {\n                                    backgroundColor: 'white'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1021,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1020,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 846,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 803,\n            columnNumber: 7\n        }, this);\n    };\n    // Grid density styling helper\n    const getGridDensityClasses = ()=>{\n        const baseClasses = {\n            container: {\n                compact: 'p-1',\n                comfortable: 'p-3',\n                spacious: 'p-4'\n            },\n            grid: {\n                compact: 'gap-2',\n                comfortable: 'gap-3',\n                spacious: 'gap-4'\n            },\n            card: {\n                compact: 'p-1',\n                comfortable: 'p-2',\n                spacious: 'p-3'\n            },\n            content: {\n                compact: 'p-1',\n                comfortable: 'p-2',\n                spacious: 'p-3'\n            },\n            title: {\n                compact: 'text-sm font-semibold mb-0.5',\n                comfortable: 'text-lg font-semibold mb-1',\n                spacious: 'text-xl font-semibold mb-2'\n            },\n            excerpt: {\n                compact: 'text-xs mb-1',\n                comfortable: 'text-sm mb-1.5',\n                spacious: 'text-base mb-2'\n            },\n            meta: {\n                compact: 'space-y-0.5 mb-1',\n                comfortable: 'space-y-1 mb-2',\n                spacious: 'space-y-2 mb-3'\n            },\n            status: {\n                compact: 'px-1 py-0.5 text-xs',\n                comfortable: 'px-1.5 py-0.5 text-xs',\n                spacious: 'px-2 py-1 text-sm'\n            },\n            date: {\n                compact: 'text-xs',\n                comfortable: 'text-xs',\n                spacious: 'text-sm'\n            },\n            categories: {\n                compact: 'gap-0.5',\n                comfortable: 'gap-0.5',\n                spacious: 'gap-1'\n            },\n            category: {\n                compact: 'px-1 py-0.5 text-xs',\n                comfortable: 'px-1.5 py-0.5 text-xs',\n                spacious: 'px-2 py-1 text-sm'\n            },\n            actions: {\n                compact: 'pt-1',\n                comfortable: 'pt-1.5',\n                spacious: 'pt-2'\n            },\n            buttons: {\n                compact: 'space-x-0.5',\n                comfortable: 'space-x-1',\n                spacious: 'space-x-2'\n            },\n            button: {\n                compact: 'p-0.5',\n                comfortable: 'p-1',\n                spacious: 'p-1.5'\n            },\n            icon: {\n                compact: 'w-2.5 h-2.5',\n                comfortable: 'w-3 h-3',\n                spacious: 'w-4 h-4'\n            },\n            checkbox: {\n                compact: 'h-2.5 w-2.5',\n                comfortable: 'h-3 w-3',\n                spacious: 'h-4 w-4'\n            }\n        };\n        return {\n            container: baseClasses.container[density],\n            grid: baseClasses.grid[density],\n            card: baseClasses.card[density],\n            content: baseClasses.content[density],\n            title: baseClasses.title[density],\n            excerpt: baseClasses.excerpt[density],\n            meta: baseClasses.meta[density],\n            status: baseClasses.status[density],\n            date: baseClasses.date[density],\n            categories: baseClasses.categories[density],\n            category: baseClasses.category[density],\n            actions: baseClasses.actions[density],\n            buttons: baseClasses.buttons[density],\n            button: baseClasses.button[density],\n            icon: baseClasses.icon[density],\n            checkbox: baseClasses.checkbox[density]\n        };\n    };\n    // Format date\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString();\n    };\n    // Truncate text\n    const truncateText = (text, maxLength)=>{\n        if (text.length <= maxLength) return text;\n        return text.substring(0, maxLength) + '...';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-2687d2e634104ff\" + \" \" + \"h-full flex flex-col space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"2687d2e634104ff\",\n                children: '.action-menu{opacity:0!important;transform:translatex(100%)!important;pointer-events:none!important;transition:all.2s ease-in-out!important}.group:hover .action-menu{opacity:0!important;transform:translatex(100%)!important;pointer-events:none!important}.group:hover .action-menu *{pointer-events:none!important}.action-menu[style*=\"opacity: 1\"]{opacity:1!important;transform:translatex(0)!important;pointer-events:auto!important;display:flex!important}.action-menu[style*=\"opacity: 1\"] *{pointer-events:auto!important}.action-menu.show{opacity:1!important;transform:translatex(0)!important;pointer-events:auto!important;display:flex!important}@media(max-width:1024px){.group:hover .action-menu{opacity:0!important;transform:translatex(100%)!important;pointer-events:none!important}.group:hover .action-menu *{pointer-events:none!important}.action-menu{display:flex!important;opacity:0!important;transform:translatex(100%)!important;pointer-events:none!important;transition:all.2s ease-in-out!important}.action-menu[style*=\"opacity: 1\"]{opacity:1!important;transform:translatex(0)!important;pointer-events:auto!important}}.mobile-three-dot{display:block!important;z-index:30!important}.md\\\\\\\\:hidden{display:block!important}@media(min-width:768px){.md\\\\\\\\:hidden{display:none!important}}.density-compact th,.density-compact td{padding-top:4px!important;padding-bottom:4px!important}.density-comfortable th,.density-comfortable td{padding-top:16px!important;padding-bottom:16px!important}.density-spacious th,.density-spacious td{padding-top:32px!important;padding-bottom:32px!important}'\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2687d2e634104ff\" + \" \" + \"relative rounded-lg shadow-sm border border-gray-200/50 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2687d2e634104ff\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-indigo-50/20\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 1242,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2687d2e634104ff\" + \" \" + \"relative p-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-14 w-14 text-lime-600 -mt-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1247,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2687d2e634104ff\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"text-3xl font-bold text-gray-900 mt-2\",\n                                                    children: \"Blog Management\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1249,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"text-sm font-medium text-gray-600\",\n                                                    children: \"Create, edit, and manage your blog content.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1252,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1248,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"hidden lg:flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAddClick,\n                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors shadow-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1263,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Add Blog Post\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1259,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1258,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1245,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 1244,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 1241,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                role: \"main\",\n                \"aria-label\": \"Blog management section\",\n                className: \"jsx-2687d2e634104ff\" + \" \" + \"flex-1 rounded-lg shadow-sm border border-gray-200/50 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        paddingBottom: '0'\n                    },\n                    className: \"jsx-2687d2e634104ff\" + \" \" + \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2687d2e634104ff\" + \" \" + \"space-y-3 p-2 bg-white rounded-lg border border-gray-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"flex flex-col space-y-3 lg:hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1286,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"Search blog posts...\",\n                                                        value: searchQuery,\n                                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"pl-10 pr-4 py-2.5 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1287,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1285,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1284,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-0.5 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleViewModeChange('list'),\n                                                            title: \"List view\",\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 \".concat(viewMode === 'list' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1310,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"text-xs font-medium hidden xs:inline\",\n                                                                    children: \"List\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1311,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1301,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleViewModeChange('grid'),\n                                                            title: \"Grid view\",\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 \".concat(viewMode === 'grid' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1322,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"text-xs font-medium hidden xs:inline\",\n                                                                    children: \"Grid\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1323,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1313,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1300,\n                                                    columnNumber: 15\n                                                }, this),\n                                                viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-0.5 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"text-xs font-medium text-gray-700 px-1\",\n                                                            children: \"Col:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1330,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center gap-0.5 flex-1\",\n                                                            children: [\n                                                                1,\n                                                                2,\n                                                                3,\n                                                                4\n                                                            ].map((num)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(num),\n                                                                    title: \"\".concat(num, \" column\").concat(num > 1 ? 's' : ''),\n                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"flex-1 px-1.5 py-1 rounded text-xs font-medium \".concat(gridColumns === num ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: num\n                                                                }, num, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1333,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1331,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1329,\n                                                    columnNumber: 17\n                                                }, this),\n                                                viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowColumnSelector(!showColumnSelector),\n                                                        title: \"Columns\",\n                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1358,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"hidden xs:inline\",\n                                                                children: \"Col\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1359,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-3 w-3 ml-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1360,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1353,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1352,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowFilters(!showFilters),\n                                                        title: \"Filters\",\n                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium rounded-lg border \".concat(showFilters || Object.keys(filters).some((key)=>filters[key]) ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1376,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"hidden xs:inline\",\n                                                                children: \"Filter\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1377,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            Object.keys(filters).some((key)=>filters[key]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"ml-0.5 inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                children: Object.values(filters).filter(Boolean).length\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1379,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1367,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1366,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowWindowList(!showWindowList),\n                                                        title: \"Density\",\n                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1393,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"hidden xs:inline\",\n                                                                children: density.charAt(0).toUpperCase() + density.slice(1)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1394,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-3 w-3 ml-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1395,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1388,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1387,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1298,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1282,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"hidden lg:flex items-center justify-between gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center gap-3 flex-1 max-w-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"relative flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1407,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"Search blog posts by title, content, excerpt...\",\n                                                            value: searchQuery,\n                                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1408,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1406,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowFilters(!showFilters),\n                                                            title: \"Show/hide filters\",\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(showFilters || Object.keys(filters).some((key)=>filters[key]) ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1428,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Filters\",\n                                                                Object.keys(filters).some((key)=>filters[key]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                    children: Object.values(filters).filter(Boolean).length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1431,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1435,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1419,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"absolute top-full right-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center justify-between mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                                                children: \"Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1443,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>setShowFilters(false),\n                                                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"text-gray-400 hover:text-gray-600\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1448,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1444,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1442,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"space-y-4\",\n                                                                        children: (_config_filters = config.filters) === null || _config_filters === void 0 ? void 0 : _config_filters.map((filter)=>{\n                                                                            var _filter_options;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2687d2e634104ff\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"block text-xs font-medium text-gray-700 mb-2\",\n                                                                                        children: filter.label\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1455,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                        value: filters[filter.key] || '',\n                                                                                        onChange: (e)=>{\n                                                                                            const newFilters = {\n                                                                                                ...filters\n                                                                                            };\n                                                                                            if (e.target.value) {\n                                                                                                newFilters[filter.key] = e.target.value;\n                                                                                            } else {\n                                                                                                delete newFilters[filter.key];\n                                                                                            }\n                                                                                            setFilters(newFilters);\n                                                                                        },\n                                                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                                                        children: (_filter_options = filter.options) === null || _filter_options === void 0 ? void 0 : _filter_options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: option.value,\n                                                                                                className: \"jsx-2687d2e634104ff\",\n                                                                                                children: option.label\n                                                                                            }, option.value, false, {\n                                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                                lineNumber: 1472,\n                                                                                                columnNumber: 37\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1458,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, filter.key, true, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1454,\n                                                                                columnNumber: 31\n                                                                            }, this);\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1452,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"flex justify-end mt-4 pt-4 border-t border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                setFilters({});\n                                                                                setShowFilters(false);\n                                                                            },\n                                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg\",\n                                                                            children: \"Clear All\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1482,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1481,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1441,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1440,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1418,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1405,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"text-sm font-medium text-gray-700\",\n                                                            children: \"View:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1502,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-1 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleViewModeChange('list'),\n                                                                    title: \"List view\",\n                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"px-3 py-2 rounded-md flex items-center gap-2 \".concat(viewMode === 'list' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-5 w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1513,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"text-sm font-medium\",\n                                                                            children: \"List\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1514,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1504,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleViewModeChange('grid'),\n                                                                    title: \"Grid view\",\n                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"px-3 py-2 rounded-md flex items-center gap-2 \".concat(viewMode === 'grid' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-5 w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1525,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"text-sm font-medium\",\n                                                                            children: \"Grid\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1526,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1516,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1503,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1501,\n                                                    columnNumber: 19\n                                                }, this),\n                                                viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"text-sm font-medium text-gray-700\",\n                                                            children: \"Columns:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1534,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-1 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(1),\n                                                                    title: \"1 column\",\n                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 1 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1536,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(2),\n                                                                    title: \"2 columns\",\n                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 2 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1547,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(3),\n                                                                    title: \"3 columns\",\n                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 3 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1558,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(4),\n                                                                    title: \"4 columns\",\n                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 4 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1569,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1535,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1533,\n                                                    columnNumber: 21\n                                                }, this),\n                                                viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowColumnSelector(!showColumnSelector),\n                                                            title: \"Select columns to display\",\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1592,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Columns\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1594,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1587,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        showColumnSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"text-xs font-medium text-gray-500 uppercase tracking-wide mb-2\",\n                                                                        children: \"Show Columns\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1601,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    config.columns.map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center space-x-2 py-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: visibleColumns.includes(column.key),\n                                                                                    onChange: (e)=>{\n                                                                                        if (e.target.checked) {\n                                                                                            setVisibleColumns((prev)=>[\n                                                                                                    ...prev,\n                                                                                                    column.key\n                                                                                                ]);\n                                                                                        } else {\n                                                                                            setVisibleColumns((prev)=>prev.filter((col)=>col !== column.key));\n                                                                                        }\n                                                                                    },\n                                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1604,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"text-sm text-gray-700\",\n                                                                                    children: column.label\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1616,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, column.key, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1603,\n                                                                            columnNumber: 31\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1600,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1599,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1586,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowWindowList(!showWindowList),\n                                                            title: \"Select density\",\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1634,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                density.charAt(0).toUpperCase() + density.slice(1),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1636,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1629,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showWindowList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"absolute top-full left-0 mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"p-1\",\n                                                                children: [\n                                                                    'compact',\n                                                                    'comfortable',\n                                                                    'spacious'\n                                                                ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            handleDensityChange(option);\n                                                                            setShowWindowList(false);\n                                                                        },\n                                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 \".concat(density === option ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'),\n                                                                        children: option.charAt(0).toUpperCase() + option.slice(1)\n                                                                    }, option, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1644,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1642,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1641,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1628,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1499,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1403,\n                                    columnNumber: 15\n                                }, this),\n                                showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                    children: \"Filters\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1670,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowFilters(false),\n                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"text-gray-400 hover:text-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1675,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1671,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1669,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"space-y-4\",\n                                            children: (_config_filters1 = config.filters) === null || _config_filters1 === void 0 ? void 0 : _config_filters1.map((filter)=>{\n                                                var _filter_options;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2687d2e634104ff\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"block text-xs font-medium text-gray-700 mb-2\",\n                                                            children: filter.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1682,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: filters[filter.key] || '',\n                                                            onChange: (e)=>{\n                                                                const newFilters = {\n                                                                    ...filters\n                                                                };\n                                                                if (e.target.value) {\n                                                                    newFilters[filter.key] = e.target.value;\n                                                                } else {\n                                                                    delete newFilters[filter.key];\n                                                                }\n                                                                setFilters(newFilters);\n                                                            },\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                            children: (_filter_options = filter.options) === null || _filter_options === void 0 ? void 0 : _filter_options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: option.value,\n                                                                    className: \"jsx-2687d2e634104ff\",\n                                                                    children: option.label\n                                                                }, option.value, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1699,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1685,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, filter.key, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1681,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1679,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"flex justify-end mt-4 pt-4 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setFilters({});\n                                                    setShowFilters(false);\n                                                },\n                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg\",\n                                                children: \"Clear All\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1709,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1708,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1668,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1280,\n                            columnNumber: 11\n                        }, this),\n                        config.enableBulkActions && selectedPosts.length > 0 && (viewMode === 'list' || viewMode === 'grid') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2687d2e634104ff\" + \" \" + \"bg-blue-50 border border-blue-200 rounded-lg px-4 py-2 shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"text-blue-600 font-semibold text-xs\",\n                                                            children: selectedPosts.length\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1730,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1729,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"text-xs font-medium text-blue-900\",\n                                                        children: [\n                                                            \"blog post\",\n                                                            selectedPosts.length === 1 ? '' : 's',\n                                                            \" selected\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1734,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1728,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center space-x-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleBulkAction('publish'),\n                                                        title: \"Publish selected blog posts\",\n                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 border border-green-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1746,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Publish\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1741,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleBulkAction('unpublish'),\n                                                        title: \"Unpublish selected blog posts\",\n                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1755,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Unpublish\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1750,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>showBulkDeleteConfirmation(),\n                                                        title: \"Delete selected blog posts\",\n                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 border border-red-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1764,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Delete\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1759,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1740,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1727,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center space-x-1.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedPosts([]),\n                                            title: \"Clear selection\",\n                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gray-500 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1777,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Clear\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1772,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1771,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1726,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1725,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2687d2e634104ff\" + \" \" + \"px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2687d2e634104ff\" + \" \" + \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-5 w-5 text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1791,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1790,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"text-sm font-medium text-red-800\",\n                                                    children: \"Error\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1794,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"mt-2 text-sm text-red-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-2687d2e634104ff\",\n                                                        children: error\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1796,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1795,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setError(null);\n                                                            fetchBlogPosts();\n                                                        },\n                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200\",\n                                                        children: \"Try Again\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1799,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1798,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1793,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1789,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1788,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1787,\n                            columnNumber: 15\n                        }, this),\n                        loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2687d2e634104ff\" + \" \" + \"px-6 py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1819,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"ml-3 text-gray-600\",\n                                        children: \"Loading blog posts...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1820,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1818,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1817,\n                            columnNumber: 15\n                        }, this),\n                        !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2687d2e634104ff\",\n                            children: blogPosts.length === 0 ? /* Empty State */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2687d2e634104ff\" + \" \" + \"p-12 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"mx-auto h-12 w-12 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1831,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"mt-2 text-sm font-medium text-gray-900\",\n                                        children: \"No blog posts found\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1832,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"mt-1 text-sm text-gray-500\",\n                                        children: debouncedSearchQuery ? 'Try adjusting your search terms.' : 'Get started by creating your first blog post.'\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1833,\n                                        columnNumber: 21\n                                    }, this),\n                                    ((_config_permissions = config.permissions) === null || _config_permissions === void 0 ? void 0 : _config_permissions.create) && !debouncedSearchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleAddClick,\n                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: \"Add Blog Post\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1838,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1837,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1830,\n                                columnNumber: 19\n                            }, this) : viewMode === 'list' ? /* Table View */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2687d2e634104ff\" + \" \" + \"overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"min-w-full divide-y divide-gray-200 density-\".concat(density),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"bg-gray-200 border-b border-gray-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"jsx-2687d2e634104ff\",\n                                                    children: [\n                                                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            scope: \"col\",\n                                                            style: {\n                                                                width: '6px'\n                                                            },\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"relative pl-2 py-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: selectedPosts.length === blogPosts.length && blogPosts.length > 0,\n                                                                onChange: (e)=>handleSelectAll(e.target.checked),\n                                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"text-blue-600 focus:ring-blue-500 border-gray-300 rounded \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-5 w-5' : 'h-4 w-4')\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1857,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1856,\n                                                            columnNumber: 31\n                                                        }, this),\n                                                        getVisibleFields().map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                scope: \"col\",\n                                                                onClick: ()=>handleSort(field.key),\n                                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"px-6 py-2 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 text-xs\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-2687d2e634104ff\",\n                                                                            children: field.label\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1877,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        sortBy === field.key ? sortOrder === 'asc' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-black\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1880,\n                                                                            columnNumber: 39\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-black\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1882,\n                                                                            columnNumber: 39\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1885,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1876,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, field.key, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1870,\n                                                                columnNumber: 31\n                                                            }, this)),\n                                                        config.actions && config.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            scope: \"col\",\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"px-6 py-2 text-right font-medium text-gray-900 uppercase tracking-wider text-xs min-w-[120px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-2687d2e634104ff\",\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1894,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1893,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1853,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1852,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"bg-white divide-y divide-gray-200\",\n                                                children: blogPosts.map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"hover:bg-gray-50 \".concat(selectedPosts.includes(post.id) ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''),\n                                                        children: [\n                                                            config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"px-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: selectedPosts.includes(post.id),\n                                                                    onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                                                    style: {\n                                                                        backgroundColor: 'white'\n                                                                    },\n                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1907,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1906,\n                                                                columnNumber: 33\n                                                            }, this),\n                                                            getVisibleFields().map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"px-6 whitespace-nowrap\",\n                                                                    children: field.key === 'title' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BlogAvatar, {\n                                                                                title: post.title,\n                                                                                featuredImageUrl: post.featuredImageUrl,\n                                                                                size: \"sm\",\n                                                                                className: \"mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1922,\n                                                                                columnNumber: 39\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2687d2e634104ff\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                                                        children: post.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1929,\n                                                                                        columnNumber: 41\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"text-sm text-gray-500\",\n                                                                                        children: post.slug\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1930,\n                                                                                        columnNumber: 41\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1928,\n                                                                                columnNumber: 39\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1921,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'isPublished' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                        children: post.isPublished ? 'Published' : 'Draft'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1934,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'excerpt' || field.key === 'content' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        title: post[field.key],\n                                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"text-sm text-gray-900 max-w-xs truncate\",\n                                                                        children: truncateText(post[field.key] || '', 50)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1942,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'tags' || field.key === 'categories' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"text-sm text-gray-900\",\n                                                                        children: post[field.key] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"flex flex-wrap gap-1\",\n                                                                            children: [\n                                                                                post[field.key].split(',').slice(0, 2).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\",\n                                                                                        children: tag.trim()\n                                                                                    }, index, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1950,\n                                                                                        columnNumber: 45\n                                                                                    }, this)),\n                                                                                post[field.key].split(',').length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"text-xs text-gray-500\",\n                                                                                    children: [\n                                                                                        \"+\",\n                                                                                        post[field.key].split(',').length - 2,\n                                                                                        \" more\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1955,\n                                                                                    columnNumber: 45\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1948,\n                                                                            columnNumber: 41\n                                                                        }, this) : '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1946,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'createdAt' || field.key === 'updatedAt' || field.key === 'publishedAt' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"text-sm text-gray-500\",\n                                                                        children: post[field.key] ? formatDate(post[field.key]) : '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1961,\n                                                                        columnNumber: 37\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"text-sm text-gray-900\",\n                                                                        children: post[field.key] || '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1965,\n                                                                        columnNumber: 37\n                                                                    }, this)\n                                                                }, field.key, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1919,\n                                                                    columnNumber: 33\n                                                                }, this)),\n                                                            config.actions && config.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"px-6 whitespace-nowrap text-right text-sm font-medium\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center space-x-2\",\n                                                                    children: config.actions.map((action)=>{\n                                                                        const isLoading = actionLoading === \"\".concat(action.action, \"-\").concat(post.id);\n                                                                        const IconComponent = action.icon === 'EyeIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"] : action.icon === 'PencilIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"] : action.icon === 'PowerIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"] : action.icon === 'TrashIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"] : _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleAction(action.action, post),\n                                                                            disabled: isLoading,\n                                                                            title: action.tooltip,\n                                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"p-1 rounded-md transition-colors \".concat(action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' : action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' : action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' : action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' : 'text-gray-600 hover:bg-gray-50', \" \").concat(isLoading ? 'opacity-50 cursor-not-allowed' : ''),\n                                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"animate-spin rounded-full h-4 w-4 border-b-2 border-current\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1998,\n                                                                                columnNumber: 45\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 2000,\n                                                                                columnNumber: 45\n                                                                            }, this)\n                                                                        }, action.action, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1984,\n                                                                            columnNumber: 41\n                                                                        }, this);\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1975,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1974,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        ]\n                                                    }, post.id, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1901,\n                                                        columnNumber: 29\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1899,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1851,\n                                        columnNumber: 23\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1850,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1849,\n                                columnNumber: 19\n                            }, this) : viewMode === 'grid' ? /* Grid View - Restructured */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2687d2e634104ff\" + \" \" + (getGridDensityClasses().container || \"\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"grid \".concat(getGridDensityClasses().grid, \" \").concat(gridColumns === 1 ? 'grid-cols-1' : gridColumns === 2 ? 'grid-cols-1 md:grid-cols-2' : gridColumns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'),\n                                    children: blogPosts.map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridCard, {\n                                            post: post\n                                        }, post.id, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 2024,\n                                            columnNumber: 25\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 2017,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 2016,\n                                columnNumber: 19\n                            }, this) : /* Card View */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2687d2e634104ff\" + \" \" + \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"space-y-4\",\n                                    children: blogPosts.map((post)=>{\n                                        var _config_actions;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow \".concat(selectedPosts.includes(post.id) ? 'ring-2 ring-blue-500 bg-blue-50' : ''),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"w-48 h-32 bg-gray-100 rounded-l-lg overflow-hidden flex-shrink-0\",\n                                                        children: post.featuredImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: post.featuredImageUrl,\n                                                            alt: post.title,\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"w-full h-full object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 2040,\n                                                            columnNumber: 33\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"w-full h-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-8 h-8 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 2047,\n                                                                columnNumber: 35\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 2046,\n                                                            columnNumber: 33\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 2038,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2687d2e634104ff\" + \" \" + \"flex-1 p-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-start justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"text-lg font-semibold text-gray-900 mb-1\",\n                                                                            children: post.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2057,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"text-sm text-gray-500 mb-2\",\n                                                                            children: post.slug\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2060,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        post.excerpt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"text-sm text-gray-600 mb-3 line-clamp-2\",\n                                                                            children: post.excerpt\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2066,\n                                                                            columnNumber: 37\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center space-x-4 text-sm text-gray-500\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                                    children: post.isPublished ? 'Published' : 'Draft'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2073,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-2687d2e634104ff\",\n                                                                                    children: [\n                                                                                        \"Updated: \",\n                                                                                        formatDate(post.updatedAt)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2080,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                post.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-2687d2e634104ff\",\n                                                                                    children: [\n                                                                                        \"Category: \",\n                                                                                        post.categories.split(',')[0].trim()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2082,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2072,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 2055,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"flex items-center space-x-2 ml-4\",\n                                                                    children: [\n                                                                        (_config_actions = config.actions) === null || _config_actions === void 0 ? void 0 : _config_actions.map((action)=>{\n                                                                            const isLoading = actionLoading === \"\".concat(action.action, \"-\").concat(post.id);\n                                                                            const IconComponent = action.icon === 'EyeIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"] : action.icon === 'PencilIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"] : action.icon === 'PowerIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"] : action.icon === 'TrashIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"] : _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleAction(action.action, post),\n                                                                                disabled: isLoading,\n                                                                                title: action.tooltip,\n                                                                                className: \"jsx-2687d2e634104ff\" + \" \" + \"p-2 rounded-md transition-colors \".concat(action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' : action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' : action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' : action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' : 'text-gray-600 hover:bg-gray-50', \" \").concat(isLoading ? 'opacity-50 cursor-not-allowed' : ''),\n                                                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"animate-spin rounded-full h-4 w-4 border-b-2 border-current\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2111,\n                                                                                    columnNumber: 43\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                    className: \"jsx-2687d2e634104ff\" + \" \" + \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2113,\n                                                                                    columnNumber: 43\n                                                                                }, this)\n                                                                            }, action.action, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 2097,\n                                                                                columnNumber: 39\n                                                                            }, this);\n                                                                        }),\n                                                                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: selectedPosts.includes(post.id),\n                                                                            onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                                                            style: {\n                                                                                backgroundColor: 'white'\n                                                                            },\n                                                                            className: \"jsx-2687d2e634104ff\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2121,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 2088,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 2054,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 2053,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 2036,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, post.id, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 2033,\n                                            columnNumber: 25\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 2031,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 2030,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1827,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 1278,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 1273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_modal__WEBPACK_IMPORTED_MODULE_3__.BlogModal, {\n                isOpen: isCreateModalOpen,\n                onClose: ()=>setIsCreateModalOpen(false),\n                onSubmit: handleCreate,\n                title: \"Create New Blog Post\"\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 2144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_modal__WEBPACK_IMPORTED_MODULE_3__.BlogModal, {\n                isOpen: isEditModalOpen,\n                onClose: ()=>{\n                    setIsEditModalOpen(false);\n                    setEditingPost(null);\n                },\n                onSubmit: async (formData)=>{\n                    if (editingPost) {\n                        await handleUpdate(editingPost.id, formData);\n                    }\n                },\n                title: \"Edit Blog Post\",\n                initialData: editingPost !== null && editingPost !== void 0 ? editingPost : undefined\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 2151,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_confirmation_modal__WEBPACK_IMPORTED_MODULE_5__.ConfirmationModal, {\n                isOpen: deleteConfirmation.isOpen,\n                title: \"Delete Confirmation\",\n                message: \"Are you sure you want to delete this blog post?\",\n                details: (()=>{\n                    if (deleteConfirmation.isBulkDelete) {\n                        var _deleteConfirmation_bulkPosts;\n                        const count = ((_deleteConfirmation_bulkPosts = deleteConfirmation.bulkPosts) === null || _deleteConfirmation_bulkPosts === void 0 ? void 0 : _deleteConfirmation_bulkPosts.length) || 0;\n                        return \"This action will permanently delete \".concat(count, \" blog post\").concat(count === 1 ? '' : 's', \". This cannot be undone.\");\n                    }\n                    const post = deleteConfirmation.post;\n                    return 'This action will permanently delete \"'.concat((post === null || post === void 0 ? void 0 : post.title) || 'this blog post', '\". This cannot be undone.');\n                })(),\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                onConfirm: confirmDelete,\n                onCancel: cancelDelete,\n                type: \"danger\",\n                showVerification: true,\n                verificationData: {\n                    canDelete: true,\n                    reason: deleteConfirmation.isBulkDelete ? \"\".concat(((_deleteConfirmation_bulkPosts = deleteConfirmation.bulkPosts) === null || _deleteConfirmation_bulkPosts === void 0 ? void 0 : _deleteConfirmation_bulkPosts.length) || 0, \" blog post\").concat((((_deleteConfirmation_bulkPosts1 = deleteConfirmation.bulkPosts) === null || _deleteConfirmation_bulkPosts1 === void 0 ? void 0 : _deleteConfirmation_bulkPosts1.length) || 0) === 1 ? '' : 's', \" selected for deletion\") : 'Blog post \"'.concat(((_deleteConfirmation_post = deleteConfirmation.post) === null || _deleteConfirmation_post === void 0 ? void 0 : _deleteConfirmation_post.title) || 'Unknown', '\" ready for deletion')\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 2167,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n        lineNumber: 1152,\n        columnNumber: 5\n    }, this);\n}\n_s1(BlogsManagement, \"oAck7AUskU6uZdwdlzA1u342lWQ=\", false, function() {\n    return [\n        _components_providers_notification_provider__WEBPACK_IMPORTED_MODULE_4__.useNotifications\n    ];\n});\n_c1 = BlogsManagement;\nvar _c, _c1;\n$RefreshReg$(_c, \"BlogAvatar\");\n$RefreshReg$(_c1, \"BlogsManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FkbWluL2Jsb2cvYmxvZ3MtbWFuYWdlbWVudC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJEO0FBd0J0QjtBQUNJO0FBRXVDO0FBQ2Y7QUE2SmpFLFNBQVN3QixXQUFXLEtBTUY7UUFORSxFQUNsQkMsS0FBSyxFQUNMQyxnQkFBZ0IsRUFDaEJDLE9BQU8sSUFBSSxFQUNYQyxZQUFZLEVBQUUsRUFDZEMsUUFBUSxDQUFDLENBQUMsRUFDTSxHQU5FOztJQU9sQixNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQytCLGNBQWNDLGdCQUFnQixHQUFHaEMsK0NBQVFBLENBQUM7SUFFakQsc0JBQXNCO0lBQ3RCLE1BQU1pQyxjQUFjO1FBQ2xCQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7UUFDSixlQUFlO0lBQ2pCO0lBRUEsTUFBTUMsWUFBWTtRQUNoQkwsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0osZUFBZTtJQUNqQjtJQUVBLE1BQU1FLFlBQVk7UUFDaEJOLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtRQUNKLGVBQWU7SUFDakI7SUFFQSxvQ0FBb0M7SUFDcEMsTUFBTUcsY0FBYyxDQUFDakI7UUFDbkIsT0FBT0EsTUFDSmtCLEtBQUssQ0FBQyxLQUNOQyxHQUFHLENBQUNDLENBQUFBLE9BQVFBLEtBQUtDLE1BQU0sQ0FBQyxJQUN4QkMsSUFBSSxDQUFDLElBQ0xDLFdBQVcsR0FDWEMsS0FBSyxDQUFDLEdBQUc7SUFDZDtJQUVBLHNEQUFzRDtJQUN0RCxNQUFNQyxxQkFBcUIsQ0FBQ3pCO1FBQzFCLE1BQU0wQixTQUFTO1lBQ2I7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUVELElBQUlDLE9BQU87UUFDWCxJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSTVCLE1BQU02QixNQUFNLEVBQUVELElBQUs7WUFDckNELE9BQU8zQixNQUFNOEIsVUFBVSxDQUFDRixLQUFNLEVBQUNELFFBQVEsS0FBS0EsSUFBRztRQUNqRDtRQUVBLE9BQU9ELE1BQU0sQ0FBQ0ssS0FBS0MsR0FBRyxDQUFDTCxRQUFRRCxPQUFPRyxNQUFNLENBQUM7SUFDL0M7SUFFQSxNQUFNSSxrQkFBa0I7UUFDdEJ6QixnQkFBZ0I7UUFDaEJGLGNBQWM7SUFDaEI7SUFFQSxNQUFNNEIsbUJBQW1CO1FBQ3ZCMUIsZ0JBQWdCO1FBQ2hCRixjQUFjO0lBQ2hCO0lBRUEsTUFBTTZCLGNBQWMsU0FPaEJqQyxPQU5BTyxXQUFXLENBQUNQLEtBQUssRUFBQyxxR0FPbEJDLE9BREFELFNBQVMsZ0JBQWdCLGtCQUFrQixJQUFHLFVBQ3BDLE9BQVZDLFdBQVU7SUFHZCxxRUFBcUU7SUFDckUsSUFBSUYsb0JBQW9CLENBQUNJLFlBQVk7UUFDbkMscUJBQ0UsOERBQUMrQjtZQUNDakMsV0FBVyxHQUFlLE9BQVpnQyxhQUFZO1lBQzFCL0IsT0FBT0E7O2dCQUVORyw4QkFDQyw4REFBQzZCO29CQUFJakMsV0FBVTs4QkFDYiw0RUFBQ2lDO3dCQUFJakMsV0FBVTs7Ozs7Ozs7Ozs7OEJBR25CLDhEQUFDa0M7b0JBQ0NDLEtBQUtyQztvQkFDTHNDLEtBQUssR0FBUyxPQUFOdkMsT0FBTTtvQkFDZEcsV0FBVyxpQkFFUEksT0FEQUwsU0FBUyxnQkFBZ0IsK0JBQStCLDhCQUE2QixrQkFDMUMsT0FBM0NLLGVBQWUsY0FBYyxlQUFjO29CQUcvQ2lDLFFBQVFQO29CQUNSUSxTQUFTUDs7Ozs7Ozs7Ozs7O0lBSWpCO0lBRUEsa0RBQWtEO0lBQ2xELHFCQUNFLDhEQUFDRTtRQUNDakMsV0FBVyxhQUVQc0IsT0FEQVUsYUFBWSxlQUlabkIsT0FIQVMsbUJBQW1CekIsUUFBTyw0REFHVixPQUFoQmdCLFNBQVMsQ0FBQ2QsS0FBSyxFQUFDO1FBR3BCRSxPQUFPQTtRQUNQSixPQUFPQTtrQkFFTkUsU0FBUyw4QkFDUiw4REFBQ2tDO1lBQUlqQyxXQUFVOzs4QkFDYiw4REFBQ2xCLHVWQUFnQkE7b0JBQUNrQixXQUFVOzs7Ozs7OEJBQzVCLDhEQUFDaUM7b0JBQUlqQyxXQUFVOztzQ0FDYiw4REFBQ2lDOzRCQUFJakMsV0FBVTtzQ0FBMkJjLFlBQVlqQjs7Ozs7O3NDQUN0RCw4REFBQ29DOzRCQUFJakMsV0FBVTtzQ0FBK0NIOzs7Ozs7Ozs7Ozs7Ozs7OztpQ0FJbEUsOERBQUMwQztzQkFBTXpCLFlBQVlqQjs7Ozs7Ozs7Ozs7QUFJM0I7R0FsSlNEO0tBQUFBO0FBb0pGLFNBQVM0QyxnQkFBZ0IsS0FBNEI7UUFBNUIsRUFBRUMsTUFBTSxFQUFvQixHQUE1QjtRQVlPQSxxQkFFbkNBLHNCQUlBQSw2QkFDQUEsOEJBS0FBLDhCQUNBQSw4QkFDQUEsOEJBTWNBLDhCQTZqQ1dBLGlCQW1PUkEsa0JBNEpBQSxxQkFnV0pDLCtCQUF1REEsZ0NBQzVDQTs7SUE1ekQxQixzQkFBc0I7SUFDdEIsTUFBTSxFQUFFQyxXQUFXLEVBQUVDLFNBQVMsRUFBRUMsV0FBVyxFQUFFQyxRQUFRLEVBQUVDLFdBQVcsRUFBRUMseUJBQXlCLEVBQUUsR0FBR3RELDZGQUFnQkE7SUFFbEgsbUJBQW1CO0lBQ25CLE1BQU0sQ0FBQ3VELFdBQVdDLGFBQWEsR0FBRzdFLCtDQUFRQSxDQUFhLEVBQUU7SUFDekQsTUFBTSxDQUFDOEUsU0FBU0MsV0FBVyxHQUFHL0UsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDZ0YsT0FBT0MsU0FBUyxHQUFHakYsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQ2tGLGFBQWFDLGVBQWUsR0FBR25GLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ29GLHNCQUFzQkMsd0JBQXdCLEdBQUdyRiwrQ0FBUUEsQ0FBQztJQUNqRSxNQUFNLENBQUNzRixhQUFhQyxlQUFlLEdBQUd2RiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUN3RixZQUFZQyxjQUFjLEdBQUd6RiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUMwRixRQUFRQyxVQUFVLEdBQUczRiwrQ0FBUUEsQ0FBQ29FLEVBQUFBLHNCQUFBQSxPQUFPd0IsV0FBVyxjQUFsQnhCLDBDQUFBQSxvQkFBb0J5QixLQUFLLEtBQUk7SUFDbEUsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUcvRiwrQ0FBUUEsQ0FDeENvRSxFQUFBQSx1QkFBQUEsT0FBT3dCLFdBQVcsY0FBbEJ4QiwyQ0FBQUEscUJBQW9CNEIsU0FBUyxNQUFLLFFBQVEsUUFBUTtJQUVwRCxNQUFNLENBQUNDLGVBQWVDLGlCQUFpQixHQUFHbEcsK0NBQVFBLENBQVcsRUFBRTtJQUMvRCxNQUFNLENBQUNtRyxVQUFVQyxZQUFZLEdBQUdwRywrQ0FBUUEsQ0FDdENvRSxFQUFBQSw4QkFBQUEsT0FBT2lDLG1CQUFtQixjQUExQmpDLGtEQUFBQSw0QkFBNEJrQyxJQUFJLE1BQUssVUFDckNsQyxFQUFBQSwrQkFBQUEsT0FBT2lDLG1CQUFtQixjQUExQmpDLG1EQUFBQSw2QkFBNEJrQyxJQUFJLE1BQUssU0FDakNsQyxPQUFPaUMsbUJBQW1CLENBQUNDLElBQUksR0FDL0I7SUFFTixNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR3hHLCtDQUFRQSxDQUNwQ29FLEVBQUFBLCtCQUFBQSxPQUFPaUMsbUJBQW1CLGNBQTFCakMsbURBQUFBLDZCQUE0Qm1DLE9BQU8sTUFBSyxhQUN4Q25DLEVBQUFBLCtCQUFBQSxPQUFPaUMsbUJBQW1CLGNBQTFCakMsbURBQUFBLDZCQUE0Qm1DLE9BQU8sTUFBSyxpQkFDeENuQyxFQUFBQSwrQkFBQUEsT0FBT2lDLG1CQUFtQixjQUExQmpDLG1EQUFBQSw2QkFBNEJtQyxPQUFPLE1BQUssYUFDbkNuQyxPQUFPaUMsbUJBQW1CLENBQUNFLE9BQU8sR0FDbkM7SUFHTixNQUFNLENBQUNFLGdCQUFnQkMsa0JBQWtCLEdBQUcxRywrQ0FBUUEsQ0FDbEQyRyxNQUFNQyxPQUFPLEVBQUN4QywrQkFBQUEsT0FBT2lDLG1CQUFtQixjQUExQmpDLG1EQUFBQSw2QkFBNEJxQyxjQUFjLElBQ3BEckMsT0FBT2lDLG1CQUFtQixDQUFDSSxjQUFjLEdBQ3pDLEVBQUU7SUFFUixNQUFNLENBQUNJLFNBQVNDLFdBQVcsR0FBRzlHLCtDQUFRQSxDQUF5QixDQUFDO0lBQ2hFLE1BQU0sQ0FBQytHLGFBQWFDLGVBQWUsR0FBR2hILCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ2lILGVBQWVDLGlCQUFpQixHQUFHbEgsK0NBQVFBLENBQWdCO0lBQ2xFLE1BQU0sQ0FBQ21ILG9CQUFvQkMsc0JBQXNCLEdBQUdwSCwrQ0FBUUEsQ0FBQztJQUM3RCxNQUFNLENBQUNxSCxnQkFBZ0JDLGtCQUFrQixHQUFHdEgsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDdUgsYUFBYUMsZUFBZSxHQUFHeEgsK0NBQVFBLENBQUM7SUFFL0MsZUFBZTtJQUNmLE1BQU0sQ0FBQ3lILG1CQUFtQkMscUJBQXFCLEdBQUcxSCwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUMySCxpQkFBaUJDLG1CQUFtQixHQUFHNUgsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDNkgsYUFBYUMsZUFBZSxHQUFHOUgsK0NBQVFBLENBQWtCO0lBRWhFLDJCQUEyQjtJQUMzQixNQUFNLENBQUNxRSxvQkFBb0IwRCxzQkFBc0IsR0FBRy9ILCtDQUFRQSxDQUt6RDtRQUNEZ0ksUUFBUTtRQUNSQyxNQUFNO1FBQ05DLGNBQWM7UUFDZEMsV0FBVyxFQUFFO0lBQ2Y7SUFFQSx3QkFBd0I7SUFDeEJsSSxnREFBU0E7cUNBQUM7WUFDUixNQUFNbUksUUFBUUM7bURBQVc7b0JBQ3ZCaEQsd0JBQXdCSDtvQkFDeEJLLGVBQWUsSUFBSSxxQ0FBcUM7Z0JBQzFEO2tEQUFHO1lBRUg7NkNBQU8sSUFBTStDLGFBQWFGOztRQUM1QjtvQ0FBRztRQUFDbEQ7S0FBWTtJQUVoQixtQkFBbUI7SUFDbkIsTUFBTXFELGlCQUFpQjtZQUFPQyxpRkFBZ0I7UUFDNUN6RCxXQUFXO1FBQ1gsSUFBSSxDQUFDeUQsZUFBZTtZQUNsQjlELFlBQVksc0JBQXNCO1FBQ3BDO1FBRUEsSUFBSTtZQUNGLE1BQU0rRCxTQUFTLElBQUlDLGdCQUFnQjtnQkFDakNDLE1BQU1yRCxZQUFZc0QsUUFBUTtnQkFDMUJDLE9BQU8sQ0FBQ3pFLE9BQU8wRSxRQUFRLElBQUksRUFBQyxFQUFHRixRQUFRO2dCQUN2Q0csUUFBUTNEO2dCQUNSTTtnQkFDQUk7WUFDRjtZQUVBLHdCQUF3QjtZQUN4QmtELE9BQU9DLE9BQU8sQ0FBQ3BDLFNBQVNxQyxPQUFPLENBQUM7b0JBQUMsQ0FBQ0MsS0FBS0MsTUFBTTtnQkFDM0MsSUFBSUEsT0FBTztvQkFDVFgsT0FBT1ksTUFBTSxDQUFDRixLQUFLQztnQkFDckI7WUFDRjtZQUVBRSxRQUFRQyxHQUFHLENBQUMsb0NBQW9DZCxPQUFPRyxRQUFRLEtBQUssWUFBWTtZQUVoRixNQUFNWSxXQUFXLE1BQU1DLE1BQU0sY0FBaUNoQixPQUFuQnJFLE9BQU9zRixRQUFRLEVBQUMsS0FBVSxPQUFQakI7WUFFOUQsSUFBSSxDQUFDZSxTQUFTRyxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU1DLFlBQVksTUFBTUosU0FBU0ssSUFBSTtnQkFDckNQLFFBQVF0RSxLQUFLLENBQUMsdUJBQXVCNEU7Z0JBQ3JDLE1BQU0sSUFBSUUsTUFBTSx1QkFBNENGLE9BQXJCSixTQUFTTyxNQUFNLEVBQUMsT0FBZSxPQUFWSDtZQUM5RDtZQUVBLE1BQU1JLE9BQU8sTUFBTVIsU0FBU1MsSUFBSTtZQUNoQ1gsUUFBUUMsR0FBRyxDQUFDLDZCQUE2QlMsT0FBTyxZQUFZO1lBRTVELElBQUksQ0FBQ0EsS0FBS0UsT0FBTyxFQUFFO2dCQUNqQixNQUFNLElBQUlKLE1BQU1FLEtBQUtoRixLQUFLLElBQUk7WUFDaEM7WUFFQUgsYUFBYW1GLEtBQUtHLEtBQUssSUFBSSxFQUFFO1lBQzdCMUUsY0FBY2xDLEtBQUs2RyxJQUFJLENBQUMsQ0FBQ0osS0FBS0ssVUFBVSxJQUFJLEtBQU1qRyxDQUFBQSxPQUFPMEUsUUFBUSxJQUFJLEVBQUM7WUFDdEU3RCxTQUFTLE9BQU8sZ0RBQWdEO1lBRWhFLElBQUksQ0FBQ3VELGVBQWU7b0JBQ3lCd0IsYUFBb0NBO2dCQUEvRTFGLFlBQVkscUJBQXFCLFVBQThDMEYsT0FBcENBLEVBQUFBLGNBQUFBLEtBQUtHLEtBQUssY0FBVkgsa0NBQUFBLFlBQVkzRyxNQUFNLEtBQUksR0FBRSxjQUFnRCxPQUFwQzJHLEVBQUFBLGVBQUFBLEtBQUtHLEtBQUssY0FBVkgsbUNBQUFBLGFBQVkzRyxNQUFNLE1BQUssSUFBSSxLQUFLO1lBQ2pIO1FBQ0YsRUFBRSxPQUFPaUgsS0FBSztZQUNaaEIsUUFBUXRFLEtBQUssQ0FBQyw4QkFBOEJzRixNQUFNLFlBQVk7WUFDOURyRixTQUFTcUYsZUFBZVIsUUFBUVEsSUFBSUMsT0FBTyxHQUFHO1lBQzlDLElBQUksQ0FBQy9CLGVBQWU7Z0JBQ2xCakUsVUFBVSw2QkFBNkI7WUFDekM7UUFDRixTQUFVO1lBQ1JRLFdBQVc7UUFDYjtJQUNGO0lBRUE5RSxnREFBU0E7cUNBQUM7WUFDUixnQ0FBZ0M7WUFDaEMsTUFBTXVLLGNBQWNwRix5QkFBeUI7WUFDN0NtRCxlQUFlaUM7UUFDakI7b0NBQUc7UUFBQ2xGO1FBQWFGO1FBQXNCTTtRQUFRSTtRQUFXZTtLQUFRO0lBRWxFLGdCQUFnQjtJQUNoQixNQUFNNEQsZUFBZSxPQUFPQztRQUMxQixJQUFJO1lBQ0ZoRyxZQUFZLHNCQUFzQjtZQUVsQyxNQUFNOEUsV0FBVyxNQUFNQyxNQUFNLGNBQThCLE9BQWhCckYsT0FBT3NGLFFBQVEsR0FBSTtnQkFDNURpQixRQUFRO2dCQUNSQyxTQUFTO29CQUFFLGdCQUFnQjtnQkFBbUI7Z0JBQzlDQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNMO1lBQ3ZCO1lBRUEsTUFBTVYsT0FBTyxNQUFNUixTQUFTUyxJQUFJO1lBRWhDLElBQUksQ0FBQ1QsU0FBU0csRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlHLE1BQU1FLEtBQUtoRixLQUFLLElBQUksK0JBQStDLE9BQWhCd0UsU0FBU08sTUFBTSxFQUFDO1lBQy9FO1lBRUEsSUFBSSxDQUFDQyxLQUFLRSxPQUFPLEVBQUU7Z0JBQ2pCLE1BQU0sSUFBSUosTUFBTUUsS0FBS2hGLEtBQUssSUFBSTtZQUNoQztZQUVBMEMscUJBQXFCO1lBQ3JCYTtZQUNBakUsWUFBWSxxQkFBcUIsSUFBc0MsT0FBbENvRyxTQUFTbEosS0FBSyxJQUFJLGlCQUFnQjtRQUN6RSxFQUFFLE9BQU84SSxLQUFLO1lBQ1osTUFBTVUsZUFBZVYsZUFBZVIsUUFBUVEsSUFBSUMsT0FBTyxHQUFHO1lBQzFEdEYsU0FBUytGO1lBQ1R6RyxVQUFVLDhCQUE4QnlHO1lBQ3hDLE1BQU1WO1FBQ1I7SUFDRjtJQUVBLDBCQUEwQjtJQUMxQixNQUFNVyxpQkFBaUI7UUFDckJ4RyxTQUFTLHVCQUF1QjtRQUNoQ2lELHFCQUFxQjtJQUN2QjtJQUVBLGdCQUFnQjtJQUNoQixNQUFNd0QsZUFBZSxPQUFPQyxJQUFZVDtRQUN0QyxJQUFJO1lBQ0ZoRyxZQUFZLHNCQUFzQjtZQUVsQyxNQUFNOEUsV0FBVyxNQUFNQyxNQUFNLGNBQWlDMEIsT0FBbkIvRyxPQUFPc0YsUUFBUSxFQUFDLEtBQU0sT0FBSHlCLEtBQU07Z0JBQ2xFUixRQUFRO2dCQUNSQyxTQUFTO29CQUFFLGdCQUFnQjtnQkFBbUI7Z0JBQzlDQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNMO1lBQ3ZCO1lBRUEsTUFBTVYsT0FBTyxNQUFNUixTQUFTUyxJQUFJO1lBRWhDLElBQUksQ0FBQ1QsU0FBU0csRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlHLE1BQU1FLEtBQUtoRixLQUFLLElBQUksK0JBQStDLE9BQWhCd0UsU0FBU08sTUFBTSxFQUFDO1lBQy9FO1lBRUEsSUFBSSxDQUFDQyxLQUFLRSxPQUFPLEVBQUU7Z0JBQ2pCLE1BQU0sSUFBSUosTUFBTUUsS0FBS2hGLEtBQUssSUFBSTtZQUNoQztZQUVBNEMsbUJBQW1CO1lBQ25CRSxlQUFlO1lBQ2ZTO1lBQ0FqRSxZQUFZLHFCQUFxQixJQUFrQyxPQUE5Qm9HLFNBQVNsSixLQUFLLElBQUksYUFBWTtRQUNyRSxFQUFFLE9BQU84SSxLQUFLO1lBQ1osTUFBTVUsZUFBZVYsZUFBZVIsUUFBUVEsSUFBSUMsT0FBTyxHQUFHO1lBQzFEdEYsU0FBUytGO1lBQ1R6RyxVQUFVLDhCQUE4QnlHO1lBQ3hDLE1BQU1WO1FBQ1I7SUFDRjtJQUVBLGdCQUFnQjtJQUNoQixNQUFNYyxlQUFlLE9BQU9EO1FBQzFCLElBQUk7WUFDRixNQUFNRSxlQUFlekcsVUFBVTBHLElBQUksQ0FBQ3JELENBQUFBLE9BQVFBLEtBQUtrRCxFQUFFLEtBQUtBO1lBQ3hEekcsWUFBWSxzQkFBc0IsYUFBZ0QsT0FBbkMyRyxDQUFBQSx5QkFBQUEsbUNBQUFBLGFBQWM3SixLQUFLLEtBQUksYUFBWTtZQUVsRixNQUFNZ0ksV0FBVyxNQUFNQyxNQUFNLGNBQWlDMEIsT0FBbkIvRyxPQUFPc0YsUUFBUSxFQUFDLEtBQU0sT0FBSHlCLEtBQU07Z0JBQ2xFUixRQUFRO1lBQ1Y7WUFFQSxJQUFJLENBQUNuQixTQUFTRyxFQUFFLEVBQUUsTUFBTSxJQUFJRyxNQUFNO1lBRWxDdkI7WUFDQWpFLFlBQVkscUJBQXFCLElBQXVDLE9BQW5DK0csQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFjN0osS0FBSyxLQUFJLGFBQVk7UUFDMUUsRUFBRSxPQUFPOEksS0FBSztZQUNaLE1BQU1VLGVBQWVWLGVBQWVSLFFBQVFRLElBQUlDLE9BQU8sR0FBRztZQUMxRHRGLFNBQVMrRjtZQUNUekcsVUFBVSw4QkFBOEJ5RztZQUN4QyxNQUFNVjtRQUNSO0lBQ0Y7SUFFQSwyQkFBMkI7SUFDM0IsTUFBTWlCLHlCQUF5QixDQUFDdEQ7UUFDOUJGLHNCQUFzQjtZQUNwQkMsUUFBUTtZQUNSQztZQUNBQyxjQUFjO1lBQ2RDLFdBQVcsRUFBRTtRQUNmO0lBQ0Y7SUFFQSxnQ0FBZ0M7SUFDaEMsTUFBTXFELDZCQUE2QjtRQUNqQyxNQUFNQyxnQkFBZ0I3RyxVQUFVOEcsTUFBTSxDQUFDekQsQ0FBQUEsT0FBUWhDLGNBQWMwRixRQUFRLENBQUMxRCxLQUFLa0QsRUFBRTtRQUM3RXBELHNCQUFzQjtZQUNwQkMsUUFBUTtZQUNSQyxNQUFNO1lBQ05DLGNBQWM7WUFDZEMsV0FBV3NEO1FBQ2I7SUFDRjtJQUVBLGlCQUFpQjtJQUNqQixNQUFNRyxnQkFBZ0I7UUFDcEIsSUFBSTtZQUNGLElBQUl2SCxtQkFBbUI2RCxZQUFZLElBQUk3RCxtQkFBbUI4RCxTQUFTLEVBQUU7Z0JBQ25FLGNBQWM7Z0JBQ2QsTUFBTTBELFdBQVd4SCxtQkFBbUI4RCxTQUFTLENBQUN4RixHQUFHLENBQUNzRixDQUFBQSxPQUNoRHdCLE1BQU0sY0FBaUN4QixPQUFuQjdELE9BQU9zRixRQUFRLEVBQUMsS0FBVyxPQUFSekIsS0FBS2tELEVBQUUsR0FBSTt3QkFBRVIsUUFBUTtvQkFBUztnQkFFdkUsTUFBTW1CLFFBQVFDLEdBQUcsQ0FBQ0Y7Z0JBQ2xCM0YsaUJBQWlCLEVBQUU7Z0JBQ25CcUM7Z0JBQ0FqRSxZQUFZLHNCQUFzQixHQUF1QyxPQUFwQ0QsbUJBQW1COEQsU0FBUyxDQUFDOUUsTUFBTSxFQUFDO1lBQzNFLE9BQU8sSUFBSWdCLG1CQUFtQjRELElBQUksRUFBRTtnQkFDbEMsZ0JBQWdCO2dCQUNoQixNQUFNbUQsYUFBYS9HLG1CQUFtQjRELElBQUksQ0FBQ2tELEVBQUU7WUFDL0M7UUFDRixFQUFFLE9BQU9iLEtBQUs7WUFDWixNQUFNVSxlQUFlVixlQUFlUixRQUFRUSxJQUFJQyxPQUFPLEdBQUc7WUFDMURoRyxVQUFVLGlDQUFpQ3lHO1FBQzdDLFNBQVU7WUFDUmpELHNCQUFzQjtnQkFDcEJDLFFBQVE7Z0JBQ1JDLE1BQU07Z0JBQ05DLGNBQWM7Z0JBQ2RDLFdBQVcsRUFBRTtZQUNmO1FBQ0Y7SUFDRjtJQUVBLGdCQUFnQjtJQUNoQixNQUFNNkQsZUFBZTtRQUNuQmpFLHNCQUFzQjtZQUNwQkMsUUFBUTtZQUNSQyxNQUFNO1lBQ05DLGNBQWM7WUFDZEMsV0FBVyxFQUFFO1FBQ2Y7SUFDRjtJQUVBLHNCQUFzQjtJQUN0QixNQUFNOEQsbUJBQW1CLE9BQU9DO1FBQzlCLElBQUlqRyxjQUFjNUMsTUFBTSxLQUFLLEdBQUc7UUFFaEM2RCxpQkFBaUJnRjtRQUNqQixJQUFJO1lBQ0YsSUFBSUEsV0FBVyxVQUFVO2dCQUN2QlY7Z0JBQ0E7WUFDRjtZQUVBOUcsWUFBWSxRQUFlLE9BQVB3SCxTQUFVLGNBQStDakcsT0FBakNBLGNBQWM1QyxNQUFNLEVBQUMsY0FBa0QsT0FBdEM0QyxjQUFjNUMsTUFBTSxLQUFLLElBQUksS0FBSyxLQUFJO1lBRW5ILE1BQU13SSxXQUFXNUYsY0FBY3RELEdBQUcsQ0FBQyxPQUFPd0k7Z0JBQ3hDLE9BQVFlO29CQUNOLEtBQUs7d0JBQ0gsT0FBT3pDLE1BQU0sY0FBaUMwQixPQUFuQi9HLE9BQU9zRixRQUFRLEVBQUMsS0FBTSxPQUFIeUIsS0FBTTs0QkFDbERSLFFBQVE7NEJBQ1JDLFNBQVM7Z0NBQUUsZ0JBQWdCOzRCQUFtQjs0QkFDOUNDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztnQ0FBRW9CLGFBQWE7NEJBQUs7d0JBQzNDO29CQUNGLEtBQUs7d0JBQ0gsT0FBTzFDLE1BQU0sY0FBaUMwQixPQUFuQi9HLE9BQU9zRixRQUFRLEVBQUMsS0FBTSxPQUFIeUIsS0FBTTs0QkFDbERSLFFBQVE7NEJBQ1JDLFNBQVM7Z0NBQUUsZ0JBQWdCOzRCQUFtQjs0QkFDOUNDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztnQ0FBRW9CLGFBQWE7NEJBQU07d0JBQzVDO29CQUNGO3dCQUNFLE1BQU0sSUFBSXJDLE1BQU0sd0JBQStCLE9BQVBvQztnQkFDNUM7WUFDRjtZQUVBLE1BQU1KLFFBQVFDLEdBQUcsQ0FBQ0Y7WUFDbEIzRixpQkFBaUIsRUFBRTtZQUNuQnFDO1lBQ0FqRSxZQUFZLFFBQWUsT0FBUDRILFFBQU8sZUFBYSxHQUFvQ2pHLE9BQWpDQSxjQUFjNUMsTUFBTSxFQUFDLGNBQXFENkksT0FBekNqRyxjQUFjNUMsTUFBTSxLQUFLLElBQUksS0FBSyxLQUFJLEtBQVUsT0FBUDZJLFFBQU87UUFDOUgsRUFBRSxPQUFPNUIsS0FBSztZQUNaLE1BQU1VLGVBQWVWLGVBQWVSLFFBQVFRLElBQUlDLE9BQU8sR0FBRyxhQUFvQixPQUFQMkIsUUFBTztZQUM5RWpILFNBQVMrRjtZQUNUekcsVUFBVSxhQUFvQixPQUFQMkgsUUFBTyxnQkFBY2xCO1FBQzlDLFNBQVU7WUFDUjlELGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsNEJBQTRCO0lBQzVCLE1BQU1rRixlQUFlLE9BQU9GLFFBQWdCRztRQUMxQ25GLGlCQUFpQixHQUFhbUYsT0FBVkgsUUFBTyxLQUFXLE9BQVJHLEtBQUtsQixFQUFFO1FBQ3JDLElBQUk7WUFDRixPQUFRZTtnQkFDTixLQUFLO29CQUNIekgsU0FBUyxrQkFBa0IsWUFBdUIsT0FBWDRILEtBQUs3SyxLQUFLLEVBQUM7b0JBQ2xEc0csZUFBZXVFO29CQUNmekUsbUJBQW1CO29CQUNuQjtnQkFFRixLQUFLO29CQUNIbkQsU0FBUyxnQkFBZ0IsWUFBdUIsT0FBWDRILEtBQUs3SyxLQUFLLEVBQUM7b0JBQ2hELHFDQUFxQztvQkFDckNpRCxTQUFTLGtCQUFrQixZQUF1QixPQUFYNEgsS0FBSzdLLEtBQUssRUFBQztvQkFDbEQ7Z0JBRUYsS0FBSztvQkFDSGlELFNBQVMsdUJBQXVCLHdCQUFtQyxPQUFYNEgsS0FBSzdLLEtBQUssRUFBQztvQkFDbkUrSix1QkFBdUJjO29CQUN2QjtnQkFFRixLQUFLO29CQUNILE1BQU1DLFlBQVksQ0FBQ0QsS0FBS0YsV0FBVztvQkFDbkN6SCxZQUNFNEgsWUFBWSx5QkFBeUIsMEJBQ3JDLEdBQWlERCxPQUE5Q0MsWUFBWSxlQUFlLGdCQUFlLE1BQWUsT0FBWEQsS0FBSzdLLEtBQUssRUFBQztvQkFFOUQsTUFBTTBKLGFBQWFtQixLQUFLbEIsRUFBRSxFQUFFO3dCQUFFZ0IsYUFBYUc7b0JBQVU7b0JBQ3JEaEksWUFDRWdJLFlBQVksd0JBQXdCLHlCQUNwQyxJQUFtQkEsT0FBZkQsS0FBSzdLLEtBQUssRUFBQyxNQUE0QyxPQUF4QzhLLFlBQVksY0FBYyxlQUFjO29CQUU3RDtnQkFFRixLQUFLO29CQUNINUgsWUFBWSx5QkFBeUIscUJBQWdDLE9BQVgySCxLQUFLN0ssS0FBSyxFQUFDO29CQUNyRSwwQ0FBMEM7b0JBQzFDOEMsWUFBWSx3QkFBd0IsSUFBZSxPQUFYK0gsS0FBSzdLLEtBQUssRUFBQztvQkFDbkQ7Z0JBRUYsS0FBSztvQkFDSGtELFlBQVksdUJBQXVCLGNBQXlCLE9BQVgySCxLQUFLN0ssS0FBSyxFQUFDO29CQUM1RCx3Q0FBd0M7b0JBQ3hDOEMsWUFBWSxzQkFBc0IsSUFBZSxPQUFYK0gsS0FBSzdLLEtBQUssRUFBQztvQkFDakQ7Z0JBRUY7b0JBQ0U4SCxRQUFRaUQsSUFBSSxDQUFDLG1CQUEwQixPQUFQTDtZQUNwQztRQUNGLFNBQVU7WUFDUmhGLGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsaUJBQWlCO0lBQ2pCLE1BQU1zRixhQUFhLENBQUMzRztRQUNsQixNQUFNNEcsV0FBVy9HLFdBQVdHLFFBQVNDLGNBQWMsUUFBUSxTQUFTLFFBQVM7UUFDN0UsTUFBTTRHLFdBQVdoSCxXQUFXRyxRQUFRQSxRQUFRQTtRQUU1Q0YsVUFBVStHO1FBQ1YzRyxhQUFhMEc7UUFDYmxILGVBQWU7UUFFZmQsU0FBUyxzQkFBc0IsY0FBd0JnSSxPQUFWNUcsT0FBTSxNQUFhLE9BQVQ0RyxVQUFTO0lBQ2xFO0lBRUEsbUJBQW1CO0lBQ25CLE1BQU1FLGtCQUFrQixDQUFDQztRQUN2QixJQUFJQSxTQUFTO1lBQ1gxRyxpQkFBaUJ0QixVQUFVakMsR0FBRyxDQUFDc0YsQ0FBQUEsT0FBUUEsS0FBS2tELEVBQUU7WUFDOUMxRyxTQUFTLHFCQUFxQixnQkFBNkNHLE9BQTdCQSxVQUFVdkIsTUFBTSxFQUFDLGNBQThDLE9BQWxDdUIsVUFBVXZCLE1BQU0sS0FBSyxJQUFJLEtBQUs7UUFDM0csT0FBTztZQUNMNkMsaUJBQWlCLEVBQUU7WUFDbkJ6QixTQUFTLHFCQUFxQjtRQUNoQztJQUNGO0lBRUEsTUFBTW9JLG1CQUFtQixDQUFDMUIsSUFBWXlCO1FBQ3BDLElBQUlBLFNBQVM7WUFDWDFHLGlCQUFpQjttQkFBSUQ7Z0JBQWVrRjthQUFHO1lBQ3ZDLE1BQU1sRCxPQUFPckQsVUFBVTBHLElBQUksQ0FBQ3dCLENBQUFBLElBQUtBLEVBQUUzQixFQUFFLEtBQUtBO1lBQzFDMUcsU0FBUyxpQkFBaUIsSUFBK0IsT0FBM0J3RCxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU16RyxLQUFLLEtBQUksYUFBWTtRQUMzRCxPQUFPO1lBQ0wwRSxpQkFBaUJELGNBQWN5RixNQUFNLENBQUNxQixDQUFBQSxTQUFVQSxXQUFXNUI7WUFDM0QsTUFBTWxELE9BQU9yRCxVQUFVMEcsSUFBSSxDQUFDd0IsQ0FBQUEsSUFBS0EsRUFBRTNCLEVBQUUsS0FBS0E7WUFDMUMxRyxTQUFTLG1CQUFtQixJQUErQixPQUEzQndELENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTXpHLEtBQUssS0FBSSxhQUFZO1FBQzdEO0lBQ0Y7SUFFQSwrQkFBK0I7SUFDL0IsTUFBTXdMLG1CQUFtQjtRQUN2QixJQUFJdkcsZUFBZXBELE1BQU0sR0FBRyxHQUFHO2dCQUN0QmU7WUFBUCxPQUFPQSxFQUFBQSxpQkFBQUEsT0FBTzZJLE1BQU0sY0FBYjdJLHFDQUFBQSxlQUFlc0gsTUFBTSxDQUFDN0YsQ0FBQUEsUUFBU1ksZUFBZWtGLFFBQVEsQ0FBQzlGLE1BQU1zRCxHQUFHLE9BQU0sRUFBRTtRQUNqRjtRQUNBLE9BQU8vRSxPQUFPNkksTUFBTSxJQUFJLEVBQUU7SUFDNUI7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTUMsdUJBQXVCLENBQUM1RztRQUM1QkYsWUFBWUU7UUFDWjdCLFNBQVMscUJBQXFCLGVBQW9CLE9BQUw2QixNQUFLO0lBQ3BEO0lBRUEsd0JBQXdCO0lBQ3hCLE1BQU02RyxzQkFBc0IsQ0FBQ0M7UUFDM0I1RyxXQUFXNEc7UUFDWDNJLFNBQVMsbUJBQW1CLGNBQXlCLE9BQVgySSxZQUFXO0lBQ3ZEO0lBRUEsNkJBQTZCO0lBQzdCLE1BQU1DLDBCQUEwQixDQUFDQztRQUMvQjlGLGVBQWU4RjtRQUNmN0ksU0FBUyx1QkFBdUIsY0FBK0I2SSxPQUFqQkEsU0FBUSxXQUFrQyxPQUF6QkEsWUFBWSxJQUFJLEtBQUssS0FBSTtJQUMxRjtJQUVBLHdCQUF3QjtJQUN4QixNQUFNQyxxQkFBcUIsQ0FBQ3BFLEtBQWFDO1FBQ3ZDLE1BQU1vRSxhQUFhO1lBQUUsR0FBRzNHLE9BQU87UUFBQztRQUNoQyxJQUFJdUMsT0FBTztZQUNUb0UsVUFBVSxDQUFDckUsSUFBSSxHQUFHQztRQUNwQixPQUFPO1lBQ0wsT0FBT29FLFVBQVUsQ0FBQ3JFLElBQUk7UUFDeEI7UUFDQXJDLFdBQVcwRztRQUNYL0ksU0FBUyxrQkFBa0IsbUJBQTRCMkUsT0FBVEQsS0FBSSxPQUFvQixPQUFmQyxTQUFTO0lBQ2xFO0lBRUEsc0JBQXNCO0lBQ3RCLE1BQU1xRSxXQUFXO1lBQUMsRUFBRXhGLElBQUksRUFBc0I7UUFDNUMsTUFBTXlGLGFBQWF6SCxjQUFjMEYsUUFBUSxDQUFDMUQsS0FBS2tELEVBQUU7UUFDakQsTUFBTXdDLFVBQVVDO1FBRWhCLHFCQUNFLDhEQUFDaEs7WUFDQ2pDLFdBQVcsbUhBRVBnTSxPQURGRCxhQUFhLG9DQUFvQyxJQUNsRCxLQUFnQixPQUFiQyxRQUFRRSxJQUFJO1lBQ2hCQyxTQUFTLENBQUNDO2dCQUNSLHVEQUF1RDtnQkFDdkQsSUFBSUMsT0FBT0MsVUFBVSxJQUFJLE1BQU07b0JBQzdCLE1BQU1DLGFBQWFILEVBQUVJLGFBQWEsQ0FBQ0MsYUFBYSxDQUFDO29CQUNqRCxJQUFJRixjQUFjQSxXQUFXRyxTQUFTLENBQUNDLFFBQVEsQ0FBQyxTQUFTO3dCQUN2REosV0FBV0csU0FBUyxDQUFDRSxNQUFNLENBQUM7d0JBQzVCTCxXQUFXdE0sS0FBSyxDQUFDNE0sT0FBTyxHQUFHO3dCQUMzQk4sV0FBV3RNLEtBQUssQ0FBQzZNLFNBQVMsR0FBRzt3QkFDN0JQLFdBQVd0TSxLQUFLLENBQUM4TSxhQUFhLEdBQUc7b0JBQ25DO2dCQUNGO1lBQ0Y7WUFDQUMsY0FBYyxDQUFDWjtnQkFDYix3REFBd0Q7Z0JBQ3hELE1BQU1HLGFBQWFILEVBQUVJLGFBQWEsQ0FBQ0MsYUFBYSxDQUFDO2dCQUNqRCxJQUFJRixjQUFjQSxXQUFXRyxTQUFTLENBQUNDLFFBQVEsQ0FBQyxTQUFTO29CQUN2REosV0FBV0csU0FBUyxDQUFDRSxNQUFNLENBQUM7b0JBQzVCTCxXQUFXdE0sS0FBSyxDQUFDNE0sT0FBTyxHQUFHO29CQUMzQk4sV0FBV3RNLEtBQUssQ0FBQzZNLFNBQVMsR0FBRztvQkFDN0JQLFdBQVd0TSxLQUFLLENBQUM4TSxhQUFhLEdBQUc7Z0JBQ25DO1lBQ0Y7OzhCQUdBLDhEQUFDOUs7b0JBQUlqQyxXQUFVOzhCQUNac0csS0FBS3hHLGdCQUFnQixpQkFDcEIsOERBQUNvQzt3QkFDQ0MsS0FBS21FLEtBQUt4RyxnQkFBZ0I7d0JBQzFCc0MsS0FBS2tFLEtBQUt6RyxLQUFLO3dCQUNmRyxXQUFVOzs7Ozs2Q0FHWiw4REFBQ2lDO3dCQUFJakMsV0FBVTtrQ0FDYiw0RUFBQ2xCLHVWQUFnQkE7NEJBQUNrQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzhCQU1sQyw4REFBQ2lDO29CQUFJakMsV0FBV2dNLFFBQVFpQixPQUFPOztzQ0FFN0IsOERBQUNDOzRCQUFHbE4sV0FBVyxHQUFpQixPQUFkZ00sUUFBUW5NLEtBQUssRUFBQztzQ0FDN0J5RyxLQUFLekcsS0FBSzs7Ozs7O3dCQUlaeUcsS0FBSzZHLE9BQU8sa0JBQ1gsOERBQUNoQzs0QkFBRW5MLFdBQVcsR0FBbUIsT0FBaEJnTSxRQUFRbUIsT0FBTyxFQUFDO3NDQUM5QjdHLEtBQUs2RyxPQUFPOzs7Ozs7c0NBS2IsOERBQUNsTDs0QkFBSWpDLFdBQVdnTSxRQUFRb0IsSUFBSTs7OENBRTFCLDhEQUFDbkw7b0NBQUlqQyxXQUFVOztzREFDYiw4REFBQ3VDOzRDQUFLdkMsV0FBVyxlQUNmc0csT0FEOEIwRixRQUFRNUQsTUFBTSxFQUFDLGdDQUk5QyxPQUhDOUIsS0FBS2tFLFdBQVcsR0FDWixnQ0FDQTtzREFFSGxFLEtBQUtrRSxXQUFXLEdBQUcsY0FBYzs7Ozs7O3NEQUVwQyw4REFBQ2pJOzRDQUFLdkMsV0FBVyxHQUFnQixPQUFiZ00sUUFBUXFCLElBQUksRUFBQztzREFDOUJDLFdBQVdoSCxLQUFLaUgsU0FBUzs7Ozs7Ozs7Ozs7O2dDQUs3QmpILEtBQUtrSCxVQUFVLGtCQUNkLDhEQUFDdkw7b0NBQUlqQyxXQUFXLGtCQUFxQyxPQUFuQmdNLFFBQVF3QixVQUFVOzhDQUNqRGxILEtBQUtrSCxVQUFVLENBQUN6TSxLQUFLLENBQUMsS0FBS00sS0FBSyxDQUFDLEdBQUcsR0FBR0wsR0FBRyxDQUFDLENBQUN5TSxVQUFrQkMsc0JBQzdELDhEQUFDbkw7NENBQWlCdkMsV0FBVyxlQUFnQyxPQUFqQmdNLFFBQVF5QixRQUFRLEVBQUM7c0RBQzFEQSxTQUFTRSxJQUFJOzJDQURMRDs7Ozs7Ozs7Ozs7Ozs7OztzQ0FTbkIsOERBQUN6TDs0QkFBSWpDLFdBQVcscUNBQThILE9BQXpGNEUsWUFBWSxZQUFZLGNBQWNBLFlBQVksYUFBYSxjQUFjLGFBQVk7OzhDQUM1SSw4REFBQzNDO29DQUFJakMsV0FBVTs7d0NBQXdCO3dDQUM1QnNOLFdBQVdoSCxLQUFLaUgsU0FBUzs7Ozs7Ozs4Q0FJcEMsOERBQUN0TDtvQ0FBSWpDLFdBQVU7OENBQ2IsNEVBQUM0Tjt3Q0FDQ3pCLFNBQVMsQ0FBQ0M7Z0RBRVdBOzRDQURuQkEsRUFBRXlCLGVBQWU7NENBQ2pCLE1BQU10QixjQUFhSCwyQkFBQUEsRUFBRUksYUFBYSxDQUFDc0IsT0FBTyxDQUFDLHVCQUF4QjFCLCtDQUFBQSx5QkFBbUNLLGFBQWEsQ0FBQzs0Q0FDcEUsSUFBSUYsWUFBWTtnREFDZCxNQUFNd0IsWUFBWXhCLFdBQVd0TSxLQUFLLENBQUM0TSxPQUFPLEtBQUs7Z0RBQy9DTixXQUFXdE0sS0FBSyxDQUFDNE0sT0FBTyxHQUFHa0IsWUFBWSxNQUFNO2dEQUM3Q3hCLFdBQVd0TSxLQUFLLENBQUM2TSxTQUFTLEdBQUdpQixZQUFZLHFCQUFxQjtnREFDOUR4QixXQUFXdE0sS0FBSyxDQUFDOE0sYUFBYSxHQUFHZ0IsWUFBWSxTQUFTOzRDQUN4RDt3Q0FDRjt3Q0FDQS9OLFdBQVU7d0NBQ1ZILE9BQU07a0RBRU4sNEVBQUNtTzs0Q0FBSWhPLFdBQVU7NENBQVVpTyxNQUFLOzRDQUFPQyxRQUFPOzRDQUFlQyxTQUFRO3NEQUNqRSw0RUFBQ0M7Z0RBQUtDLGVBQWM7Z0RBQVFDLGdCQUFlO2dEQUFRQyxhQUFhO2dEQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT2pGLDhEQUFDdk07NEJBQUlqQyxXQUFXLDhJQU1mLE9BTEM0RSxZQUFZLFlBQ1IsaURBQ0FBLFlBQVksYUFDWiwrQ0FDQTs7OENBR0osOERBQUNnSjtvQ0FDQ3pCLFNBQVMsQ0FBQ0M7d0NBQ1JBLEVBQUV5QixlQUFlO3dDQUNqQnBELGFBQWEsUUFBUW5FO29DQUN2QjtvQ0FDQXRHLFdBQVcsb1RBRVYsT0FEQzRFLFlBQVksWUFBWSxZQUFZQSxZQUFZLGFBQWEsY0FBYztvQ0FFN0UvRSxPQUFNOzhDQUVOLDRFQUFDbEIsdVZBQVVBO3dDQUFDcUIsV0FBVyw2QkFBZ0gsT0FBbkY0RSxZQUFZLFlBQVksWUFBWUEsWUFBWSxhQUFhLFlBQVk7Ozs7Ozs7Ozs7OzhDQUkvSCw4REFBQ2dKO29DQUNDekIsU0FBUyxDQUFDQzt3Q0FDUkEsRUFBRXlCLGVBQWU7d0NBQ2pCcEQsYUFBYSxRQUFRbkU7b0NBQ3ZCO29DQUNBdEcsV0FBVyw4VEFFVixPQURDNEUsWUFBWSxZQUFZLFlBQVlBLFlBQVksYUFBYSxjQUFjO29DQUU3RS9FLE9BQU07OENBRU4sNEVBQUNyQix1VkFBT0E7d0NBQUN3QixXQUFXLDZCQUFnSCxPQUFuRjRFLFlBQVksWUFBWSxZQUFZQSxZQUFZLGFBQWEsWUFBWTs7Ozs7Ozs7Ozs7OENBSTVILDhEQUFDZ0o7b0NBQ0N6QixTQUFTLENBQUNDO3dDQUNSQSxFQUFFeUIsZUFBZTt3Q0FDakJwRCxhQUFhLG9CQUFvQm5FO29DQUNuQztvQ0FDQXRHLFdBQVcsOERBS1Q0RSxPQUpBMEIsS0FBS2tFLFdBQVcsR0FDWixnRkFDQSwyRUFDTCxtS0FFQSxPQURDNUYsWUFBWSxZQUFZLFlBQVlBLFlBQVksYUFBYSxjQUFjO29DQUU3RS9FLE9BQU95RyxLQUFLa0UsV0FBVyxHQUFHLHdCQUF3Qjs4Q0FFakRsRSxLQUFLa0UsV0FBVyxpQkFDZiw4REFBQ2pNLHVWQUFZQTt3Q0FBQ3lCLFdBQVcsNkJBQWdILE9BQW5GNEUsWUFBWSxZQUFZLFlBQVlBLFlBQVksYUFBYSxZQUFZOzs7Ozs2REFFL0gsOERBQUMvRix3VkFBU0E7d0NBQUNtQixXQUFXLDZCQUFnSCxPQUFuRjRFLFlBQVksWUFBWSxZQUFZQSxZQUFZLGFBQWEsWUFBWTs7Ozs7Ozs7Ozs7OENBS2hJLDhEQUFDZ0o7b0NBQ0N6QixTQUFTLENBQUNDO3dDQUNSQSxFQUFFeUIsZUFBZTt3Q0FDakJwRCxhQUFhLGFBQWFuRTtvQ0FDNUI7b0NBQ0F0RyxXQUFXLDhUQUVWLE9BREM0RSxZQUFZLFlBQVksWUFBWUEsWUFBWSxhQUFhLGNBQWM7b0NBRTdFL0UsT0FBTTs4Q0FFTiw0RUFBQ2Qsd1ZBQXFCQTt3Q0FBQ2lCLFdBQVcsNkJBQWdILE9BQW5GNEUsWUFBWSxZQUFZLFlBQVlBLFlBQVksYUFBYSxZQUFZOzs7Ozs7Ozs7Ozs4Q0FJMUksOERBQUNnSjtvQ0FDQ3pCLFNBQVMsQ0FBQ0M7d0NBQ1JBLEVBQUV5QixlQUFlO3dDQUNqQnBELGFBQWEsV0FBV25FO29DQUMxQjtvQ0FDQXRHLFdBQVcsOFRBRVYsT0FEQzRFLFlBQVksWUFBWSxZQUFZQSxZQUFZLGFBQWEsY0FBYztvQ0FFN0UvRSxPQUFNOzhDQUVOLDRFQUFDYix3VkFBY0E7d0NBQUNnQixXQUFXLDZCQUFnSCxPQUFuRjRFLFlBQVksWUFBWSxZQUFZQSxZQUFZLGFBQWEsWUFBWTs7Ozs7Ozs7Ozs7OENBSW5JLDhEQUFDZ0o7b0NBQ0N6QixTQUFTLENBQUNDO3dDQUNSQSxFQUFFeUIsZUFBZTt3Q0FDakJwRCxhQUFhLFVBQVVuRTtvQ0FDekI7b0NBQ0F0RyxXQUFXLCtTQUVWLE9BREM0RSxZQUFZLFlBQVksWUFBWUEsWUFBWSxhQUFhLGNBQWM7b0NBRTdFL0UsT0FBTTs4Q0FFTiw0RUFBQ2pCLHdWQUFTQTt3Q0FBQ29CLFdBQVcsNkJBQWdILE9BQW5GNEUsWUFBWSxZQUFZLFlBQVlBLFlBQVksYUFBYSxZQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozt3QkFNL0huQyxPQUFPZ00saUJBQWlCLGtCQUN2Qiw4REFBQ3hNOzRCQUFJakMsV0FBVyx5QkFBMEMsT0FBakJnTSxRQUFRMEMsUUFBUTtzQ0FDdkQsNEVBQUNDO2dDQUNDQyxNQUFLO2dDQUNMM0QsU0FBU2M7Z0NBQ1Q4QyxVQUFVLENBQUN6QyxJQUFNbEIsaUJBQWlCNUUsS0FBS2tELEVBQUUsRUFBRTRDLEVBQUUwQyxNQUFNLENBQUM3RCxPQUFPO2dDQUMzRGpMLFdBQVcsZ0ZBQWlHLE9BQWpCZ00sUUFBUTBDLFFBQVE7Z0NBQzNHek8sT0FBTztvQ0FBRThPLGlCQUFpQjtnQ0FBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFPaEQ7SUFFQSw4QkFBOEI7SUFDOUIsTUFBTTlDLHdCQUF3QjtRQUM1QixNQUFNakssY0FBYztZQUNsQmdOLFdBQVc7Z0JBQ1RDLFNBQVM7Z0JBQ1RDLGFBQWE7Z0JBQ2JDLFVBQVU7WUFDWjtZQUNBQyxNQUFNO2dCQUNKSCxTQUFTO2dCQUNUQyxhQUFhO2dCQUNiQyxVQUFVO1lBQ1o7WUFDQWpELE1BQU07Z0JBQ0orQyxTQUFTO2dCQUNUQyxhQUFhO2dCQUNiQyxVQUFVO1lBQ1o7WUFDQWxDLFNBQVM7Z0JBQ1BnQyxTQUFTO2dCQUNUQyxhQUFhO2dCQUNiQyxVQUFVO1lBQ1o7WUFDQXRQLE9BQU87Z0JBQ0xvUCxTQUFTO2dCQUNUQyxhQUFhO2dCQUNiQyxVQUFVO1lBQ1o7WUFDQWhDLFNBQVM7Z0JBQ1A4QixTQUFTO2dCQUNUQyxhQUFhO2dCQUNiQyxVQUFVO1lBQ1o7WUFDQS9CLE1BQU07Z0JBQ0o2QixTQUFTO2dCQUNUQyxhQUFhO2dCQUNiQyxVQUFVO1lBQ1o7WUFDQS9HLFFBQVE7Z0JBQ042RyxTQUFTO2dCQUNUQyxhQUFhO2dCQUNiQyxVQUFVO1lBQ1o7WUFDQTlCLE1BQU07Z0JBQ0o0QixTQUFTO2dCQUNUQyxhQUFhO2dCQUNiQyxVQUFVO1lBQ1o7WUFDQTNCLFlBQVk7Z0JBQ1Z5QixTQUFTO2dCQUNUQyxhQUFhO2dCQUNiQyxVQUFVO1lBQ1o7WUFDQTFCLFVBQVU7Z0JBQ1J3QixTQUFTO2dCQUNUQyxhQUFhO2dCQUNiQyxVQUFVO1lBQ1o7WUFDQUUsU0FBUztnQkFDUEosU0FBUztnQkFDVEMsYUFBYTtnQkFDYkMsVUFBVTtZQUNaO1lBQ0FHLFNBQVM7Z0JBQ1BMLFNBQVM7Z0JBQ1RDLGFBQWE7Z0JBQ2JDLFVBQVU7WUFDWjtZQUNBdkIsUUFBUTtnQkFDTnFCLFNBQVM7Z0JBQ1RDLGFBQWE7Z0JBQ2JDLFVBQVU7WUFDWjtZQUNBSSxNQUFNO2dCQUNKTixTQUFTO2dCQUNUQyxhQUFhO2dCQUNiQyxVQUFVO1lBQ1o7WUFDQVQsVUFBVTtnQkFDUk8sU0FBUztnQkFDVEMsYUFBYTtnQkFDYkMsVUFBVTtZQUNaO1FBQ0Y7UUFFQSxPQUFPO1lBQ0xILFdBQVdoTixZQUFZZ04sU0FBUyxDQUFDcEssUUFBUTtZQUN6Q3dLLE1BQU1wTixZQUFZb04sSUFBSSxDQUFDeEssUUFBUTtZQUMvQnNILE1BQU1sSyxZQUFZa0ssSUFBSSxDQUFDdEgsUUFBUTtZQUMvQnFJLFNBQVNqTCxZQUFZaUwsT0FBTyxDQUFDckksUUFBUTtZQUNyQy9FLE9BQU9tQyxZQUFZbkMsS0FBSyxDQUFDK0UsUUFBUTtZQUNqQ3VJLFNBQVNuTCxZQUFZbUwsT0FBTyxDQUFDdkksUUFBUTtZQUNyQ3dJLE1BQU1wTCxZQUFZb0wsSUFBSSxDQUFDeEksUUFBUTtZQUMvQndELFFBQVFwRyxZQUFZb0csTUFBTSxDQUFDeEQsUUFBUTtZQUNuQ3lJLE1BQU1yTCxZQUFZcUwsSUFBSSxDQUFDekksUUFBUTtZQUMvQjRJLFlBQVl4TCxZQUFZd0wsVUFBVSxDQUFDNUksUUFBUTtZQUMzQzZJLFVBQVV6TCxZQUFZeUwsUUFBUSxDQUFDN0ksUUFBUTtZQUN2Q3lLLFNBQVNyTixZQUFZcU4sT0FBTyxDQUFDekssUUFBUTtZQUNyQzBLLFNBQVN0TixZQUFZc04sT0FBTyxDQUFDMUssUUFBUTtZQUNyQ2dKLFFBQVE1TCxZQUFZNEwsTUFBTSxDQUFDaEosUUFBUTtZQUNuQzJLLE1BQU12TixZQUFZdU4sSUFBSSxDQUFDM0ssUUFBUTtZQUMvQjhKLFVBQVUxTSxZQUFZME0sUUFBUSxDQUFDOUosUUFBUTtRQUN6QztJQUNGO0lBRUEsY0FBYztJQUNkLE1BQU0wSSxhQUFhLENBQUNrQztRQUNsQixPQUFPLElBQUlDLEtBQUtELFlBQVlFLGtCQUFrQjtJQUNoRDtJQUVBLGdCQUFnQjtJQUNoQixNQUFNQyxlQUFlLENBQUN6SCxNQUFjMEg7UUFDbEMsSUFBSTFILEtBQUt4RyxNQUFNLElBQUlrTyxXQUFXLE9BQU8xSDtRQUNyQyxPQUFPQSxLQUFLMkgsU0FBUyxDQUFDLEdBQUdELGFBQWE7SUFDeEM7SUFFQSxxQkFDRSw4REFBQzNOO2lEQUFjOzs7Ozs7MEJBeUZiLDhEQUFDQTt5REFBYzs7a0NBQ2IsOERBQUNBO2lFQUFjOzs7Ozs7a0NBRWYsOERBQUNBO2lFQUFjO2tDQUNiLDRFQUFDQTtxRUFBYzs7OENBQ2IsOERBQUNBOzZFQUFjOztzREFDYiw4REFBQ25ELHVWQUFnQkE7NENBQUNrQixXQUFVOzs7Ozs7c0RBQzVCLDhEQUFDaUM7Ozs4REFDQyw4REFBQzZOOzZGQUFhOzhEQUF3Qzs7Ozs7OzhEQUd0RCw4REFBQzNFOzZGQUFZOzhEQUFvQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU1yRCw4REFBQ2xKOzZFQUFjOzhDQUNiLDRFQUFDMkw7d0NBQ0N6QixTQUFTN0M7aUZBQ0M7OzBEQUVWLDhEQUFDbkssd1ZBQVFBO2dEQUFDYSxXQUFVOzs7Ozs7NENBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFVL0MsOERBQUNpQztnQkFFQzhOLE1BQUs7Z0JBQ0xDLGNBQVc7eURBRkQ7MEJBSVYsNEVBQUMvTjtvQkFBMEJoQyxPQUFPO3dCQUFFZ1EsZUFBZTtvQkFBSTs2REFBeEM7O3NDQUViLDhEQUFDaE87cUVBQWM7OzhDQUViLDhEQUFDQTs2RUFBYzs7c0RBRWIsOERBQUNBO3FGQUFjO3NEQUNiLDRFQUFDQTt5RkFBYzs7a0VBQ2IsOERBQUMvQyx3VkFBbUJBO3dEQUFDYyxXQUFVOzs7Ozs7a0VBQy9CLDhEQUFDMk87d0RBQ0NDLE1BQUs7d0RBQ0xzQixhQUFZO3dEQUNaekksT0FBT2xFO3dEQUNQc0wsVUFBVSxDQUFDekMsSUFBTTVJLGVBQWU0SSxFQUFFMEMsTUFBTSxDQUFDckgsS0FBSztpR0FDcEM7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQU1sQiw4REFBQ3hGO3FGQUFjOzs4REFFYiw4REFBQ0E7NkZBQWM7O3NFQUNiLDhEQUFDMkw7NERBQ0N6QixTQUFTLElBQU1aLHFCQUFxQjs0REFNcEMxTCxPQUFNO3FHQUxLLDBFQUlWLE9BSEMyRSxhQUFhLFNBQ1QscUNBQ0E7OzhFQUlOLDhEQUFDcEYsd1ZBQWNBO29FQUFDWSxXQUFVOzs7Ozs7OEVBQzFCLDhEQUFDdUM7NkdBQWU7OEVBQXVDOzs7Ozs7Ozs7Ozs7c0VBRXpELDhEQUFDcUw7NERBQ0N6QixTQUFTLElBQU1aLHFCQUFxQjs0REFNcEMxTCxPQUFNO3FHQUxLLDBFQUlWLE9BSEMyRSxhQUFhLFNBQ1QscUNBQ0E7OzhFQUlOLDhEQUFDbkYsd1ZBQWNBO29FQUFDVyxXQUFVOzs7Ozs7OEVBQzFCLDhEQUFDdUM7NkdBQWU7OEVBQXVDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0RBSzFEaUMsYUFBYSx3QkFDWiw4REFBQ3ZDOzZGQUFjOztzRUFDYiw4REFBQ007cUdBQWU7c0VBQXlDOzs7Ozs7c0VBQ3pELDhEQUFDTjtxR0FBYztzRUFDWjtnRUFBQztnRUFBRztnRUFBRztnRUFBRzs2REFBRSxDQUFDakIsR0FBRyxDQUFDLENBQUNtUCxvQkFDakIsOERBQUN2QztvRUFFQ3pCLFNBQVMsSUFBTVQsd0JBQXdCeUU7b0VBTXZDdFEsT0FBTyxHQUFnQnNRLE9BQWJBLEtBQUksV0FBNEIsT0FBbkJBLE1BQU0sSUFBSSxNQUFNOzZHQUw1QixrREFJVixPQUhDdkssZ0JBQWdCdUssTUFDWixzQ0FDQTs4RUFJTEE7bUVBVElBOzs7Ozs7Ozs7Ozs7Ozs7O2dEQWlCZDNMLGFBQWEsd0JBQ1osOERBQUN2Qzs2RkFBYzs4REFDYiw0RUFBQzJMO3dEQUNDekIsU0FBUyxJQUFNMUcsc0JBQXNCLENBQUNEO3dEQUV0QzNGLE9BQU07aUdBREk7OzBFQUdWLDhEQUFDTCx3VkFBeUJBO2dFQUFDUSxXQUFVOzs7Ozs7MEVBQ3JDLDhEQUFDdUM7eUdBQWU7MEVBQW1COzs7Ozs7MEVBQ25DLDhEQUFDaEQsd1ZBQWVBO2dFQUFDUyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs4REFNakMsOERBQUNpQzs2RkFBYzs4REFDYiw0RUFBQzJMO3dEQUNDekIsU0FBUyxJQUFNOUcsZUFBZSxDQUFDRDt3REFNL0J2RixPQUFNO2lHQUxLLG9HQUlWLE9BSEN1RixlQUFlaUMsT0FBTytJLElBQUksQ0FBQ2xMLFNBQVNtTCxJQUFJLENBQUM3SSxDQUFBQSxNQUFPdEMsT0FBTyxDQUFDc0MsSUFBSSxJQUN4RCw2Q0FDQTs7MEVBSU4sOERBQUNsSSx3VkFBVUE7Z0VBQUNVLFdBQVU7Ozs7OzswRUFDdEIsOERBQUN1Qzt5R0FBZTswRUFBbUI7Ozs7Ozs0REFDbEM4RSxPQUFPK0ksSUFBSSxDQUFDbEwsU0FBU21MLElBQUksQ0FBQzdJLENBQUFBLE1BQU90QyxPQUFPLENBQUNzQyxJQUFJLG1CQUM1Qyw4REFBQ2pGO3lHQUFlOzBFQUNiOEUsT0FBT2lKLE1BQU0sQ0FBQ3BMLFNBQVM2RSxNQUFNLENBQUN3RyxTQUFTN08sTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBT3RELDhEQUFDTzs2RkFBYzs4REFDYiw0RUFBQzJMO3dEQUNDekIsU0FBUyxJQUFNeEcsa0JBQWtCLENBQUNEO3dEQUVsQzdGLE9BQU07aUdBREk7OzBFQUdWLDhEQUFDTCx3VkFBeUJBO2dFQUFDUSxXQUFVOzs7Ozs7MEVBQ3JDLDhEQUFDdUM7eUdBQWU7MEVBQW9CcUMsUUFBUTFELE1BQU0sQ0FBQyxHQUFHRSxXQUFXLEtBQUt3RCxRQUFRdkQsS0FBSyxDQUFDOzs7Ozs7MEVBQ3BGLDhEQUFDOUIsd1ZBQWVBO2dFQUFDUyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FRL0IsOERBQUNpQzs2RUFBYzs7c0RBRWIsOERBQUNBO3FGQUFjOzs4REFDYiw4REFBQ0E7NkZBQWM7O3NFQUNiLDhEQUFDL0Msd1ZBQW1CQTs0REFBQ2MsV0FBVTs7Ozs7O3NFQUMvQiw4REFBQzJPOzREQUNDQyxNQUFLOzREQUNMc0IsYUFBWTs0REFDWnpJLE9BQU9sRTs0REFDUHNMLFVBQVUsQ0FBQ3pDLElBQU01SSxlQUFlNEksRUFBRTBDLE1BQU0sQ0FBQ3JILEtBQUs7cUdBQ3BDOzs7Ozs7Ozs7Ozs7OERBS2QsOERBQUN4Rjs2RkFBYzs7c0VBQ2IsOERBQUMyTDs0REFDQ3pCLFNBQVMsSUFBTTlHLGVBQWUsQ0FBQ0Q7NERBTS9CdkYsT0FBTTtxR0FMSyxnSUFJVixPQUhDdUYsZUFBZWlDLE9BQU8rSSxJQUFJLENBQUNsTCxTQUFTbUwsSUFBSSxDQUFDN0ksQ0FBQUEsTUFBT3RDLE9BQU8sQ0FBQ3NDLElBQUksSUFDeEQsNkNBQ0E7OzhFQUlOLDhEQUFDbEksd1ZBQVVBO29FQUFDVSxXQUFVOzs7Ozs7Z0VBQWlCO2dFQUV0Q3FILE9BQU8rSSxJQUFJLENBQUNsTCxTQUFTbUwsSUFBSSxDQUFDN0ksQ0FBQUEsTUFBT3RDLE9BQU8sQ0FBQ3NDLElBQUksbUJBQzVDLDhEQUFDakY7NkdBQWU7OEVBQ2I4RSxPQUFPaUosTUFBTSxDQUFDcEwsU0FBUzZFLE1BQU0sQ0FBQ3dHLFNBQVM3TyxNQUFNOzs7Ozs7OEVBR2xELDhEQUFDbkMsd1ZBQWVBO29FQUFDUyxXQUFVOzs7Ozs7Ozs7Ozs7d0RBSTVCb0YsNkJBQ0MsOERBQUNuRDtxR0FBYztzRUFDYiw0RUFBQ0E7eUdBQWM7O2tGQUNiLDhEQUFDQTtpSEFBYzs7MEZBQ2IsOERBQUNpTDt5SEFBYTswRkFBb0M7Ozs7OzswRkFDbEQsOERBQUNVO2dGQUNDekIsU0FBUyxJQUFNOUcsZUFBZTt5SEFDcEI7MEZBRVYsNEVBQUNwRyx3VkFBU0E7b0ZBQUNlLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O2tGQUl6Qiw4REFBQ2lDO2lIQUFjO21GQUNaUSxrQkFBQUEsT0FBT3lDLE9BQU8sY0FBZHpDLHNDQUFBQSxnQkFBZ0J6QixHQUFHLENBQUMsQ0FBQytJO2dGQWtCZkE7aUdBakJMLDhEQUFDOUg7OztrR0FDQyw4REFBQ3VPO2lJQUFnQjtrR0FDZHpHLE9BQU95RyxLQUFLOzs7Ozs7a0dBRWYsOERBQUNDO3dGQUNDaEosT0FBT3ZDLE9BQU8sQ0FBQzZFLE9BQU92QyxHQUFHLENBQUMsSUFBSTt3RkFDOUJxSCxVQUFVLENBQUN6Qzs0RkFDVCxNQUFNUCxhQUFhO2dHQUFFLEdBQUczRyxPQUFPOzRGQUFDOzRGQUNoQyxJQUFJa0gsRUFBRTBDLE1BQU0sQ0FBQ3JILEtBQUssRUFBRTtnR0FDbEJvRSxVQUFVLENBQUM5QixPQUFPdkMsR0FBRyxDQUFDLEdBQUc0RSxFQUFFMEMsTUFBTSxDQUFDckgsS0FBSzs0RkFDekMsT0FBTztnR0FDTCxPQUFPb0UsVUFBVSxDQUFDOUIsT0FBT3ZDLEdBQUcsQ0FBQzs0RkFDL0I7NEZBQ0FyQyxXQUFXMEc7d0ZBQ2I7aUlBQ1U7bUdBRVQ5QixrQkFBQUEsT0FBTzJHLE9BQU8sY0FBZDNHLHNDQUFBQSxnQkFBZ0IvSSxHQUFHLENBQUMsQ0FBQzJQLHVCQUNwQiw4REFBQ0E7Z0dBQTBCbEosT0FBT2tKLE9BQU9sSixLQUFLOzswR0FDM0NrSixPQUFPSCxLQUFLOytGQURGRyxPQUFPbEosS0FBSzs7Ozs7Ozs7Ozs7K0VBbEJyQnNDLE9BQU92QyxHQUFHOzs7Ozs7Ozs7OztrRkEyQnhCLDhEQUFDdkY7aUhBQWM7a0ZBQ2IsNEVBQUMyTDs0RUFDQ3pCLFNBQVM7Z0ZBQ1BoSCxXQUFXLENBQUM7Z0ZBQ1pFLGVBQWU7NEVBQ2pCO3FIQUNVO3NGQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQVdiLDhEQUFDcEQ7cUZBQWM7OzhEQUViLDhEQUFDQTs2RkFBYzs7c0VBQ2IsOERBQUNNO3FHQUFlO3NFQUFvQzs7Ozs7O3NFQUNwRCw4REFBQ047cUdBQWM7OzhFQUNiLDhEQUFDMkw7b0VBQ0N6QixTQUFTLElBQU1aLHFCQUFxQjtvRUFNcEMxTCxPQUFNOzZHQUxLLGdEQUlWLE9BSEMyRSxhQUFhLFNBQ1QscUNBQ0E7O3NGQUlOLDhEQUFDcEYsd1ZBQWNBOzRFQUFDWSxXQUFVOzs7Ozs7c0ZBQzFCLDhEQUFDdUM7cUhBQWU7c0ZBQXNCOzs7Ozs7Ozs7Ozs7OEVBRXhDLDhEQUFDcUw7b0VBQ0N6QixTQUFTLElBQU1aLHFCQUFxQjtvRUFNcEMxTCxPQUFNOzZHQUxLLGdEQUlWLE9BSEMyRSxhQUFhLFNBQ1QscUNBQ0E7O3NGQUlOLDhEQUFDbkYsd1ZBQWNBOzRFQUFDVyxXQUFVOzs7Ozs7c0ZBQzFCLDhEQUFDdUM7cUhBQWU7c0ZBQXNCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0RBTTNDaUMsYUFBYSx3QkFDWiw4REFBQ3ZDOzZGQUFjOztzRUFDYiw4REFBQ007cUdBQWU7c0VBQW9DOzs7Ozs7c0VBQ3BELDhEQUFDTjtxR0FBYzs7OEVBQ2IsOERBQUMyTDtvRUFDQ3pCLFNBQVMsSUFBTVQsd0JBQXdCO29FQU12QzdMLE9BQU07NkdBTEsseUNBSVYsT0FIQytGLGdCQUFnQixJQUNaLHNDQUNBOzhFQUdQOzs7Ozs7OEVBR0QsOERBQUNnSTtvRUFDQ3pCLFNBQVMsSUFBTVQsd0JBQXdCO29FQU12QzdMLE9BQU07NkdBTEsseUNBSVYsT0FIQytGLGdCQUFnQixJQUNaLHNDQUNBOzhFQUdQOzs7Ozs7OEVBR0QsOERBQUNnSTtvRUFDQ3pCLFNBQVMsSUFBTVQsd0JBQXdCO29FQU12QzdMLE9BQU07NkdBTEsseUNBSVYsT0FIQytGLGdCQUFnQixJQUNaLHNDQUNBOzhFQUdQOzs7Ozs7OEVBR0QsOERBQUNnSTtvRUFDQ3pCLFNBQVMsSUFBTVQsd0JBQXdCO29FQU12QzdMLE9BQU07NkdBTEsseUNBSVYsT0FIQytGLGdCQUFnQixJQUNaLHNDQUNBOzhFQUdQOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0RBUU5wQixhQUFhLHdCQUNaLDhEQUFDdkM7NkZBQWM7O3NFQUNiLDhEQUFDMkw7NERBQ0N6QixTQUFTLElBQU0xRyxzQkFBc0IsQ0FBQ0Q7NERBRXRDM0YsT0FBTTtxR0FESTs7OEVBR1YsOERBQUNMLHdWQUF5QkE7b0VBQUNRLFdBQVU7Ozs7OztnRUFBaUI7OEVBRXRELDhEQUFDVCx3VkFBZUE7b0VBQUNTLFdBQVU7Ozs7Ozs7Ozs7Ozt3REFJNUJ3RixvQ0FDQyw4REFBQ3ZEO3FHQUFjO3NFQUNiLDRFQUFDQTt5R0FBYzs7a0ZBQ2IsOERBQUNBO2lIQUFjO2tGQUFpRTs7Ozs7O29FQUMvRVEsT0FBT2tKLE9BQU8sQ0FBQzNLLEdBQUcsQ0FBQyxDQUFDNFAsdUJBQ25CLDhEQUFDSjtxSEFBaUM7OzhGQUNoQyw4REFBQzdCO29GQUNDQyxNQUFLO29GQUNMM0QsU0FBU25HLGVBQWVrRixRQUFRLENBQUM0RyxPQUFPcEosR0FBRztvRkFDM0NxSCxVQUFVLENBQUN6Qzt3RkFDVCxJQUFJQSxFQUFFMEMsTUFBTSxDQUFDN0QsT0FBTyxFQUFFOzRGQUNwQmxHLGtCQUFrQjhMLENBQUFBLE9BQVE7dUdBQUlBO29HQUFNRCxPQUFPcEosR0FBRztpR0FBQzt3RkFDakQsT0FBTzs0RkFDTHpDLGtCQUFrQjhMLENBQUFBLE9BQVFBLEtBQUs5RyxNQUFNLENBQUMrRyxDQUFBQSxNQUFPQSxRQUFRRixPQUFPcEosR0FBRzt3RkFDakU7b0ZBQ0Y7NkhBQ1U7Ozs7Ozs4RkFFWiw4REFBQ2pGOzZIQUFlOzhGQUNicU8sT0FBT0osS0FBSzs7Ozs7OzsyRUFkTEksT0FBT3BKLEdBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBeUJsQyw4REFBQ3ZGOzZGQUFjOztzRUFDYiw4REFBQzJMOzREQUNDekIsU0FBUyxJQUFNeEcsa0JBQWtCLENBQUNEOzREQUVsQzdGLE9BQU07cUdBREk7OzhFQUdWLDhEQUFDTCx3VkFBeUJBO29FQUFDUSxXQUFVOzs7Ozs7Z0VBQ3BDNEUsUUFBUTFELE1BQU0sQ0FBQyxHQUFHRSxXQUFXLEtBQUt3RCxRQUFRdkQsS0FBSyxDQUFDOzhFQUNqRCw4REFBQzlCLHdWQUFlQTtvRUFBQ1MsV0FBVTs7Ozs7Ozs7Ozs7O3dEQUk1QjBGLGdDQUNDLDhEQUFDekQ7cUdBQWM7c0VBQ2IsNEVBQUNBO3lHQUFjOzBFQUNaO29FQUFFO29FQUFXO29FQUFlO2lFQUFXLENBQVdqQixHQUFHLENBQUMsQ0FBQzJQLHVCQUN0RCw4REFBQy9DO3dFQUVDekIsU0FBUzs0RUFDUFgsb0JBQW9CbUY7NEVBQ3BCaEwsa0JBQWtCO3dFQUNwQjtpSEFDVyxtRUFFVixPQURDZixZQUFZK0wsU0FBUyx5Q0FBeUM7a0ZBRy9EQSxPQUFPelAsTUFBTSxDQUFDLEdBQUdFLFdBQVcsS0FBS3VQLE9BQU90UCxLQUFLLENBQUM7dUVBVDFDc1A7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQ0FzQnBCdkwsNkJBQ0MsOERBQUNuRDs2RUFBYzs7c0RBQ2IsOERBQUNBO3FGQUFjOzs4REFDYiw4REFBQ2lMOzZGQUFhOzhEQUFvQzs7Ozs7OzhEQUNsRCw4REFBQ1U7b0RBQ0N6QixTQUFTLElBQU05RyxlQUFlOzZGQUNwQjs4REFFViw0RUFBQ3BHLHdWQUFTQTt3REFBQ2UsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBSXpCLDhEQUFDaUM7cUZBQWM7dURBQ1pRLG1CQUFBQSxPQUFPeUMsT0FBTyxjQUFkekMsdUNBQUFBLGlCQUFnQnpCLEdBQUcsQ0FBQyxDQUFDK0k7b0RBa0JmQTtxRUFqQkwsOERBQUM5SDs7O3NFQUNDLDhEQUFDdU87cUdBQWdCO3NFQUNkekcsT0FBT3lHLEtBQUs7Ozs7OztzRUFFZiw4REFBQ0M7NERBQ0NoSixPQUFPdkMsT0FBTyxDQUFDNkUsT0FBT3ZDLEdBQUcsQ0FBQyxJQUFJOzREQUM5QnFILFVBQVUsQ0FBQ3pDO2dFQUNULE1BQU1QLGFBQWE7b0VBQUUsR0FBRzNHLE9BQU87Z0VBQUM7Z0VBQ2hDLElBQUlrSCxFQUFFMEMsTUFBTSxDQUFDckgsS0FBSyxFQUFFO29FQUNsQm9FLFVBQVUsQ0FBQzlCLE9BQU92QyxHQUFHLENBQUMsR0FBRzRFLEVBQUUwQyxNQUFNLENBQUNySCxLQUFLO2dFQUN6QyxPQUFPO29FQUNMLE9BQU9vRSxVQUFVLENBQUM5QixPQUFPdkMsR0FBRyxDQUFDO2dFQUMvQjtnRUFDQXJDLFdBQVcwRzs0REFDYjtxR0FDVTt1RUFFVDlCLGtCQUFBQSxPQUFPMkcsT0FBTyxjQUFkM0csc0NBQUFBLGdCQUFnQi9JLEdBQUcsQ0FBQyxDQUFDMlAsdUJBQ3BCLDhEQUFDQTtvRUFBMEJsSixPQUFPa0osT0FBT2xKLEtBQUs7OzhFQUMzQ2tKLE9BQU9ILEtBQUs7bUVBREZHLE9BQU9sSixLQUFLOzs7Ozs7Ozs7OzttREFsQnJCc0MsT0FBT3ZDLEdBQUc7Ozs7Ozs7Ozs7O3NEQTJCeEIsOERBQUN2RjtxRkFBYztzREFDYiw0RUFBQzJMO2dEQUNDekIsU0FBUztvREFDUGhILFdBQVcsQ0FBQztvREFDWkUsZUFBZTtnREFDakI7eUZBQ1U7MERBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dCQVNWNUMsT0FBT2dNLGlCQUFpQixJQUFJbkssY0FBYzVDLE1BQU0sR0FBRyxLQUFNOEMsQ0FBQUEsYUFBYSxVQUFVQSxhQUFhLE1BQUssbUJBQ2pHLDhEQUFDdkM7cUVBQWM7c0NBQ1gsNEVBQUNBO3lFQUFjOztrREFDYiw4REFBQ0E7aUZBQWM7OzBEQUNiLDhEQUFDQTt5RkFBYzs7a0VBQ2IsOERBQUNBO2lHQUFjO2tFQUNiLDRFQUFDTTtxR0FBZTtzRUFDYitCLGNBQWM1QyxNQUFNOzs7Ozs7Ozs7OztrRUFHekIsOERBQUNhO2lHQUFlOzs0REFBb0M7NERBQ3hDK0IsY0FBYzVDLE1BQU0sS0FBSyxJQUFJLEtBQUs7NERBQUk7Ozs7Ozs7Ozs7Ozs7MERBS3BELDhEQUFDTzt5RkFBYzs7a0VBQ2IsOERBQUMyTDt3REFDQ3pCLFNBQVMsSUFBTTdCLGlCQUFpQjt3REFFaEN6SyxPQUFNO2lHQURJOzswRUFHViw4REFBQ2hCLHdWQUFTQTtnRUFBQ21CLFdBQVU7Ozs7Ozs0REFBaUI7Ozs7Ozs7a0VBSXhDLDhEQUFDNE47d0RBQ0N6QixTQUFTLElBQU03QixpQkFBaUI7d0RBRWhDekssT0FBTTtpR0FESTs7MEVBR1YsOERBQUN0Qix1VkFBWUE7Z0VBQUN5QixXQUFVOzs7Ozs7NERBQWlCOzs7Ozs7O2tFQUkzQyw4REFBQzROO3dEQUNDekIsU0FBUyxJQUFNdEM7d0RBRWZoSyxPQUFNO2lHQURJOzswRUFHViw4REFBQ2pCLHdWQUFTQTtnRUFBQ29CLFdBQVU7Ozs7Ozs0REFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBTzVDLDhEQUFDaUM7aUZBQWM7a0RBQ2IsNEVBQUMyTDs0Q0FDQ3pCLFNBQVMsSUFBTTVILGlCQUFpQixFQUFFOzRDQUVsQzFFLE9BQU07cUZBREk7OzhEQUdWLDhEQUFDWix3VkFBU0E7b0RBQUNlLFdBQVU7Ozs7OztnREFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dCQVMvQ3FELHVCQUNDLDhEQUFDcEI7cUVBQWM7c0NBQ2IsNEVBQUNBO3lFQUFjOzBDQUNiLDRFQUFDQTs2RUFBYzs7c0RBQ2IsOERBQUNBO3FGQUFjO3NEQUNiLDRFQUFDaEQsd1ZBQVNBO2dEQUFDZSxXQUFVOzs7Ozs7Ozs7OztzREFFdkIsOERBQUNpQztxRkFBYzs7OERBQ2IsOERBQUNpTDs2RkFBYTs4REFBbUM7Ozs7Ozs4REFDakQsOERBQUNqTDs2RkFBYzs4REFDYiw0RUFBQ2tKOztrRUFBRzlIOzs7Ozs7Ozs7Ozs4REFFTiw4REFBQ3BCOzZGQUFjOzhEQUNiLDRFQUFDMkw7d0RBQ0N6QixTQUFTOzREQUNQN0ksU0FBUzs0REFDVHNEO3dEQUNGO2lHQUNVO2tFQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBV1p6RCx5QkFDQyw4REFBQ2xCO3FFQUFjO3NDQUNiLDRFQUFDQTt5RUFBYzs7a0RBQ2IsOERBQUNBO2lGQUFjOzs7Ozs7a0RBQ2YsOERBQUNNO2lGQUFlO2tEQUFxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBTTFDLENBQUNZLFdBQVcsQ0FBQ0UsdUJBQ1osOERBQUNwQjs7c0NBQ0VnQixVQUFVdkIsTUFBTSxLQUFLLElBQ3BCLGVBQWUsaUJBQ2YsOERBQUNPO3lFQUFjOztrREFDYiw4REFBQ25ELHVWQUFnQkE7d0NBQUNrQixXQUFVOzs7Ozs7a0RBQzVCLDhEQUFDa047aUZBQWE7a0RBQXlDOzs7Ozs7a0RBQ3ZELDhEQUFDL0I7aUZBQVk7a0RBQ1YxSCx1QkFBdUIscUNBQXFDOzs7Ozs7b0NBRTlEaEIsRUFBQUEsc0JBQUFBLE9BQU9zTyxXQUFXLGNBQWxCdE8sMENBQUFBLG9CQUFvQnVPLE1BQU0sS0FBSSxDQUFDdk4sc0NBQzlCLDhEQUFDeEI7aUZBQWM7a0RBQ2IsNEVBQUMyTDs0Q0FDQ3pCLFNBQVM3QztxRkFDQztzREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozt1Q0FNTDlFLGFBQWEsU0FDZixjQUFjLGlCQUNkLDhEQUFDdkM7eUVBQWM7MENBQ2IsNEVBQUNBOzZFQUFjOzhDQUNiLDRFQUFDZ1A7aUZBQWlCLCtDQUF1RCxPQUFSck07OzBEQUMvRCw4REFBQ3NNO3lGQUFnQjswREFDZiw0RUFBQ0M7Ozt3REFFRTFPLE9BQU9nTSxpQkFBaUIsa0JBQ3ZCLDhEQUFDMkM7NERBQUdDLE9BQU07NERBQXFDcFIsT0FBTztnRUFBRXFSLE9BQU87NERBQU07cUdBQTNDO3NFQUN4Qiw0RUFBQzNDO2dFQUNDQyxNQUFLO2dFQUNMM0QsU0FBUzNHLGNBQWM1QyxNQUFNLEtBQUt1QixVQUFVdkIsTUFBTSxJQUFJdUIsVUFBVXZCLE1BQU0sR0FBRztnRUFDekVtTixVQUFVLENBQUN6QyxJQUFNcEIsZ0JBQWdCb0IsRUFBRTBDLE1BQU0sQ0FBQzdELE9BQU87eUdBQ3RDLDZEQUVWLE9BRENyRyxZQUFZLFlBQVksWUFBWUEsWUFBWSxhQUFhLFlBQVk7Ozs7Ozs7Ozs7O3dEQU9oRnlHLG1CQUFtQnJLLEdBQUcsQ0FBQyxDQUFDa0Qsc0JBQ3ZCLDhEQUFDa047Z0VBRUNDLE9BQU07Z0VBRU5sRixTQUFTLElBQU10QixXQUFXM0csTUFBTXNELEdBQUc7eUdBRHpCOzBFQUdWLDRFQUFDdkY7NkdBQWM7O3NGQUNiLDhEQUFDTTs7c0ZBQU0yQixNQUFNc00sS0FBSzs7Ozs7O3dFQUNqQnpNLFdBQVdHLE1BQU1zRCxHQUFHLEdBQ25CckQsY0FBYyxzQkFDWiw4REFBQzFGLHdWQUFXQTs0RUFBQ3VCLFdBQVU7Ozs7O2lHQUV2Qiw4REFBQ3RCLHdWQUFhQTs0RUFBQ3NCLFdBQVU7Ozs7O2lHQUczQiw4REFBQ3ZCLHdWQUFXQTs0RUFBQ3VCLFdBQVU7Ozs7Ozs7Ozs7OzsrREFkdEJrRSxNQUFNc0QsR0FBRzs7Ozs7d0RBcUJqQi9FLE9BQU80TSxPQUFPLElBQUk1TSxPQUFPNE0sT0FBTyxDQUFDM04sTUFBTSxHQUFHLG1CQUN6Qyw4REFBQzBQOzREQUFHQyxPQUFNO3FHQUFnQjtzRUFDeEIsNEVBQUM5Tzs7MEVBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBS2QsOERBQUNnUDt5RkFBZ0I7MERBQ2R0TyxVQUFVakMsR0FBRyxDQUFDLENBQUNzRixxQkFDZCw4REFBQzZLO2lHQUE0QixvQkFFNUIsT0FEQzdNLGNBQWMwRixRQUFRLENBQUMxRCxLQUFLa0QsRUFBRSxJQUFJLDRDQUE0Qzs7NERBRzdFL0csT0FBT2dNLGlCQUFpQixrQkFDdkIsOERBQUMrQzt5R0FBYTswRUFDWiw0RUFBQzdDO29FQUNDQyxNQUFLO29FQUNMM0QsU0FBUzNHLGNBQWMwRixRQUFRLENBQUMxRCxLQUFLa0QsRUFBRTtvRUFDdkNxRixVQUFVLENBQUN6QyxJQUFNbEIsaUJBQWlCNUUsS0FBS2tELEVBQUUsRUFBRTRDLEVBQUUwQyxNQUFNLENBQUM3RCxPQUFPO29FQUUzRGhMLE9BQU87d0VBQUU4TyxpQkFBaUI7b0VBQVE7NkdBRHhCOzs7Ozs7Ozs7Ozs0REFPZjFELG1CQUFtQnJLLEdBQUcsQ0FBQyxDQUFDa0Qsc0JBQ3ZCLDhEQUFDc047NkdBQTZCOzhFQUMzQnROLE1BQU1zRCxHQUFHLEtBQUssd0JBQ2IsOERBQUN2RjtpSEFBYzs7MEZBQ2IsOERBQUNyQztnRkFDQ0MsT0FBT3lHLEtBQUt6RyxLQUFLO2dGQUNqQkMsa0JBQWtCd0csS0FBS3hHLGdCQUFnQjtnRkFDdkNDLE1BQUs7Z0ZBQ0xDLFdBQVU7Ozs7OzswRkFFWiw4REFBQ2lDOzs7a0dBQ0MsOERBQUNBO2lJQUFjO2tHQUFxQ3FFLEtBQUt6RyxLQUFLOzs7Ozs7a0dBQzlELDhEQUFDb0M7aUlBQWM7a0dBQXlCcUUsS0FBS21MLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7OytFQUduRHZOLE1BQU1zRCxHQUFHLEtBQUssOEJBQ2hCLDhEQUFDakY7aUhBQWdCLDREQUloQixPQUhDK0QsS0FBS2tFLFdBQVcsR0FDWixnQ0FDQTtrRkFFSGxFLEtBQUtrRSxXQUFXLEdBQUcsY0FBYzs7Ozs7K0VBRWxDdEcsTUFBTXNELEdBQUcsS0FBSyxhQUFhdEQsTUFBTXNELEdBQUcsS0FBSywwQkFDM0MsOERBQUN2Rjt3RUFBd0RwQyxPQUFPeUcsSUFBSSxDQUFDcEMsTUFBTXNELEdBQUcsQ0FBQztpSEFBaEU7a0ZBQ1ptSSxhQUFhckosSUFBSSxDQUFDcEMsTUFBTXNELEdBQUcsQ0FBQyxJQUFJLElBQUk7Ozs7OytFQUVyQ3RELE1BQU1zRCxHQUFHLEtBQUssVUFBVXRELE1BQU1zRCxHQUFHLEtBQUssNkJBQ3hDLDhEQUFDdkY7aUhBQWM7a0ZBQ1pxRSxJQUFJLENBQUNwQyxNQUFNc0QsR0FBRyxDQUFDLGlCQUNkLDhEQUFDdkY7cUhBQWM7O2dGQUNYcUUsSUFBSSxDQUFDcEMsTUFBTXNELEdBQUcsQ0FBQyxDQUFZekcsS0FBSyxDQUFDLEtBQUtNLEtBQUssQ0FBQyxHQUFHLEdBQUdMLEdBQUcsQ0FBQyxDQUFDMFEsS0FBYWhFLHNCQUNwRSw4REFBQ25MO2lJQUEyQjtrR0FDekJtUCxJQUFJL0QsSUFBSTt1RkFEQUQ7Ozs7O2dGQUlYcEgsSUFBSSxDQUFDcEMsTUFBTXNELEdBQUcsQ0FBQyxDQUFZekcsS0FBSyxDQUFDLEtBQUtXLE1BQU0sR0FBRyxtQkFDL0MsOERBQUNhOzZIQUFlOzt3RkFBd0I7d0ZBQUcrRCxJQUFJLENBQUNwQyxNQUFNc0QsR0FBRyxDQUFDLENBQVl6RyxLQUFLLENBQUMsS0FBS1csTUFBTSxHQUFHO3dGQUFFOzs7Ozs7Ozs7Ozs7bUZBRzlGOzs7OzsrRUFFSndDLE1BQU1zRCxHQUFHLEtBQUssZUFBZXRELE1BQU1zRCxHQUFHLEtBQUssZUFBZXRELE1BQU1zRCxHQUFHLEtBQUssOEJBQzFFLDhEQUFDdkY7aUhBQWM7a0ZBQ1pxRSxJQUFJLENBQUNwQyxNQUFNc0QsR0FBRyxDQUFDLEdBQUc4RixXQUFXaEgsSUFBSSxDQUFDcEMsTUFBTXNELEdBQUcsQ0FBQyxJQUFjOzs7Ozs2RkFHN0QsOERBQUN2RjtpSEFBYztrRkFDWnFFLElBQUksQ0FBQ3BDLE1BQU1zRCxHQUFHLENBQUMsSUFBSTs7Ozs7O21FQS9DakJ0RCxNQUFNc0QsR0FBRzs7Ozs7NERBc0RuQi9FLE9BQU80TSxPQUFPLElBQUk1TSxPQUFPNE0sT0FBTyxDQUFDM04sTUFBTSxHQUFHLG1CQUN6Qyw4REFBQzhQO3lHQUFhOzBFQUNaLDRFQUFDdlA7NkdBQWM7OEVBQ1pRLE9BQU80TSxPQUFPLENBQUNyTyxHQUFHLENBQUMsQ0FBQ3VKO3dFQUNuQixNQUFNb0gsWUFBWXJNLGtCQUFrQixHQUFvQmdCLE9BQWpCaUUsT0FBT0EsTUFBTSxFQUFDLEtBQVcsT0FBUmpFLEtBQUtrRCxFQUFFO3dFQUMvRCxNQUFNb0ksZ0JBQWdCckgsT0FBT2dGLElBQUksS0FBSyxZQUFZaFIsdVZBQVlBLEdBQ3pDZ00sT0FBT2dGLElBQUksS0FBSyxlQUFlNVEsdVZBQVVBLEdBQ3pDNEwsT0FBT2dGLElBQUksS0FBSyxjQUFjMVEsd1ZBQVNBLEdBQ3ZDMEwsT0FBT2dGLElBQUksS0FBSyxjQUFjM1Esd1ZBQVNBLEdBQUdFLHVWQUFnQkE7d0VBRS9FLHFCQUNFLDhEQUFDOE87NEVBRUN6QixTQUFTLElBQU0xQixhQUFhRixPQUFPQSxNQUFNLEVBQUVqRTs0RUFDM0N1TCxVQUFVRjs0RUFRVjlSLE9BQU8wSyxPQUFPdUgsT0FBTztxSEFQVixvQ0FNUEgsT0FMRnBILE9BQU93SCxPQUFPLEtBQUssWUFBWSxtQ0FDL0J4SCxPQUFPd0gsT0FBTyxLQUFLLGNBQWMsbUNBQ2pDeEgsT0FBT3dILE9BQU8sS0FBSyxZQUFZLHVDQUMvQnhILE9BQU93SCxPQUFPLEtBQUssV0FBVyxpQ0FDOUIsa0NBQ0QsS0FBb0QsT0FBakRKLFlBQVksa0NBQWtDO3NGQUdqREEsMEJBQ0MsOERBQUMxUDt5SEFBYzs7Ozs7cUdBRWYsOERBQUMyUDt5SEFBd0I7Ozs7OzsyRUFmdEJySCxPQUFPQSxNQUFNOzs7OztvRUFtQnhCOzs7Ozs7Ozs7Ozs7dURBdkdDakUsS0FBS2tELEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dUNBaUh4QmhGLGFBQWEsU0FDZiw0QkFBNEIsaUJBQzVCLDhEQUFDdkM7MEVBQWVnSyx3QkFBd0IrQyxTQUFTOzBDQUMvQyw0RUFBQy9NOzZFQUFlLFFBQ2QyRCxPQURzQnFHLHdCQUF3Qm1ELElBQUksRUFBQyxLQUtwRCxPQUpDeEosZ0JBQWdCLElBQUksZ0JBQ3BCQSxnQkFBZ0IsSUFBSSwrQkFDcEJBLGdCQUFnQixJQUFJLDhDQUNwQjs4Q0FFQzNDLFVBQVVqQyxHQUFHLENBQUMsQ0FBQ3NGLHFCQUNkLDhEQUFDd0Y7NENBQXVCeEYsTUFBTUE7MkNBQWZBLEtBQUtrRCxFQUFFOzs7Ozs7Ozs7Ozs7Ozt1Q0FLNUIsYUFBYSxpQkFDYiw4REFBQ3ZIO3lFQUFjOzBDQUNiLDRFQUFDQTs2RUFBYzs4Q0FDWmdCLFVBQVVqQyxHQUFHLENBQUMsQ0FBQ3NGOzRDQXlESDdEOzZEQXhEWCw4REFBQ1I7cUZBQTZCLDBGQUU3QixPQURDcUMsY0FBYzBGLFFBQVEsQ0FBQzFELEtBQUtrRCxFQUFFLElBQUksb0NBQW9DO3NEQUV0RSw0RUFBQ3ZIO3lGQUFjOztrRUFFYiw4REFBQ0E7aUdBQWM7a0VBQ1pxRSxLQUFLeEcsZ0JBQWdCLGlCQUNwQiw4REFBQ29DOzREQUNDQyxLQUFLbUUsS0FBS3hHLGdCQUFnQjs0REFDMUJzQyxLQUFLa0UsS0FBS3pHLEtBQUs7cUdBQ0w7Ozs7O2lGQUdaLDhEQUFDb0M7cUdBQWM7c0VBQ2IsNEVBQUNuRCx1VkFBZ0JBO2dFQUFDa0IsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OztrRUFNbEMsOERBQUNpQztpR0FBYztrRUFDYiw0RUFBQ0E7cUdBQWM7OzhFQUNiLDhEQUFDQTs2R0FBYzs7c0ZBRWIsOERBQUNpTDtxSEFBYTtzRkFDWDVHLEtBQUt6RyxLQUFLOzs7Ozs7c0ZBRWIsOERBQUNzTDtxSEFBWTtzRkFDVjdFLEtBQUttTCxJQUFJOzs7Ozs7d0VBSVhuTCxLQUFLNkcsT0FBTyxrQkFDWCw4REFBQ2hDO3FIQUFZO3NGQUNWN0UsS0FBSzZHLE9BQU87Ozs7OztzRkFLakIsOERBQUNsTDtxSEFBYzs7OEZBQ2IsOERBQUNNOzZIQUFnQiw0REFJaEIsT0FIQytELEtBQUtrRSxXQUFXLEdBQ1osZ0NBQ0E7OEZBRUhsRSxLQUFLa0UsV0FBVyxHQUFHLGNBQWM7Ozs7Ozs4RkFFcEMsOERBQUNqSTs7O3dGQUFLO3dGQUFVK0ssV0FBV2hILEtBQUtpSCxTQUFTOzs7Ozs7O2dGQUN4Q2pILEtBQUtrSCxVQUFVLGtCQUNkLDhEQUFDakw7Ozt3RkFBSzt3RkFBVytELEtBQUtrSCxVQUFVLENBQUN6TSxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQzRNLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBTXpELDhEQUFDMUw7NkdBQWM7O3lFQUNaUSxrQkFBQUEsT0FBTzRNLE9BQU8sY0FBZDVNLHNDQUFBQSxnQkFBZ0J6QixHQUFHLENBQUMsQ0FBQ3VKOzRFQUNwQixNQUFNb0gsWUFBWXJNLGtCQUFrQixHQUFvQmdCLE9BQWpCaUUsT0FBT0EsTUFBTSxFQUFDLEtBQVcsT0FBUmpFLEtBQUtrRCxFQUFFOzRFQUMvRCxNQUFNb0ksZ0JBQWdCckgsT0FBT2dGLElBQUksS0FBSyxZQUFZaFIsdVZBQVlBLEdBQ3pDZ00sT0FBT2dGLElBQUksS0FBSyxlQUFlNVEsdVZBQVVBLEdBQ3pDNEwsT0FBT2dGLElBQUksS0FBSyxjQUFjMVEsd1ZBQVNBLEdBQ3ZDMEwsT0FBT2dGLElBQUksS0FBSyxjQUFjM1Esd1ZBQVNBLEdBQUdFLHVWQUFnQkE7NEVBRS9FLHFCQUNFLDhEQUFDOE87Z0ZBRUN6QixTQUFTLElBQU0xQixhQUFhRixPQUFPQSxNQUFNLEVBQUVqRTtnRkFDM0N1TCxVQUFVRjtnRkFRVjlSLE9BQU8wSyxPQUFPdUgsT0FBTzt5SEFQVixvQ0FNUEgsT0FMRnBILE9BQU93SCxPQUFPLEtBQUssWUFBWSxtQ0FDL0J4SCxPQUFPd0gsT0FBTyxLQUFLLGNBQWMsbUNBQ2pDeEgsT0FBT3dILE9BQU8sS0FBSyxZQUFZLHVDQUMvQnhILE9BQU93SCxPQUFPLEtBQUssV0FBVyxpQ0FDOUIsa0NBQ0QsS0FBb0QsT0FBakRKLFlBQVksa0NBQWtDOzBGQUdqREEsMEJBQ0MsOERBQUMxUDs2SEFBYzs7Ozs7eUdBRWYsOERBQUMyUDs2SEFBd0I7Ozs7OzsrRUFmdEJySCxPQUFPQSxNQUFNOzs7Ozt3RUFtQnhCO3dFQUdDOUgsT0FBT2dNLGlCQUFpQixrQkFDdkIsOERBQUNFOzRFQUNDQyxNQUFLOzRFQUNMM0QsU0FBUzNHLGNBQWMwRixRQUFRLENBQUMxRCxLQUFLa0QsRUFBRTs0RUFDdkNxRixVQUFVLENBQUN6QyxJQUFNbEIsaUJBQWlCNUUsS0FBS2tELEVBQUUsRUFBRTRDLEVBQUUwQyxNQUFNLENBQUM3RCxPQUFPOzRFQUUzRGhMLE9BQU87Z0ZBQUU4TyxpQkFBaUI7NEVBQVE7cUhBRHhCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsyQ0E1RmR6SSxLQUFLa0QsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBK0duQyw4REFBQy9KLGtEQUFTQTtnQkFDUjRHLFFBQVFQO2dCQUNSa00sU0FBUyxJQUFNak0scUJBQXFCO2dCQUNwQ2tNLFVBQVVuSjtnQkFDVmpKLE9BQU07Ozs7OzswQkFHUiw4REFBQ0osa0RBQVNBO2dCQUNSNEcsUUFBUUw7Z0JBQ1JnTSxTQUFTO29CQUNQL0wsbUJBQW1CO29CQUNuQkUsZUFBZTtnQkFDakI7Z0JBQ0E4TCxVQUFVLE9BQU9sSjtvQkFDZixJQUFJN0MsYUFBYTt3QkFDZixNQUFNcUQsYUFBYXJELFlBQVlzRCxFQUFFLEVBQUVUO29CQUNyQztnQkFDRjtnQkFDQWxKLE9BQU07Z0JBQ05xUyxhQUFhaE0sd0JBQUFBLHlCQUFBQSxjQUFlaU07Ozs7OzswQkFJOUIsOERBQUN4Uyx5RUFBaUJBO2dCQUNoQjBHLFFBQVEzRCxtQkFBbUIyRCxNQUFNO2dCQUNqQ3hHLE9BQU07Z0JBQ04rSSxTQUFRO2dCQUNSd0osU0FBUyxDQUFDO29CQUNSLElBQUkxUCxtQkFBbUI2RCxZQUFZLEVBQUU7NEJBQ3JCN0Q7d0JBQWQsTUFBTTJQLFFBQVEzUCxFQUFBQSxnQ0FBQUEsbUJBQW1COEQsU0FBUyxjQUE1QjlELG9EQUFBQSw4QkFBOEJoQixNQUFNLEtBQUk7d0JBQ3RELE9BQU8sdUNBQXlEMlEsT0FBbEJBLE9BQU0sY0FBbUMsT0FBdkJBLFVBQVUsSUFBSSxLQUFLLEtBQUk7b0JBQ3pGO29CQUNBLE1BQU0vTCxPQUFPNUQsbUJBQW1CNEQsSUFBSTtvQkFDcEMsT0FBTyx3Q0FBd0UsT0FBaENBLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTXpHLEtBQUssS0FBSSxrQkFBaUI7Z0JBQ2pGO2dCQUNBeVMsYUFBWTtnQkFDWkMsWUFBVztnQkFDWEMsV0FBV3ZJO2dCQUNYd0ksVUFBVXBJO2dCQUNWdUUsTUFBSztnQkFDTDhELGtCQUFrQjtnQkFDbEJDLGtCQUFrQjtvQkFDaEJDLFdBQVc7b0JBQ1hDLFFBQVFuUSxtQkFBbUI2RCxZQUFZLEdBQ25DLEdBQXlELE9BQXREN0QsRUFBQUEsZ0NBQUFBLG1CQUFtQjhELFNBQVMsY0FBNUI5RCxvREFBQUEsOEJBQThCaEIsTUFBTSxLQUFJLEdBQUUsY0FBeUUsT0FBN0QsQ0FBQ2dCLEVBQUFBLGlDQUFBQSxtQkFBbUI4RCxTQUFTLGNBQTVCOUQscURBQUFBLCtCQUE4QmhCLE1BQU0sS0FBSSxPQUFPLElBQUksS0FBSyxLQUFJLDRCQUN0SCxjQUEwRCxPQUE1Q2dCLEVBQUFBLDJCQUFBQSxtQkFBbUI0RCxJQUFJLGNBQXZCNUQsK0NBQUFBLHlCQUF5QjdDLEtBQUssS0FBSSxXQUFVO2dCQUNoRTs7Ozs7Ozs7Ozs7O0FBSVI7SUFsMERnQjJDOztRQUVvRjlDLHlGQUFnQkE7OztNQUZwRzhDIiwic291cmNlcyI6WyIvVm9sdW1lcy9GaWxlcy9UZWNobm9sb3dheS1OZXctV2Vic2l0ZS9UZWNobm9sb3dheS9zcmMvY29tcG9uZW50cy9hZG1pbi9ibG9nL2Jsb2dzLW1hbmFnZW1lbnQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7XG4gIEV5ZVNsYXNoSWNvbixcbiAgRXllSWNvbixcbiAgQXJyb3dVcEljb24sXG4gIEFycm93RG93bkljb24sXG4gIFBlbmNpbEljb24sXG4gIFRyYXNoSWNvbixcbiAgUG93ZXJJY29uLFxuICBEb2N1bWVudFRleHRJY29uLFxuICBEb2N1bWVudER1cGxpY2F0ZUljb24sXG4gIEFyY2hpdmVCb3hJY29uLFxuICBDYWxlbmRhckljb24sXG4gIFRhZ0ljb24sXG4gIFhNYXJrSWNvbixcbiAgTWFnbmlmeWluZ0dsYXNzSWNvbixcbiAgUGx1c0ljb24sXG4gIExpc3RCdWxsZXRJY29uLFxuICBTcXVhcmVzMlgySWNvbixcbiAgUmVjdGFuZ2xlU3RhY2tJY29uLFxuICBGdW5uZWxJY29uLFxuICBDaGV2cm9uRG93bkljb24sXG4gIEFkanVzdG1lbnRzSG9yaXpvbnRhbEljb24sXG4gIEVsbGlwc2lzVmVydGljYWxJY29uLFxufSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnO1xuaW1wb3J0IHsgQmxvZ01vZGFsIH0gZnJvbSAnLi9ibG9nLW1vZGFsJztcbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZU5vdGlmaWNhdGlvbnMgfSBmcm9tICdAL2NvbXBvbmVudHMvcHJvdmlkZXJzL25vdGlmaWNhdGlvbi1wcm92aWRlcic7XG5pbXBvcnQgeyBDb25maXJtYXRpb25Nb2RhbCB9IGZyb20gJy4uL3NoYXJlZC9jb25maXJtYXRpb24tbW9kYWwnO1xuXG4vLyBJbmxpbmUgdHlwZSBkZWZpbml0aW9ucyAoZXh0cmFjdGVkIGZyb20gY3J1ZC90eXBlcy50cylcbmludGVyZmFjZSBDcnVkQ29sdW1uPFQgPSBhbnk+IHtcbiAga2V5OiBrZXlvZiBUIHwgc3RyaW5nXG4gIGxhYmVsOiBzdHJpbmdcbiAgc29ydGFibGU/OiBib29sZWFuXG4gIHNlYXJjaGFibGU/OiBib29sZWFuXG4gIHJlbmRlclR5cGU/OiAndGV4dCcgfCAnZW1haWwnIHwgJ2RhdGUnIHwgJ2N1cnJlbmN5JyB8ICdib29sZWFuJyB8ICdzdGF0dXMnIHwgJ3JhdGluZycgfCAncHJvZ3Jlc3MnIHwgJ2ltYWdlJyB8ICdjdXN0b20nIHwgJ2NvbXBhbnknIHwgJ251bWJlcidcbiAgcmVuZGVyRnVuY3Rpb24/OiAodmFsdWU6IGFueSwgcm93OiBUKSA9PiBSZWFjdC5SZWFjdE5vZGVcbiAgcmVuZGVyUHJvcHM/OiBhbnlcbiAgd2lkdGg/OiBzdHJpbmdcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG4gIGhpZGVhYmxlPzogYm9vbGVhblxuICBkZWZhdWx0VmlzaWJsZT86IGJvb2xlYW5cbn1cblxuaW50ZXJmYWNlIENydWRGaWVsZCB7XG4gIGtleTogc3RyaW5nXG4gIGxhYmVsOiBzdHJpbmdcbiAgbmFtZT86IHN0cmluZ1xuICB0eXBlOiAndGV4dCcgfCAnZW1haWwnIHwgJ3Bhc3N3b3JkJyB8ICdudW1iZXInIHwgJ3RleHRhcmVhJyB8ICdzZWxlY3QnIHwgJ2NoZWNrYm94JyB8ICdib29sZWFuJyB8ICdyYWRpbycgfCAnZGF0ZScgfCAnZGF0ZXRpbWUtbG9jYWwnIHwgJ3VybCcgfCAnZmlsZSdcbiAgcmVxdWlyZWQ/OiBib29sZWFuXG4gIHNlYXJjaGFibGU/OiBib29sZWFuXG4gIHBsYWNlaG9sZGVyPzogc3RyaW5nXG4gIGRlZmF1bHRWYWx1ZT86IGFueVxuICBvcHRpb25zPzogQXJyYXk8eyB2YWx1ZTogc3RyaW5nOyBsYWJlbDogc3RyaW5nIH0+XG4gIHZhbGlkYXRpb24/OiB7XG4gICAgbWluTGVuZ3RoPzogbnVtYmVyXG4gICAgbWF4TGVuZ3RoPzogbnVtYmVyXG4gICAgbWluPzogbnVtYmVyXG4gICAgbWF4PzogbnVtYmVyXG4gICAgcGF0dGVybj86IFJlZ0V4cFxuICAgIHN0ZXA/OiBudW1iZXJcbiAgfVxuICByb3dzPzogbnVtYmVyXG4gIG11bHRpcGxlPzogYm9vbGVhblxuICBhY2NlcHQ/OiBzdHJpbmdcbn1cblxuaW50ZXJmYWNlIENydWRGaWx0ZXIge1xuICBrZXk6IHN0cmluZ1xuICBsYWJlbDogc3RyaW5nXG4gIG5hbWU/OiBzdHJpbmdcbiAgdHlwZTogJ3RleHQnIHwgJ3NlbGVjdCcgfCAnZGF0ZScgfCAnZGF0ZXJhbmdlJyB8ICdjaGVja2JveCdcbiAgb3B0aW9ucz86IEFycmF5PHsgdmFsdWU6IHN0cmluZzsgbGFiZWw6IHN0cmluZyB9PlxuICBwbGFjZWhvbGRlcj86IHN0cmluZ1xufVxuXG5pbnRlcmZhY2UgRm9ybVNlY3Rpb24ge1xuICB0aXRsZTogc3RyaW5nXG4gIGZpZWxkczogc3RyaW5nW11cbn1cblxuaW50ZXJmYWNlIEZvcm1MYXlvdXQge1xuICB0eXBlOiAnY29tcGFjdCcgfCAnZnVsbCdcbiAgY29sdW1uczogbnVtYmVyXG4gIHNlY3Rpb25zOiBGb3JtU2VjdGlvbltdXG59XG5cbmludGVyZmFjZSBDcnVkQWN0aW9uPFQgPSBhbnk+IHtcbiAgbGFiZWw6IHN0cmluZ1xuICBpY29uOiBzdHJpbmdcbiAgYWN0aW9uOiAnZWRpdCcgfCAnZGVsZXRlJyB8ICd2aWV3JyB8ICdwcmV2aWV3JyB8ICd0b2dnbGUtc3RhdHVzJyB8ICd0b2dnbGUtcHVibGlzaGVkJyB8ICd0b2dnbGUtZmVhdHVyZWQnIHwgJ2N1c3RvbSdcbiAgY3VzdG9tQWN0aW9uPzogc3RyaW5nXG4gIHZhcmlhbnQ/OiAncHJpbWFyeScgfCAnc2Vjb25kYXJ5JyB8ICdkYW5nZXInIHwgJ3N1Y2Nlc3MnIHwgJ3dhcm5pbmcnXG4gIGRpc2FibGVkPzogYm9vbGVhblxuICBoaWRkZW4/OiBib29sZWFuXG4gIHJlcXVpcmVzQ29uZmlybWF0aW9uPzogYm9vbGVhblxuICBjb25maXJtYXRpb25NZXNzYWdlPzogc3RyaW5nXG4gIHRvb2x0aXA/OiBzdHJpbmdcbn1cblxuaW50ZXJmYWNlIENydWRCdWxrQWN0aW9uPFQgPSBhbnk+IHtcbiAgbGFiZWw6IHN0cmluZ1xuICBpY29uPzogc3RyaW5nXG4gIGFjdGlvbjogc3RyaW5nXG4gIHZhcmlhbnQ/OiAncHJpbWFyeScgfCAnc2Vjb25kYXJ5JyB8ICdkYW5nZXInIHwgJ3N1Y2Nlc3MnIHwgJ3dhcm5pbmcnXG4gIHJlcXVpcmVzQ29uZmlybWF0aW9uPzogYm9vbGVhblxuICBjb25maXJtYXRpb25NZXNzYWdlPzogc3RyaW5nXG59XG5cbmludGVyZmFjZSBDcnVkUGVybWlzc2lvbnMge1xuICBjcmVhdGU6IGJvb2xlYW5cbiAgcmVhZDogYm9vbGVhblxuICB1cGRhdGU6IGJvb2xlYW5cbiAgZGVsZXRlOiBib29sZWFuXG4gIGV4cG9ydDogYm9vbGVhblxufVxuXG50eXBlIFZpZXdNb2RlID0gJ2xpc3QnIHwgJ2dyaWQnXG50eXBlIERpc3BsYXlEZW5zaXR5ID0gJ2NvbXBhY3QnIHwgJ2NvbWZvcnRhYmxlJ1xuXG5pbnRlcmZhY2UgVmlld1NldHRpbmdzIHtcbiAgbW9kZTogVmlld01vZGVcbiAgZGVuc2l0eTogRGlzcGxheURlbnNpdHlcbiAgdmlzaWJsZUNvbHVtbnM6IHN0cmluZ1tdXG59XG5cbmludGVyZmFjZSBDcnVkQ29uZmlnPFQgPSBhbnk+IHtcbiAgdGl0bGU6IHN0cmluZ1xuICBkZXNjcmlwdGlvbj86IHN0cmluZ1xuICBlbmRwb2ludDogc3RyaW5nXG4gIGNvbHVtbnM6IENydWRDb2x1bW48VD5bXVxuICBmaWVsZHM6IENydWRGaWVsZFtdXG4gIGZpbHRlcnM/OiBDcnVkRmlsdGVyW11cbiAgYWN0aW9ucz86IENydWRBY3Rpb248VD5bXVxuICBidWxrQWN0aW9ucz86IENydWRCdWxrQWN0aW9uPFQ+W11cbiAgcGVybWlzc2lvbnM6IENydWRQZXJtaXNzaW9uc1xuICBzZWFyY2hQbGFjZWhvbGRlcj86IHN0cmluZ1xuICBkZWZhdWx0U29ydD86IHsgZmllbGQ6IHN0cmluZzsgZGlyZWN0aW9uOiAnYXNjJyB8ICdkZXNjJyB9XG4gIHBhZ2VTaXplPzogbnVtYmVyXG4gIGVuYWJsZVNlYXJjaD86IGJvb2xlYW5cbiAgZW5hYmxlRmlsdGVycz86IGJvb2xlYW5cbiAgZW5hYmxlQnVsa0FjdGlvbnM/OiBib29sZWFuXG4gIGVuYWJsZUV4cG9ydD86IGJvb2xlYW5cbiAgZW5hYmxlVmlld0NvbnRyb2xzPzogYm9vbGVhblxuICBlbmFibGVEZW5zaXR5Q29udHJvbHM/OiBib29sZWFuXG4gIGVuYWJsZUNvbHVtblZpc2liaWxpdHk/OiBib29sZWFuXG4gIGRlZmF1bHRWaWV3U2V0dGluZ3M/OiBQYXJ0aWFsPFZpZXdTZXR0aW5ncz5cbiAgZm9ybUxheW91dD86IEZvcm1MYXlvdXRcbiAgY3VzdG9tQ3JlYXRlQnV0dG9uPzogUmVhY3ROb2RlXG4gIGN1c3RvbUhlYWRlcj86IFJlYWN0Tm9kZVxuICBjdXN0b21Gb290ZXI/OiBSZWFjdE5vZGVcbiAgb25JdGVtQ2xpY2s/OiBib29sZWFuXG59XG5cbmludGVyZmFjZSBCbG9nUG9zdCB7XG4gIGlkOiBzdHJpbmc7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIHNsdWc6IHN0cmluZztcbiAgY29udGVudDogc3RyaW5nO1xuICBleGNlcnB0Pzogc3RyaW5nO1xuICBmZWF0dXJlZEltYWdlVXJsPzogc3RyaW5nO1xuICBhdXRob3JJZD86IHN0cmluZztcbiAgaXNQdWJsaXNoZWQ6IGJvb2xlYW47XG4gIHB1Ymxpc2hlZEF0Pzogc3RyaW5nO1xuICBjYXRlZ29yaWVzPzogc3RyaW5nO1xuICB0YWdzPzogc3RyaW5nO1xuICBjcmVhdGVkQXQ6IHN0cmluZztcbiAgdXBkYXRlZEF0OiBzdHJpbmc7XG4gIFtrZXk6IHN0cmluZ106IGFueTtcbn1cblxuaW50ZXJmYWNlIEJsb2dNYW5hZ2VyUHJvcHMge1xuICBjb25maWc6IENydWRDb25maWc8QmxvZ1Bvc3Q+O1xufVxuXG4vLyBCbG9nQXZhdGFyIENvbXBvbmVudFxuaW50ZXJmYWNlIEJsb2dBdmF0YXJQcm9wcyB7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIGZlYXR1cmVkSW1hZ2VVcmw/OiBzdHJpbmcgfCBudWxsO1xuICBzaXplPzogJ3hzJyB8ICdzbScgfCAnbWQnIHwgJ2xnJyB8ICd4bCcgfCAnZnVsbC1oZWlnaHQnO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG4gIHN0eWxlPzogUmVhY3QuQ1NTUHJvcGVydGllcztcbn1cblxuZnVuY3Rpb24gQmxvZ0F2YXRhcih7IFxuICB0aXRsZSwgXG4gIGZlYXR1cmVkSW1hZ2VVcmwsIFxuICBzaXplID0gJ21kJywgXG4gIGNsYXNzTmFtZSA9ICcnLFxuICBzdHlsZSA9IHt9XG59OiBCbG9nQXZhdGFyUHJvcHMpIHtcbiAgY29uc3QgW2ltYWdlRXJyb3IsIHNldEltYWdlRXJyb3JdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaW1hZ2VMb2FkaW5nLCBzZXRJbWFnZUxvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG5cbiAgLy8gU2l6ZSBjb25maWd1cmF0aW9uc1xuICBjb25zdCBzaXplQ2xhc3NlcyA9IHtcbiAgICB4czogJ3ctNiBoLTYnLFxuICAgIHNtOiAndy04IGgtOCcsXG4gICAgbWQ6ICd3LTEyIGgtMTInLFxuICAgIGxnOiAndy0xNiBoLTE2JyxcbiAgICB4bDogJ3ctMjQgaC0yNCcsXG4gICAgJ2Z1bGwtaGVpZ2h0JzogJ3ctZnVsbCBoLWZ1bGwnXG4gIH07XG5cbiAgY29uc3QgaWNvblNpemVzID0ge1xuICAgIHhzOiAndy0zIGgtMycsXG4gICAgc206ICd3LTQgaC00JyxcbiAgICBtZDogJ3ctNiBoLTYnLFxuICAgIGxnOiAndy04IGgtOCcsXG4gICAgeGw6ICd3LTEyIGgtMTInLFxuICAgICdmdWxsLWhlaWdodCc6ICd3LTE2IGgtMTYnXG4gIH07XG5cbiAgY29uc3QgdGV4dFNpemVzID0ge1xuICAgIHhzOiAndGV4dC14cycsXG4gICAgc206ICd0ZXh0LXNtJyxcbiAgICBtZDogJ3RleHQtYmFzZScsXG4gICAgbGc6ICd0ZXh0LWxnJyxcbiAgICB4bDogJ3RleHQteGwnLFxuICAgICdmdWxsLWhlaWdodCc6ICd0ZXh0LTR4bCdcbiAgfTtcblxuICAvLyBHZW5lcmF0ZSBpbml0aWFscyBmcm9tIGJsb2cgdGl0bGVcbiAgY29uc3QgZ2V0SW5pdGlhbHMgPSAodGl0bGU6IHN0cmluZykgPT4ge1xuICAgIHJldHVybiB0aXRsZVxuICAgICAgLnNwbGl0KCcgJylcbiAgICAgIC5tYXAod29yZCA9PiB3b3JkLmNoYXJBdCgwKSlcbiAgICAgIC5qb2luKCcnKVxuICAgICAgLnRvVXBwZXJDYXNlKClcbiAgICAgIC5zbGljZSgwLCAyKTtcbiAgfTtcblxuICAvLyBHZW5lcmF0ZSBhIGNvbnNpc3RlbnQgY29sb3IgYmFzZWQgb24gdGhlIGJsb2cgdGl0bGVcbiAgY29uc3QgZ2V0QmFja2dyb3VuZENvbG9yID0gKHRpdGxlOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBjb2xvcnMgPSBbXG4gICAgICAnYmctYmx1ZS01MDAnLFxuICAgICAgJ2JnLWdyZWVuLTUwMCcsXG4gICAgICAnYmctcHVycGxlLTUwMCcsXG4gICAgICAnYmctcGluay01MDAnLFxuICAgICAgJ2JnLWluZGlnby01MDAnLFxuICAgICAgJ2JnLXllbGxvdy01MDAnLFxuICAgICAgJ2JnLXJlZC01MDAnLFxuICAgICAgJ2JnLXRlYWwtNTAwJyxcbiAgICAgICdiZy1vcmFuZ2UtNTAwJyxcbiAgICAgICdiZy1jeWFuLTUwMCdcbiAgICBdO1xuICAgIFxuICAgIGxldCBoYXNoID0gMDtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRpdGxlLmxlbmd0aDsgaSsrKSB7XG4gICAgICBoYXNoID0gdGl0bGUuY2hhckNvZGVBdChpKSArICgoaGFzaCA8PCA1KSAtIGhhc2gpO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gY29sb3JzW01hdGguYWJzKGhhc2gpICUgY29sb3JzLmxlbmd0aF07XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlSW1hZ2VMb2FkID0gKCkgPT4ge1xuICAgIHNldEltYWdlTG9hZGluZyhmYWxzZSk7XG4gICAgc2V0SW1hZ2VFcnJvcihmYWxzZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlSW1hZ2VFcnJvciA9ICgpID0+IHtcbiAgICBzZXRJbWFnZUxvYWRpbmcoZmFsc2UpO1xuICAgIHNldEltYWdlRXJyb3IodHJ1ZSk7XG4gIH07XG5cbiAgY29uc3QgYmFzZUNsYXNzZXMgPSBgXG4gICAgJHtzaXplQ2xhc3Nlc1tzaXplXX0gXG4gICAgcm91bmRlZC1sZyBcbiAgICBmbGV4IFxuICAgIGl0ZW1zLWNlbnRlciBcbiAgICBqdXN0aWZ5LWNlbnRlciBcbiAgICBvdmVyZmxvdy1oaWRkZW4gXG4gICAgJHtzaXplID09PSAnZnVsbC1oZWlnaHQnID8gJ21pbi1oLVszMjBweF0nIDogJyd9XG4gICAgJHtjbGFzc05hbWV9XG4gIGA7XG5cbiAgLy8gSWYgd2UgaGF2ZSBhIHZhbGlkIGZlYXR1cmVkIGltYWdlIFVSTCBhbmQgbm8gZXJyb3IsIHNob3cgdGhlIGltYWdlXG4gIGlmIChmZWF0dXJlZEltYWdlVXJsICYmICFpbWFnZUVycm9yKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgXG4gICAgICAgIGNsYXNzTmFtZT17YCR7YmFzZUNsYXNzZXN9IGJnLWdyYXktMTAwIHJlbGF0aXZlYH1cbiAgICAgICAgc3R5bGU9e3N0eWxlfVxuICAgICAgPlxuICAgICAgICB7aW1hZ2VMb2FkaW5nICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctZ3JheS0xMDBcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBib3JkZXItMiBib3JkZXItZ3JheS0zMDAgYm9yZGVyLXQtYmx1ZS02MDAgdy02IGgtNlwiPjwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgICA8aW1nXG4gICAgICAgICAgc3JjPXtmZWF0dXJlZEltYWdlVXJsfVxuICAgICAgICAgIGFsdD17YCR7dGl0bGV9IGZlYXR1cmVkIGltYWdlYH1cbiAgICAgICAgICBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICR7c2l6ZSA9PT0gJ2Z1bGwtaGVpZ2h0JyA/ICd3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlcicgOiAndy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXInfVxuICAgICAgICAgICAgJHtpbWFnZUxvYWRpbmcgPyAnb3BhY2l0eS0wJyA6ICdvcGFjaXR5LTEwMCd9XG4gICAgICAgICAgICB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMjAwXG4gICAgICAgICAgYH1cbiAgICAgICAgICBvbkxvYWQ9e2hhbmRsZUltYWdlTG9hZH1cbiAgICAgICAgICBvbkVycm9yPXtoYW5kbGVJbWFnZUVycm9yfVxuICAgICAgICAvPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIC8vIEZhbGxiYWNrOiBTaG93IGluaXRpYWxzIHdpdGggY29sb3JlZCBiYWNrZ3JvdW5kXG4gIHJldHVybiAoXG4gICAgPGRpdiBcbiAgICAgIGNsYXNzTmFtZT17YFxuICAgICAgICAke2Jhc2VDbGFzc2VzfSBcbiAgICAgICAgJHtnZXRCYWNrZ3JvdW5kQ29sb3IodGl0bGUpfSBcbiAgICAgICAgdGV4dC13aGl0ZSBcbiAgICAgICAgZm9udC1zZW1pYm9sZCBcbiAgICAgICAgJHt0ZXh0U2l6ZXNbc2l6ZV19XG4gICAgICAgIHNoYWRvdy1zbVxuICAgICAgYH1cbiAgICAgIHN0eWxlPXtzdHlsZX1cbiAgICAgIHRpdGxlPXt0aXRsZX1cbiAgICA+XG4gICAgICB7c2l6ZSA9PT0gJ2Z1bGwtaGVpZ2h0JyA/IChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzcGFjZS15LTRcIj5cbiAgICAgICAgICA8RG9jdW1lbnRUZXh0SWNvbiBjbGFzc05hbWU9XCJ3LTI0IGgtMjQgdGV4dC13aGl0ZSBvcGFjaXR5LTgwXCIgLz5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB4LTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIG1iLTJcIj57Z2V0SW5pdGlhbHModGl0bGUpfTwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIG9wYWNpdHktOTAgYnJlYWstd29yZHMgbGluZS1jbGFtcC0zXCI+e3RpdGxlfTwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICkgOiAoXG4gICAgICAgIDxzcGFuPntnZXRJbml0aWFscyh0aXRsZSl9PC9zcGFuPlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIEJsb2dzTWFuYWdlbWVudCh7IGNvbmZpZyB9OiBCbG9nTWFuYWdlclByb3BzKSB7XG4gIC8vIE5vdGlmaWNhdGlvbiBzeXN0ZW1cbiAgY29uc3QgeyBzaG93U3VjY2Vzcywgc2hvd0Vycm9yLCBzaG93V2FybmluZywgc2hvd0luZm8sIHNob3dMb2FkaW5nLCBjbGVhckxvYWRpbmdOb3RpZmljYXRpb25zIH0gPSB1c2VOb3RpZmljYXRpb25zKCk7XG4gIFxuICAvLyBTdGF0ZSBtYW5hZ2VtZW50XG4gIGNvbnN0IFtibG9nUG9zdHMsIHNldEJsb2dQb3N0c10gPSB1c2VTdGF0ZTxCbG9nUG9zdFtdPihbXSk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc2VhcmNoUXVlcnksIHNldFNlYXJjaFF1ZXJ5XSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2RlYm91bmNlZFNlYXJjaFF1ZXJ5LCBzZXREZWJvdW5jZWRTZWFyY2hRdWVyeV0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtjdXJyZW50UGFnZSwgc2V0Q3VycmVudFBhZ2VdID0gdXNlU3RhdGUoMSk7XG4gIGNvbnN0IFt0b3RhbFBhZ2VzLCBzZXRUb3RhbFBhZ2VzXSA9IHVzZVN0YXRlKDEpO1xuICBjb25zdCBbc29ydEJ5LCBzZXRTb3J0QnldID0gdXNlU3RhdGUoY29uZmlnLmRlZmF1bHRTb3J0Py5maWVsZCB8fCAndXBkYXRlZEF0Jyk7XG4gIGNvbnN0IFtzb3J0T3JkZXIsIHNldFNvcnRPcmRlcl0gPSB1c2VTdGF0ZTwnYXNjJyB8ICdkZXNjJz4oXG4gICAgY29uZmlnLmRlZmF1bHRTb3J0Py5kaXJlY3Rpb24gPT09ICdhc2MnID8gJ2FzYycgOiAnZGVzYydcbiAgKTtcbiAgY29uc3QgW3NlbGVjdGVkUG9zdHMsIHNldFNlbGVjdGVkUG9zdHNdID0gdXNlU3RhdGU8c3RyaW5nW10+KFtdKTtcbiAgY29uc3QgW3ZpZXdNb2RlLCBzZXRWaWV3TW9kZV0gPSB1c2VTdGF0ZTwnbGlzdCcgfCAnZ3JpZCc+KFxuICAgIGNvbmZpZy5kZWZhdWx0Vmlld1NldHRpbmdzPy5tb2RlID09PSAnbGlzdCcgfHxcbiAgICBjb25maWcuZGVmYXVsdFZpZXdTZXR0aW5ncz8ubW9kZSA9PT0gJ2dyaWQnXG4gICAgICA/IGNvbmZpZy5kZWZhdWx0Vmlld1NldHRpbmdzLm1vZGVcbiAgICAgIDogJ2xpc3QnXG4gICk7XG4gIGNvbnN0IFtkZW5zaXR5LCBzZXREZW5zaXR5XSA9IHVzZVN0YXRlPCdjb21wYWN0JyB8ICdjb21mb3J0YWJsZScgfCAnc3BhY2lvdXMnPihcbiAgICBjb25maWcuZGVmYXVsdFZpZXdTZXR0aW5ncz8uZGVuc2l0eSA9PT0gJ2NvbXBhY3QnIHx8XG4gICAgY29uZmlnLmRlZmF1bHRWaWV3U2V0dGluZ3M/LmRlbnNpdHkgPT09ICdjb21mb3J0YWJsZScgfHxcbiAgICBjb25maWcuZGVmYXVsdFZpZXdTZXR0aW5ncz8uZGVuc2l0eSA9PT0gJ3NwYWNpb3VzJ1xuICAgICAgPyAoY29uZmlnLmRlZmF1bHRWaWV3U2V0dGluZ3MuZGVuc2l0eSBhcyAnY29tcGFjdCcgfCAnY29tZm9ydGFibGUnIHwgJ3NwYWNpb3VzJylcbiAgICAgIDogJ2NvbWZvcnRhYmxlJ1xuICApO1xuXG4gIGNvbnN0IFt2aXNpYmxlQ29sdW1ucywgc2V0VmlzaWJsZUNvbHVtbnNdID0gdXNlU3RhdGU8c3RyaW5nW10+KFxuICAgIEFycmF5LmlzQXJyYXkoY29uZmlnLmRlZmF1bHRWaWV3U2V0dGluZ3M/LnZpc2libGVDb2x1bW5zKVxuICAgICAgPyBjb25maWcuZGVmYXVsdFZpZXdTZXR0aW5ncy52aXNpYmxlQ29sdW1uc1xuICAgICAgOiBbXVxuICApO1xuICBjb25zdCBbZmlsdGVycywgc2V0RmlsdGVyc10gPSB1c2VTdGF0ZTxSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+Pih7fSk7XG4gIGNvbnN0IFtzaG93RmlsdGVycywgc2V0U2hvd0ZpbHRlcnNdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbYWN0aW9uTG9hZGluZywgc2V0QWN0aW9uTG9hZGluZ10gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3Nob3dDb2x1bW5TZWxlY3Rvciwgc2V0U2hvd0NvbHVtblNlbGVjdG9yXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dXaW5kb3dMaXN0LCBzZXRTaG93V2luZG93TGlzdF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtncmlkQ29sdW1ucywgc2V0R3JpZENvbHVtbnNdID0gdXNlU3RhdGUoMyk7XG5cbiAgLy8gTW9kYWwgc3RhdGVzXG4gIGNvbnN0IFtpc0NyZWF0ZU1vZGFsT3Blbiwgc2V0SXNDcmVhdGVNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNFZGl0TW9kYWxPcGVuLCBzZXRJc0VkaXRNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZWRpdGluZ1Bvc3QsIHNldEVkaXRpbmdQb3N0XSA9IHVzZVN0YXRlPEJsb2dQb3N0IHwgbnVsbD4obnVsbCk7XG4gIFxuICAvLyBDb25maXJtYXRpb24gbW9kYWwgc3RhdGVcbiAgY29uc3QgW2RlbGV0ZUNvbmZpcm1hdGlvbiwgc2V0RGVsZXRlQ29uZmlybWF0aW9uXSA9IHVzZVN0YXRlPHtcbiAgICBpc09wZW46IGJvb2xlYW47XG4gICAgcG9zdDogQmxvZ1Bvc3QgfCBudWxsO1xuICAgIGlzQnVsa0RlbGV0ZT86IGJvb2xlYW47XG4gICAgYnVsa1Bvc3RzPzogQmxvZ1Bvc3RbXTtcbiAgfT4oe1xuICAgIGlzT3BlbjogZmFsc2UsXG4gICAgcG9zdDogbnVsbCxcbiAgICBpc0J1bGtEZWxldGU6IGZhbHNlLFxuICAgIGJ1bGtQb3N0czogW11cbiAgfSk7XG5cbiAgLy8gRGVib3VuY2Ugc2VhcmNoIHF1ZXJ5XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIHNldERlYm91bmNlZFNlYXJjaFF1ZXJ5KHNlYXJjaFF1ZXJ5KTtcbiAgICAgIHNldEN1cnJlbnRQYWdlKDEpOyAvLyBSZXNldCB0byBmaXJzdCBwYWdlIHdoZW4gc2VhcmNoaW5nXG4gICAgfSwgMzAwKTtcblxuICAgIHJldHVybiAoKSA9PiBjbGVhclRpbWVvdXQodGltZXIpO1xuICB9LCBbc2VhcmNoUXVlcnldKTtcblxuICAvLyBGZXRjaCBibG9nIHBvc3RzXG4gIGNvbnN0IGZldGNoQmxvZ1Bvc3RzID0gYXN5bmMgKHByZXNlcnZlRm9jdXMgPSBmYWxzZSkgPT4ge1xuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgaWYgKCFwcmVzZXJ2ZUZvY3VzKSB7XG4gICAgICBzaG93TG9hZGluZygnTG9hZGluZyBCbG9nIFBvc3RzJywgJ1JldHJpZXZpbmcgYmxvZyBwb3N0cy4uLicpO1xuICAgIH1cbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyh7XG4gICAgICAgIHBhZ2U6IGN1cnJlbnRQYWdlLnRvU3RyaW5nKCksXG4gICAgICAgIGxpbWl0OiAoY29uZmlnLnBhZ2VTaXplIHx8IDEwKS50b1N0cmluZygpLFxuICAgICAgICBzZWFyY2g6IGRlYm91bmNlZFNlYXJjaFF1ZXJ5LFxuICAgICAgICBzb3J0QnksXG4gICAgICAgIHNvcnRPcmRlcixcbiAgICAgIH0pO1xuXG4gICAgICAvLyBBZGQgZmlsdGVycyB0byBwYXJhbXNcbiAgICAgIE9iamVjdC5lbnRyaWVzKGZpbHRlcnMpLmZvckVhY2goKFtrZXksIHZhbHVlXSkgPT4ge1xuICAgICAgICBpZiAodmFsdWUpIHtcbiAgICAgICAgICBwYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICB9KTtcblxuICAgICAgY29uc29sZS5sb2coJ0ZldGNoaW5nIGJsb2cgcG9zdHMgd2l0aCBwYXJhbXM6JywgcGFyYW1zLnRvU3RyaW5nKCkpOyAvLyBEZWJ1ZyBsb2dcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9hZG1pbi8ke2NvbmZpZy5lbmRwb2ludH0/JHtwYXJhbXN9YCk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZXJyb3JUZXh0ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xuICAgICAgICBjb25zb2xlLmVycm9yKCdBUEkgRXJyb3IgUmVzcG9uc2U6JywgZXJyb3JUZXh0KTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBIVFRQIGVycm9yISBzdGF0dXM6ICR7cmVzcG9uc2Uuc3RhdHVzfSAtICR7ZXJyb3JUZXh0fWApO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgY29uc29sZS5sb2coJ1JlY2VpdmVkIGJsb2cgcG9zdHMgZGF0YTonLCBkYXRhKTsgLy8gRGVidWcgbG9nXG5cbiAgICAgIGlmICghZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihkYXRhLmVycm9yIHx8ICdGYWlsZWQgdG8gZmV0Y2ggYmxvZyBwb3N0cycpO1xuICAgICAgfVxuXG4gICAgICBzZXRCbG9nUG9zdHMoZGF0YS5wb3N0cyB8fCBbXSk7XG4gICAgICBzZXRUb3RhbFBhZ2VzKE1hdGguY2VpbCgoZGF0YS50b3RhbENvdW50IHx8IDApIC8gKGNvbmZpZy5wYWdlU2l6ZSB8fCAxMCkpKTtcbiAgICAgIHNldEVycm9yKG51bGwpOyAvLyBDbGVhciBhbnkgcHJldmlvdXMgZXJyb3JzIG9uIHN1Y2Nlc3NmdWwgZmV0Y2hcbiAgICAgIFxuICAgICAgaWYgKCFwcmVzZXJ2ZUZvY3VzKSB7XG4gICAgICAgIHNob3dTdWNjZXNzKCdCbG9nIFBvc3RzIExvYWRlZCcsIGBMb2FkZWQgJHtkYXRhLnBvc3RzPy5sZW5ndGggfHwgMH0gYmxvZyBwb3N0JHtkYXRhLnBvc3RzPy5sZW5ndGggPT09IDEgPyAnJyA6ICdzJ31gKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGJsb2cgcG9zdHM6JywgZXJyKTsgLy8gRGVidWcgbG9nXG4gICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ0ZhaWxlZCB0byBmZXRjaCBibG9nIHBvc3RzJyk7XG4gICAgICBpZiAoIXByZXNlcnZlRm9jdXMpIHtcbiAgICAgICAgc2hvd0Vycm9yKCdGYWlsZWQgdG8gTG9hZCBCbG9nIFBvc3RzJywgJ1VuYWJsZSB0byByZXRyaWV2ZSBibG9nIHBvc3RzJyk7XG4gICAgICB9XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFByZXNlcnZlIGZvY3VzIHdoZW4gc2VhcmNoaW5nXG4gICAgY29uc3QgaXNTZWFyY2hpbmcgPSBkZWJvdW5jZWRTZWFyY2hRdWVyeSAhPT0gJyc7XG4gICAgZmV0Y2hCbG9nUG9zdHMoaXNTZWFyY2hpbmcpO1xuICB9LCBbY3VycmVudFBhZ2UsIGRlYm91bmNlZFNlYXJjaFF1ZXJ5LCBzb3J0QnksIHNvcnRPcmRlciwgZmlsdGVyc10pO1xuXG4gIC8vIEhhbmRsZSBjcmVhdGVcbiAgY29uc3QgaGFuZGxlQ3JlYXRlID0gYXN5bmMgKGZvcm1EYXRhOiBhbnkpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2hvd0xvYWRpbmcoJ0NyZWF0aW5nIEJsb2cgUG9zdCcsICdTYXZpbmcgbmV3IGJsb2cgcG9zdC4uLicpO1xuICAgICAgXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL2FkbWluLyR7Y29uZmlnLmVuZHBvaW50fWAsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShmb3JtRGF0YSksXG4gICAgICB9KTtcblxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIFxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZGF0YS5lcnJvciB8fCBgRmFpbGVkIHRvIGNyZWF0ZSBibG9nIHBvc3QgKCR7cmVzcG9uc2Uuc3RhdHVzfSlgKTtcbiAgICAgIH1cblxuICAgICAgaWYgKCFkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGRhdGEuZXJyb3IgfHwgJ0ZhaWxlZCB0byBjcmVhdGUgYmxvZyBwb3N0Jyk7XG4gICAgICB9XG5cbiAgICAgIHNldElzQ3JlYXRlTW9kYWxPcGVuKGZhbHNlKTtcbiAgICAgIGZldGNoQmxvZ1Bvc3RzKCk7XG4gICAgICBzaG93U3VjY2VzcygnQmxvZyBQb3N0IENyZWF0ZWQnLCBgXCIke2Zvcm1EYXRhLnRpdGxlIHx8ICdOZXcgYmxvZyBwb3N0J31cIiBjcmVhdGVkIHN1Y2Nlc3NmdWxseSFgKTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIGNyZWF0ZSBibG9nIHBvc3QnO1xuICAgICAgc2V0RXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICAgIHNob3dFcnJvcignRmFpbGVkIHRvIENyZWF0ZSBCbG9nIFBvc3QnLCBlcnJvck1lc3NhZ2UpO1xuICAgICAgdGhyb3cgZXJyO1xuICAgIH1cbiAgfTtcblxuICAvLyBIYW5kbGUgYWRkIGJ1dHRvbiBjbGlja1xuICBjb25zdCBoYW5kbGVBZGRDbGljayA9ICgpID0+IHtcbiAgICBzaG93SW5mbygnT3BlbmluZyBDcmVhdGUgRm9ybScsICdQcmVwYXJpbmcgdG8gY3JlYXRlIGEgbmV3IGJsb2cgcG9zdC4uLicpO1xuICAgIHNldElzQ3JlYXRlTW9kYWxPcGVuKHRydWUpO1xuICB9O1xuXG4gIC8vIEhhbmRsZSB1cGRhdGVcbiAgY29uc3QgaGFuZGxlVXBkYXRlID0gYXN5bmMgKGlkOiBzdHJpbmcsIGZvcm1EYXRhOiBhbnkpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2hvd0xvYWRpbmcoJ1VwZGF0aW5nIEJsb2cgUG9zdCcsICdTYXZpbmcgY2hhbmdlcy4uLicpO1xuICAgICAgXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL2FkbWluLyR7Y29uZmlnLmVuZHBvaW50fS8ke2lkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnUFVUJyxcbiAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KGZvcm1EYXRhKSxcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihkYXRhLmVycm9yIHx8IGBGYWlsZWQgdG8gdXBkYXRlIGJsb2cgcG9zdCAoJHtyZXNwb25zZS5zdGF0dXN9KWApO1xuICAgICAgfVxuXG4gICAgICBpZiAoIWRhdGEuc3VjY2Vzcykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZGF0YS5lcnJvciB8fCAnRmFpbGVkIHRvIHVwZGF0ZSBibG9nIHBvc3QnKTtcbiAgICAgIH1cblxuICAgICAgc2V0SXNFZGl0TW9kYWxPcGVuKGZhbHNlKTtcbiAgICAgIHNldEVkaXRpbmdQb3N0KG51bGwpO1xuICAgICAgZmV0Y2hCbG9nUG9zdHMoKTtcbiAgICAgIHNob3dTdWNjZXNzKCdCbG9nIFBvc3QgVXBkYXRlZCcsIGBcIiR7Zm9ybURhdGEudGl0bGUgfHwgJ0Jsb2cgcG9zdCd9XCIgdXBkYXRlZCBzdWNjZXNzZnVsbHkhYCk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ0ZhaWxlZCB0byB1cGRhdGUgYmxvZyBwb3N0JztcbiAgICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgICBzaG93RXJyb3IoJ0ZhaWxlZCB0byBVcGRhdGUgQmxvZyBQb3N0JywgZXJyb3JNZXNzYWdlKTtcbiAgICAgIHRocm93IGVycjtcbiAgICB9XG4gIH07XG5cbiAgLy8gSGFuZGxlIGRlbGV0ZVxuICBjb25zdCBoYW5kbGVEZWxldGUgPSBhc3luYyAoaWQ6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBwb3N0VG9EZWxldGUgPSBibG9nUG9zdHMuZmluZChwb3N0ID0+IHBvc3QuaWQgPT09IGlkKTtcbiAgICAgIHNob3dMb2FkaW5nKCdEZWxldGluZyBCbG9nIFBvc3QnLCBgUmVtb3ZpbmcgXCIke3Bvc3RUb0RlbGV0ZT8udGl0bGUgfHwgJ2Jsb2cgcG9zdCd9XCIuLi5gKTtcbiAgICAgIFxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9hZG1pbi8ke2NvbmZpZy5lbmRwb2ludH0vJHtpZH1gLCB7XG4gICAgICAgIG1ldGhvZDogJ0RFTEVURScsXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZGVsZXRlIGJsb2cgcG9zdCcpO1xuXG4gICAgICBmZXRjaEJsb2dQb3N0cygpO1xuICAgICAgc2hvd1N1Y2Nlc3MoJ0Jsb2cgUG9zdCBEZWxldGVkJywgYFwiJHtwb3N0VG9EZWxldGU/LnRpdGxlIHx8ICdCbG9nIHBvc3QnfVwiIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5IWApO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdGYWlsZWQgdG8gZGVsZXRlIGJsb2cgcG9zdCc7XG4gICAgICBzZXRFcnJvcihlcnJvck1lc3NhZ2UpO1xuICAgICAgc2hvd0Vycm9yKCdGYWlsZWQgdG8gRGVsZXRlIEJsb2cgUG9zdCcsIGVycm9yTWVzc2FnZSk7XG4gICAgICB0aHJvdyBlcnI7XG4gICAgfVxuICB9O1xuXG4gIC8vIFNob3cgZGVsZXRlIGNvbmZpcm1hdGlvblxuICBjb25zdCBzaG93RGVsZXRlQ29uZmlybWF0aW9uID0gKHBvc3Q6IEJsb2dQb3N0KSA9PiB7XG4gICAgc2V0RGVsZXRlQ29uZmlybWF0aW9uKHtcbiAgICAgIGlzT3BlbjogdHJ1ZSxcbiAgICAgIHBvc3QsXG4gICAgICBpc0J1bGtEZWxldGU6IGZhbHNlLFxuICAgICAgYnVsa1Bvc3RzOiBbXVxuICAgIH0pO1xuICB9O1xuXG4gIC8vIFNob3cgYnVsayBkZWxldGUgY29uZmlybWF0aW9uXG4gIGNvbnN0IHNob3dCdWxrRGVsZXRlQ29uZmlybWF0aW9uID0gKCkgPT4ge1xuICAgIGNvbnN0IHBvc3RzVG9EZWxldGUgPSBibG9nUG9zdHMuZmlsdGVyKHBvc3QgPT4gc2VsZWN0ZWRQb3N0cy5pbmNsdWRlcyhwb3N0LmlkKSk7XG4gICAgc2V0RGVsZXRlQ29uZmlybWF0aW9uKHtcbiAgICAgIGlzT3BlbjogdHJ1ZSxcbiAgICAgIHBvc3Q6IG51bGwsXG4gICAgICBpc0J1bGtEZWxldGU6IHRydWUsXG4gICAgICBidWxrUG9zdHM6IHBvc3RzVG9EZWxldGVcbiAgICB9KTtcbiAgfTtcblxuICAvLyBDb25maXJtIGRlbGV0ZVxuICBjb25zdCBjb25maXJtRGVsZXRlID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBpZiAoZGVsZXRlQ29uZmlybWF0aW9uLmlzQnVsa0RlbGV0ZSAmJiBkZWxldGVDb25maXJtYXRpb24uYnVsa1Bvc3RzKSB7XG4gICAgICAgIC8vIEJ1bGsgZGVsZXRlXG4gICAgICAgIGNvbnN0IHByb21pc2VzID0gZGVsZXRlQ29uZmlybWF0aW9uLmJ1bGtQb3N0cy5tYXAocG9zdCA9PiBcbiAgICAgICAgICBmZXRjaChgL2FwaS9hZG1pbi8ke2NvbmZpZy5lbmRwb2ludH0vJHtwb3N0LmlkfWAsIHsgbWV0aG9kOiAnREVMRVRFJyB9KVxuICAgICAgICApO1xuICAgICAgICBhd2FpdCBQcm9taXNlLmFsbChwcm9taXNlcyk7XG4gICAgICAgIHNldFNlbGVjdGVkUG9zdHMoW10pO1xuICAgICAgICBmZXRjaEJsb2dQb3N0cygpO1xuICAgICAgICBzaG93U3VjY2VzcygnQmxvZyBQb3N0cyBEZWxldGVkJywgYCR7ZGVsZXRlQ29uZmlybWF0aW9uLmJ1bGtQb3N0cy5sZW5ndGh9IGJsb2cgcG9zdChzKSBkZWxldGVkIHN1Y2Nlc3NmdWxseSFgKTtcbiAgICAgIH0gZWxzZSBpZiAoZGVsZXRlQ29uZmlybWF0aW9uLnBvc3QpIHtcbiAgICAgICAgLy8gU2luZ2xlIGRlbGV0ZVxuICAgICAgICBhd2FpdCBoYW5kbGVEZWxldGUoZGVsZXRlQ29uZmlybWF0aW9uLnBvc3QuaWQpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdGYWlsZWQgdG8gZGVsZXRlIGJsb2cgcG9zdChzKSc7XG4gICAgICBzaG93RXJyb3IoJ0ZhaWxlZCB0byBEZWxldGUgQmxvZyBQb3N0KHMpJywgZXJyb3JNZXNzYWdlKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0RGVsZXRlQ29uZmlybWF0aW9uKHtcbiAgICAgICAgaXNPcGVuOiBmYWxzZSxcbiAgICAgICAgcG9zdDogbnVsbCxcbiAgICAgICAgaXNCdWxrRGVsZXRlOiBmYWxzZSxcbiAgICAgICAgYnVsa1Bvc3RzOiBbXVxuICAgICAgfSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIENhbmNlbCBkZWxldGVcbiAgY29uc3QgY2FuY2VsRGVsZXRlID0gKCkgPT4ge1xuICAgIHNldERlbGV0ZUNvbmZpcm1hdGlvbih7XG4gICAgICBpc09wZW46IGZhbHNlLFxuICAgICAgcG9zdDogbnVsbCxcbiAgICAgIGlzQnVsa0RlbGV0ZTogZmFsc2UsXG4gICAgICBidWxrUG9zdHM6IFtdXG4gICAgfSk7XG4gIH07XG5cbiAgLy8gSGFuZGxlIGJ1bGsgYWN0aW9uc1xuICBjb25zdCBoYW5kbGVCdWxrQWN0aW9uID0gYXN5bmMgKGFjdGlvbjogc3RyaW5nKSA9PiB7XG4gICAgaWYgKHNlbGVjdGVkUG9zdHMubGVuZ3RoID09PSAwKSByZXR1cm47XG5cbiAgICBzZXRBY3Rpb25Mb2FkaW5nKGFjdGlvbik7XG4gICAgdHJ5IHtcbiAgICAgIGlmIChhY3Rpb24gPT09ICdkZWxldGUnKSB7XG4gICAgICAgIHNob3dCdWxrRGVsZXRlQ29uZmlybWF0aW9uKCk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgc2hvd0xvYWRpbmcoYEJ1bGsgJHthY3Rpb259YCwgYFByb2Nlc3NpbmcgJHtzZWxlY3RlZFBvc3RzLmxlbmd0aH0gYmxvZyBwb3N0JHtzZWxlY3RlZFBvc3RzLmxlbmd0aCA9PT0gMSA/ICcnIDogJ3MnfS4uLmApO1xuXG4gICAgICBjb25zdCBwcm9taXNlcyA9IHNlbGVjdGVkUG9zdHMubWFwKGFzeW5jIChpZCkgPT4ge1xuICAgICAgICBzd2l0Y2ggKGFjdGlvbikge1xuICAgICAgICAgIGNhc2UgJ3B1Ymxpc2gnOlxuICAgICAgICAgICAgcmV0dXJuIGZldGNoKGAvYXBpL2FkbWluLyR7Y29uZmlnLmVuZHBvaW50fS8ke2lkfWAsIHtcbiAgICAgICAgICAgICAgbWV0aG9kOiAnUFVUJyxcbiAgICAgICAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgaXNQdWJsaXNoZWQ6IHRydWUgfSksXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICBjYXNlICd1bnB1Ymxpc2gnOlxuICAgICAgICAgICAgcmV0dXJuIGZldGNoKGAvYXBpL2FkbWluLyR7Y29uZmlnLmVuZHBvaW50fS8ke2lkfWAsIHtcbiAgICAgICAgICAgICAgbWV0aG9kOiAnUFVUJyxcbiAgICAgICAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgaXNQdWJsaXNoZWQ6IGZhbHNlIH0pLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgVW5rbm93biBidWxrIGFjdGlvbjogJHthY3Rpb259YCk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICBhd2FpdCBQcm9taXNlLmFsbChwcm9taXNlcyk7XG4gICAgICBzZXRTZWxlY3RlZFBvc3RzKFtdKTtcbiAgICAgIGZldGNoQmxvZ1Bvc3RzKCk7XG4gICAgICBzaG93U3VjY2VzcyhgQnVsayAke2FjdGlvbn0gY29tcGxldGVkYCwgYCR7c2VsZWN0ZWRQb3N0cy5sZW5ndGh9IGJsb2cgcG9zdCR7c2VsZWN0ZWRQb3N0cy5sZW5ndGggPT09IDEgPyAnJyA6ICdzJ30gJHthY3Rpb259ZWQgc3VjY2Vzc2Z1bGx5IWApO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6IGBGYWlsZWQgdG8gJHthY3Rpb259IGJsb2cgcG9zdHNgO1xuICAgICAgc2V0RXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICAgIHNob3dFcnJvcihgRmFpbGVkIHRvICR7YWN0aW9ufSBibG9nIHBvc3RzYCwgZXJyb3JNZXNzYWdlKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0QWN0aW9uTG9hZGluZyhudWxsKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gSGFuZGxlIGluZGl2aWR1YWwgYWN0aW9uc1xuICBjb25zdCBoYW5kbGVBY3Rpb24gPSBhc3luYyAoYWN0aW9uOiBzdHJpbmcsIGl0ZW06IEJsb2dQb3N0KSA9PiB7XG4gICAgc2V0QWN0aW9uTG9hZGluZyhgJHthY3Rpb259LSR7aXRlbS5pZH1gKTtcbiAgICB0cnkge1xuICAgICAgc3dpdGNoIChhY3Rpb24pIHtcbiAgICAgICAgY2FzZSAnZWRpdCc6XG4gICAgICAgICAgc2hvd0luZm8oJ09wZW5pbmcgRWRpdG9yJywgYEVkaXRpbmcgXCIke2l0ZW0udGl0bGV9XCJgKTtcbiAgICAgICAgICBzZXRFZGl0aW5nUG9zdChpdGVtKTtcbiAgICAgICAgICBzZXRJc0VkaXRNb2RhbE9wZW4odHJ1ZSk7XG4gICAgICAgICAgYnJlYWs7XG5cbiAgICAgICAgY2FzZSAndmlldyc6XG4gICAgICAgICAgc2hvd0luZm8oJ09wZW5pbmcgVmlldycsIGBWaWV3aW5nIFwiJHtpdGVtLnRpdGxlfVwiYCk7XG4gICAgICAgICAgLy8gVE9ETzogSW1wbGVtZW50IHZpZXcgZnVuY3Rpb25hbGl0eVxuICAgICAgICAgIHNob3dJbmZvKCdWaWV3IEJsb2cgUG9zdCcsIGBPcGVuaW5nIFwiJHtpdGVtLnRpdGxlfVwiIGluIG5ldyB0YWJgKTtcbiAgICAgICAgICBicmVhaztcblxuICAgICAgICBjYXNlICdkZWxldGUnOlxuICAgICAgICAgIHNob3dJbmZvKCdEZWxldGUgQ29uZmlybWF0aW9uJywgYFByZXBhcmluZyB0byBkZWxldGUgXCIke2l0ZW0udGl0bGV9XCJgKTtcbiAgICAgICAgICBzaG93RGVsZXRlQ29uZmlybWF0aW9uKGl0ZW0pO1xuICAgICAgICAgIGJyZWFrO1xuXG4gICAgICAgIGNhc2UgJ3RvZ2dsZS1wdWJsaXNoZWQnOlxuICAgICAgICAgIGNvbnN0IG5ld1N0YXR1cyA9ICFpdGVtLmlzUHVibGlzaGVkO1xuICAgICAgICAgIHNob3dMb2FkaW5nKFxuICAgICAgICAgICAgbmV3U3RhdHVzID8gJ1B1Ymxpc2hpbmcgQmxvZyBQb3N0JyA6ICdVbnB1Ymxpc2hpbmcgQmxvZyBQb3N0JyxcbiAgICAgICAgICAgIGAke25ld1N0YXR1cyA/ICdQdWJsaXNoaW5nJyA6ICdVbnB1Ymxpc2hpbmcnfSBcIiR7aXRlbS50aXRsZX1cIi4uLmBcbiAgICAgICAgICApO1xuICAgICAgICAgIGF3YWl0IGhhbmRsZVVwZGF0ZShpdGVtLmlkLCB7IGlzUHVibGlzaGVkOiBuZXdTdGF0dXMgfSk7XG4gICAgICAgICAgc2hvd1N1Y2Nlc3MoXG4gICAgICAgICAgICBuZXdTdGF0dXMgPyAnQmxvZyBQb3N0IFB1Ymxpc2hlZCcgOiAnQmxvZyBQb3N0IFVucHVibGlzaGVkJyxcbiAgICAgICAgICAgIGBcIiR7aXRlbS50aXRsZX1cIiAke25ld1N0YXR1cyA/ICdwdWJsaXNoZWQnIDogJ3VucHVibGlzaGVkJ30gc3VjY2Vzc2Z1bGx5IWBcbiAgICAgICAgICApO1xuICAgICAgICAgIGJyZWFrO1xuXG4gICAgICAgIGNhc2UgJ2R1cGxpY2F0ZSc6XG4gICAgICAgICAgc2hvd0xvYWRpbmcoJ0R1cGxpY2F0aW5nIEJsb2cgUG9zdCcsIGBDcmVhdGluZyBjb3B5IG9mIFwiJHtpdGVtLnRpdGxlfVwiLi4uYCk7XG4gICAgICAgICAgLy8gVE9ETzogSW1wbGVtZW50IGR1cGxpY2F0ZSBmdW5jdGlvbmFsaXR5XG4gICAgICAgICAgc2hvd1N1Y2Nlc3MoJ0Jsb2cgUG9zdCBEdXBsaWNhdGVkJywgYFwiJHtpdGVtLnRpdGxlfVwiIGR1cGxpY2F0ZWQgc3VjY2Vzc2Z1bGx5IWApO1xuICAgICAgICAgIGJyZWFrO1xuXG4gICAgICAgIGNhc2UgJ2FyY2hpdmUnOlxuICAgICAgICAgIHNob3dMb2FkaW5nKCdBcmNoaXZpbmcgQmxvZyBQb3N0JywgYEFyY2hpdmluZyBcIiR7aXRlbS50aXRsZX1cIi4uLmApO1xuICAgICAgICAgIC8vIFRPRE86IEltcGxlbWVudCBhcmNoaXZlIGZ1bmN0aW9uYWxpdHlcbiAgICAgICAgICBzaG93U3VjY2VzcygnQmxvZyBQb3N0IEFyY2hpdmVkJywgYFwiJHtpdGVtLnRpdGxlfVwiIGFyY2hpdmVkIHN1Y2Nlc3NmdWxseSFgKTtcbiAgICAgICAgICBicmVhaztcblxuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIGNvbnNvbGUud2FybihgVW5rbm93biBhY3Rpb246ICR7YWN0aW9ufWApO1xuICAgICAgfVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRBY3Rpb25Mb2FkaW5nKG51bGwpO1xuICAgIH1cbiAgfTtcblxuICAvLyBIYW5kbGUgc29ydGluZ1xuICBjb25zdCBoYW5kbGVTb3J0ID0gKGZpZWxkOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBuZXdPcmRlciA9IHNvcnRCeSA9PT0gZmllbGQgPyAoc29ydE9yZGVyID09PSAnYXNjJyA/ICdkZXNjJyA6ICdhc2MnKSA6ICdhc2MnO1xuICAgIGNvbnN0IG5ld0ZpZWxkID0gc29ydEJ5ID09PSBmaWVsZCA/IGZpZWxkIDogZmllbGQ7XG4gICAgXG4gICAgc2V0U29ydEJ5KG5ld0ZpZWxkKTtcbiAgICBzZXRTb3J0T3JkZXIobmV3T3JkZXIpO1xuICAgIHNldEN1cnJlbnRQYWdlKDEpO1xuICAgIFxuICAgIHNob3dJbmZvKCdTb3J0aW5nIEJsb2cgUG9zdHMnLCBgU29ydGluZyBieSAke2ZpZWxkfSAoJHtuZXdPcmRlcn0pYCk7XG4gIH07XG5cbiAgLy8gSGFuZGxlIHNlbGVjdGlvblxuICBjb25zdCBoYW5kbGVTZWxlY3RBbGwgPSAoY2hlY2tlZDogYm9vbGVhbikgPT4ge1xuICAgIGlmIChjaGVja2VkKSB7XG4gICAgICBzZXRTZWxlY3RlZFBvc3RzKGJsb2dQb3N0cy5tYXAocG9zdCA9PiBwb3N0LmlkKSk7XG4gICAgICBzaG93SW5mbygnU2VsZWN0aW9uIFVwZGF0ZWQnLCBgU2VsZWN0ZWQgYWxsICR7YmxvZ1Bvc3RzLmxlbmd0aH0gYmxvZyBwb3N0JHtibG9nUG9zdHMubGVuZ3RoID09PSAxID8gJycgOiAncyd9YCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHNldFNlbGVjdGVkUG9zdHMoW10pO1xuICAgICAgc2hvd0luZm8oJ1NlbGVjdGlvbiBDbGVhcmVkJywgJ0Rlc2VsZWN0ZWQgYWxsIGJsb2cgcG9zdHMnKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU2VsZWN0UG9zdCA9IChpZDogc3RyaW5nLCBjaGVja2VkOiBib29sZWFuKSA9PiB7XG4gICAgaWYgKGNoZWNrZWQpIHtcbiAgICAgIHNldFNlbGVjdGVkUG9zdHMoWy4uLnNlbGVjdGVkUG9zdHMsIGlkXSk7XG4gICAgICBjb25zdCBwb3N0ID0gYmxvZ1Bvc3RzLmZpbmQocCA9PiBwLmlkID09PSBpZCk7XG4gICAgICBzaG93SW5mbygnUG9zdCBTZWxlY3RlZCcsIGBcIiR7cG9zdD8udGl0bGUgfHwgJ0Jsb2cgcG9zdCd9XCIgYWRkZWQgdG8gc2VsZWN0aW9uYCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHNldFNlbGVjdGVkUG9zdHMoc2VsZWN0ZWRQb3N0cy5maWx0ZXIocG9zdElkID0+IHBvc3RJZCAhPT0gaWQpKTtcbiAgICAgIGNvbnN0IHBvc3QgPSBibG9nUG9zdHMuZmluZChwID0+IHAuaWQgPT09IGlkKTtcbiAgICAgIHNob3dJbmZvKCdQb3N0IERlc2VsZWN0ZWQnLCBgXCIke3Bvc3Q/LnRpdGxlIHx8ICdCbG9nIHBvc3QnfVwiIHJlbW92ZWQgZnJvbSBzZWxlY3Rpb25gKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gR2V0IHZpc2libGUgZmllbGRzIGZvciB0YWJsZVxuICBjb25zdCBnZXRWaXNpYmxlRmllbGRzID0gKCkgPT4ge1xuICAgIGlmICh2aXNpYmxlQ29sdW1ucy5sZW5ndGggPiAwKSB7XG4gICAgICByZXR1cm4gY29uZmlnLmZpZWxkcz8uZmlsdGVyKGZpZWxkID0+IHZpc2libGVDb2x1bW5zLmluY2x1ZGVzKGZpZWxkLmtleSkpIHx8IFtdO1xuICAgIH1cbiAgICByZXR1cm4gY29uZmlnLmZpZWxkcyB8fCBbXTtcbiAgfTtcblxuICAvLyBIYW5kbGUgdmlldyBtb2RlIGNoYW5nZVxuICBjb25zdCBoYW5kbGVWaWV3TW9kZUNoYW5nZSA9IChtb2RlOiAnbGlzdCcgfCAnZ3JpZCcpID0+IHtcbiAgICBzZXRWaWV3TW9kZShtb2RlKTtcbiAgICBzaG93SW5mbygnVmlldyBNb2RlIENoYW5nZWQnLCBgU3dpdGNoZWQgdG8gJHttb2RlfSB2aWV3YCk7XG4gIH07XG5cbiAgLy8gSGFuZGxlIGRlbnNpdHkgY2hhbmdlXG4gIGNvbnN0IGhhbmRsZURlbnNpdHlDaGFuZ2UgPSAobmV3RGVuc2l0eTogJ2NvbXBhY3QnIHwgJ2NvbWZvcnRhYmxlJyB8ICdzcGFjaW91cycpID0+IHtcbiAgICBzZXREZW5zaXR5KG5ld0RlbnNpdHkpO1xuICAgIHNob3dJbmZvKCdEZW5zaXR5IFVwZGF0ZWQnLCBgQ2hhbmdlZCB0byAke25ld0RlbnNpdHl9IGRlbnNpdHlgKTtcbiAgfTtcblxuICAvLyBIYW5kbGUgZ3JpZCBjb2x1bW5zIGNoYW5nZVxuICBjb25zdCBoYW5kbGVHcmlkQ29sdW1uc0NoYW5nZSA9IChjb2x1bW5zOiBudW1iZXIpID0+IHtcbiAgICBzZXRHcmlkQ29sdW1ucyhjb2x1bW5zKTtcbiAgICBzaG93SW5mbygnR3JpZCBMYXlvdXQgVXBkYXRlZCcsIGBDaGFuZ2VkIHRvICR7Y29sdW1uc30gY29sdW1uJHtjb2x1bW5zID09PSAxID8gJycgOiAncyd9IGxheW91dGApO1xuICB9O1xuXG4gIC8vIEhhbmRsZSBmaWx0ZXIgY2hhbmdlc1xuICBjb25zdCBoYW5kbGVGaWx0ZXJDaGFuZ2UgPSAoa2V5OiBzdHJpbmcsIHZhbHVlOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBuZXdGaWx0ZXJzID0geyAuLi5maWx0ZXJzIH07XG4gICAgaWYgKHZhbHVlKSB7XG4gICAgICBuZXdGaWx0ZXJzW2tleV0gPSB2YWx1ZTtcbiAgICB9IGVsc2Uge1xuICAgICAgZGVsZXRlIG5ld0ZpbHRlcnNba2V5XTtcbiAgICB9XG4gICAgc2V0RmlsdGVycyhuZXdGaWx0ZXJzKTtcbiAgICBzaG93SW5mbygnRmlsdGVyIEFwcGxpZWQnLCBgRmlsdGVyIHVwZGF0ZWQ6ICR7a2V5fSA9ICR7dmFsdWUgfHwgJ2FsbCd9YCk7XG4gIH07XG5cbiAgLy8gR3JpZCBDYXJkIENvbXBvbmVudFxuICBjb25zdCBHcmlkQ2FyZCA9ICh7IHBvc3QgfTogeyBwb3N0OiBCbG9nUG9zdCB9KSA9PiB7XG4gICAgY29uc3QgaXNTZWxlY3RlZCA9IHNlbGVjdGVkUG9zdHMuaW5jbHVkZXMocG9zdC5pZCk7XG4gICAgY29uc3QgY2xhc3NlcyA9IGdldEdyaWREZW5zaXR5Q2xhc3NlcygpO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgXG4gICAgICAgIGNsYXNzTmFtZT17YGdyb3VwIHJlbGF0aXZlIGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZC1sZyBzaGFkb3ctc20gaG92ZXI6c2hhZG93LW1kIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xuICAgICAgICAgIGlzU2VsZWN0ZWQgPyAncmluZy0yIHJpbmctYmx1ZS01MDAgYmctYmx1ZS01MCcgOiAnJ1xuICAgICAgICB9ICR7Y2xhc3Nlcy5jYXJkfWB9XG4gICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgLy8gSGlkZSBhY3Rpb24gbWVudSB3aGVuIGNsaWNraW5nIG9uIGNhcmQgKG1vYmlsZSBvbmx5KVxuICAgICAgICAgIGlmICh3aW5kb3cuaW5uZXJXaWR0aCA8PSAxMDI0KSB7XG4gICAgICAgICAgICBjb25zdCBhY3Rpb25NZW51ID0gZS5jdXJyZW50VGFyZ2V0LnF1ZXJ5U2VsZWN0b3IoJy5hY3Rpb24tbWVudScpIGFzIEhUTUxFbGVtZW50O1xuICAgICAgICAgICAgaWYgKGFjdGlvbk1lbnUgJiYgYWN0aW9uTWVudS5jbGFzc0xpc3QuY29udGFpbnMoJ3Nob3cnKSkge1xuICAgICAgICAgICAgICBhY3Rpb25NZW51LmNsYXNzTGlzdC5yZW1vdmUoJ3Nob3cnKTtcbiAgICAgICAgICAgICAgYWN0aW9uTWVudS5zdHlsZS5vcGFjaXR5ID0gJzAnO1xuICAgICAgICAgICAgICBhY3Rpb25NZW51LnN0eWxlLnRyYW5zZm9ybSA9ICd0cmFuc2xhdGVYKDEwMCUpJztcbiAgICAgICAgICAgICAgYWN0aW9uTWVudS5zdHlsZS5wb2ludGVyRXZlbnRzID0gJ25vbmUnO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfX1cbiAgICAgICAgb25Nb3VzZUxlYXZlPXsoZSkgPT4ge1xuICAgICAgICAgIC8vIEhpZGUgYWN0aW9uIG1lbnUgd2hlbiBtb3VzZSBsZWF2ZXMgKGFsbCBzY3JlZW4gc2l6ZXMpXG4gICAgICAgICAgY29uc3QgYWN0aW9uTWVudSA9IGUuY3VycmVudFRhcmdldC5xdWVyeVNlbGVjdG9yKCcuYWN0aW9uLW1lbnUnKSBhcyBIVE1MRWxlbWVudDtcbiAgICAgICAgICBpZiAoYWN0aW9uTWVudSAmJiBhY3Rpb25NZW51LmNsYXNzTGlzdC5jb250YWlucygnc2hvdycpKSB7XG4gICAgICAgICAgICBhY3Rpb25NZW51LmNsYXNzTGlzdC5yZW1vdmUoJ3Nob3cnKTtcbiAgICAgICAgICAgIGFjdGlvbk1lbnUuc3R5bGUub3BhY2l0eSA9ICcwJztcbiAgICAgICAgICAgIGFjdGlvbk1lbnUuc3R5bGUudHJhbnNmb3JtID0gJ3RyYW5zbGF0ZVgoMTAwJSknO1xuICAgICAgICAgICAgYWN0aW9uTWVudS5zdHlsZS5wb2ludGVyRXZlbnRzID0gJ25vbmUnO1xuICAgICAgICAgIH1cbiAgICAgICAgfX1cbiAgICAgID5cbiAgICAgICAgey8qIEZlYXR1cmVkIEltYWdlICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFzcGVjdC12aWRlbyBiZy1ncmF5LTEwMCByb3VuZGVkLXQtbGcgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAge3Bvc3QuZmVhdHVyZWRJbWFnZVVybCA/IChcbiAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgc3JjPXtwb3N0LmZlYXR1cmVkSW1hZ2VVcmx9XG4gICAgICAgICAgICAgIGFsdD17cG9zdC50aXRsZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXJcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxEb2N1bWVudFRleHRJY29uIGNsYXNzTmFtZT1cInctMTIgaC0xMiB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBDb250ZW50ICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17Y2xhc3Nlcy5jb250ZW50fT5cbiAgICAgICAgICB7LyogVGl0bGUgKi99XG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT17YCR7Y2xhc3Nlcy50aXRsZX0gdGV4dC1ncmF5LTkwMCBsaW5lLWNsYW1wLTJgfT5cbiAgICAgICAgICAgIHtwb3N0LnRpdGxlfVxuICAgICAgICAgIDwvaDM+XG5cbiAgICAgICAgICB7LyogRXhjZXJwdCAqL31cbiAgICAgICAgICB7cG9zdC5leGNlcnB0ICYmIChcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT17YCR7Y2xhc3Nlcy5leGNlcnB0fSB0ZXh0LWdyYXktNjAwIGxpbmUtY2xhbXAtM2B9PlxuICAgICAgICAgICAgICB7cG9zdC5leGNlcnB0fVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgey8qIE1ldGEgSW5mb3JtYXRpb24gKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbGFzc2VzLm1ldGF9PlxuICAgICAgICAgICAgICAgIHsvKiBTdGF0dXMgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGlubGluZS1mbGV4ICR7Y2xhc3Nlcy5zdGF0dXN9IGZvbnQtc2VtaWJvbGQgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICAgIHBvc3QuaXNQdWJsaXNoZWRcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnXG4gICAgICAgICAgICAgICAgICAgICAgOiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCdcbiAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAge3Bvc3QuaXNQdWJsaXNoZWQgPyAnUHVibGlzaGVkJyA6ICdEcmFmdCd9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2Ake2NsYXNzZXMuZGF0ZX0gdGV4dC1ncmF5LTUwMGB9PlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZShwb3N0LnVwZGF0ZWRBdCl9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogQ2F0ZWdvcmllcyAqL31cbiAgICAgICAgICAgICAgICB7cG9zdC5jYXRlZ29yaWVzICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleCBmbGV4LXdyYXAgJHtjbGFzc2VzLmNhdGVnb3JpZXN9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtwb3N0LmNhdGVnb3JpZXMuc3BsaXQoJywnKS5zbGljZSgwLCAyKS5tYXAoKGNhdGVnb3J5OiBzdHJpbmcsIGluZGV4OiBudW1iZXIpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBrZXk9e2luZGV4fSBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCAke2NsYXNzZXMuY2F0ZWdvcnl9IGJnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAgcm91bmRlZGB9PlxuICAgICAgICAgICAgICAgICAgICAgICAge2NhdGVnb3J5LnRyaW0oKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIEdyaWQgQ2FyZCBGb290ZXIgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuICR7ZGVuc2l0eSA9PT0gJ2NvbXBhY3QnID8gJ210LTMgcHQtMicgOiBkZW5zaXR5ID09PSAnc3BhY2lvdXMnID8gJ210LTUgcHQtNCcgOiAnbXQtNCBwdC0zJ30gYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwYH0+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgIFVwZGF0ZWQge2Zvcm1hdERhdGUocG9zdC51cGRhdGVkQXQpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIE1vYmlsZSBBY3Rpb24gQnV0dG9uIC0gQWx3YXlzIHZpc2libGUgb24gbW9iaWxlIGFuZCBzbWFsbCBzY3JlZW5zICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6aGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKVxuICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGFjdGlvbk1lbnUgPSBlLmN1cnJlbnRUYXJnZXQuY2xvc2VzdCgnLmdyb3VwJyk/LnF1ZXJ5U2VsZWN0b3IoJy5hY3Rpb24tbWVudScpIGFzIEhUTUxFbGVtZW50O1xuICAgICAgICAgICAgICAgICAgICAgIGlmIChhY3Rpb25NZW51KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBpc1Zpc2libGUgPSBhY3Rpb25NZW51LnN0eWxlLm9wYWNpdHkgPT09ICcxJztcbiAgICAgICAgICAgICAgICAgICAgICAgIGFjdGlvbk1lbnUuc3R5bGUub3BhY2l0eSA9IGlzVmlzaWJsZSA/ICcwJyA6ICcxJztcbiAgICAgICAgICAgICAgICAgICAgICAgIGFjdGlvbk1lbnUuc3R5bGUudHJhbnNmb3JtID0gaXNWaXNpYmxlID8gJ3RyYW5zbGF0ZVgoMTAwJSknIDogJ3RyYW5zbGF0ZVgoMCknO1xuICAgICAgICAgICAgICAgICAgICAgICAgYWN0aW9uTWVudS5zdHlsZS5wb2ludGVyRXZlbnRzID0gaXNWaXNpYmxlID8gJ25vbmUnIDogJ2F1dG8nO1xuICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCBiZy1ncmF5LTEwMCBob3ZlcjpiZy1ncmF5LTIwMCByb3VuZGVkLW1kIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJTaG93IEFjdGlvbnNcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cImgtNCB3LTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTIgNXYuMDFNMTIgMTJ2LjAxTTEyIDE5di4wMU0xMiA2YTEgMSAwIDExMC0yIDEgMSAwIDAxMCAyem0wIDdhMSAxIDAgMTEwLTIgMSAxIDAgMDEwIDJ6bTAgN2ExIDEgMCAxMTAtMiAxIDEgMCAwMTAgMnpcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBBY3Rpb25zIFNpZGViYXIgLSBQcm9mZXNzaW9uYWwgT3ZlcmxheSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGFjdGlvbi1tZW51IGFic29sdXRlIGJnLXdoaXRlIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgei0xMCAke1xuICAgICAgICAgICAgZGVuc2l0eSA9PT0gJ2NvbXBhY3QnIFxuICAgICAgICAgICAgICA/ICd0b3AtMSByaWdodC0xIGJvdHRvbS0xIHctMTIgc3BhY2UteS0xLjUgcHgtMScgXG4gICAgICAgICAgICAgIDogZGVuc2l0eSA9PT0gJ3NwYWNpb3VzJyBcbiAgICAgICAgICAgICAgPyAndG9wLTMgcmlnaHQtMyBib3R0b20tMyB3LTE2IHNwYWNlLXktMyBweC0yJyBcbiAgICAgICAgICAgICAgOiAndG9wLTIgcmlnaHQtMiBib3R0b20tMiB3LTE0IHNwYWNlLXktMiBweC0xLjUnXG4gICAgICAgICAgfWB9PlxuICAgICAgICAgICAgey8qIEVkaXQgQnV0dG9uICovfVxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKClcbiAgICAgICAgICAgICAgICBoYW5kbGVBY3Rpb24oJ2VkaXQnLCBwb3N0KVxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bncm91cC9idG4gcmVsYXRpdmUgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwIGJvcmRlciBib3JkZXItYmx1ZS01MDAgaG92ZXI6Ym9yZGVyLWJsdWUtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbWQgaG92ZXI6c2hhZG93LWxnIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpyaW5nLW9mZnNldC0yIGN1cnNvci1wb2ludGVyIGhvdmVyOnNjYWxlLTExMCBwb2ludGVyLWV2ZW50cy1hdXRvICR7XG4gICAgICAgICAgICAgICAgZGVuc2l0eSA9PT0gJ2NvbXBhY3QnID8gJ3ctOCBoLTgnIDogZGVuc2l0eSA9PT0gJ3NwYWNpb3VzJyA/ICd3LTEyIGgtMTInIDogJ3ctMTAgaC0xMCdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgIHRpdGxlPVwiRWRpdCBCbG9nIFBvc3RcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8UGVuY2lsSWNvbiBjbGFzc05hbWU9e2Bncm91cC1ob3Zlci9idG46c2NhbGUtMTEwICR7ZGVuc2l0eSA9PT0gJ2NvbXBhY3QnID8gJ2gtNCB3LTQnIDogZGVuc2l0eSA9PT0gJ3NwYWNpb3VzJyA/ICdoLTYgdy02JyA6ICdoLTUgdy01J31gfSAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHsvKiBWaWV3IEJ1dHRvbiAqL31cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpXG4gICAgICAgICAgICAgICAgaGFuZGxlQWN0aW9uKCd2aWV3JywgcG9zdClcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZ3JvdXAvYnRuIHJlbGF0aXZlIGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiZy1pbmRpZ28tNjAwIGhvdmVyOmJnLWluZGlnby03MDAgYm9yZGVyIGJvcmRlci1pbmRpZ28tNTAwIGhvdmVyOmJvcmRlci1pbmRpZ28tNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbWQgaG92ZXI6c2hhZG93LWxnIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1pbmRpZ28tNTAwIGZvY3VzOnJpbmctb2Zmc2V0LTIgY3Vyc29yLXBvaW50ZXIgaG92ZXI6c2NhbGUtMTEwIHBvaW50ZXItZXZlbnRzLWF1dG8gJHtcbiAgICAgICAgICAgICAgICBkZW5zaXR5ID09PSAnY29tcGFjdCcgPyAndy04IGgtOCcgOiBkZW5zaXR5ID09PSAnc3BhY2lvdXMnID8gJ3ctMTIgaC0xMicgOiAndy0xMCBoLTEwJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgdGl0bGU9XCJWaWV3IEJsb2cgUG9zdFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxFeWVJY29uIGNsYXNzTmFtZT17YGdyb3VwLWhvdmVyL2J0bjpzY2FsZS0xMTAgJHtkZW5zaXR5ID09PSAnY29tcGFjdCcgPyAnaC00IHctNCcgOiBkZW5zaXR5ID09PSAnc3BhY2lvdXMnID8gJ2gtNiB3LTYnIDogJ2gtNSB3LTUnfWB9IC8+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgey8qIFRvZ2dsZSBQdWJsaXNoZWQgQnV0dG9uICovfVxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKClcbiAgICAgICAgICAgICAgICBoYW5kbGVBY3Rpb24oJ3RvZ2dsZS1wdWJsaXNoZWQnLCBwb3N0KVxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bncm91cC9idG4gcmVsYXRpdmUgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyICR7XG4gICAgICAgICAgICAgICAgcG9zdC5pc1B1Ymxpc2hlZCBcbiAgICAgICAgICAgICAgICAgID8gJ2JnLW9yYW5nZS02MDAgaG92ZXI6Ymctb3JhbmdlLTcwMCBib3JkZXItb3JhbmdlLTUwMCBob3Zlcjpib3JkZXItb3JhbmdlLTYwMCcgXG4gICAgICAgICAgICAgICAgICA6ICdiZy1ncmVlbi02MDAgaG92ZXI6YmctZ3JlZW4tNzAwIGJvcmRlci1ncmVlbi01MDAgaG92ZXI6Ym9yZGVyLWdyZWVuLTYwMCdcbiAgICAgICAgICAgICAgfSBib3JkZXIgdGV4dC13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1tZCBob3ZlcjpzaGFkb3ctbGcgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGN1cnNvci1wb2ludGVyIGhvdmVyOnNjYWxlLTExMCBwb2ludGVyLWV2ZW50cy1hdXRvICR7XG4gICAgICAgICAgICAgICAgZGVuc2l0eSA9PT0gJ2NvbXBhY3QnID8gJ3ctOCBoLTgnIDogZGVuc2l0eSA9PT0gJ3NwYWNpb3VzJyA/ICd3LTEyIGgtMTInIDogJ3ctMTAgaC0xMCdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgIHRpdGxlPXtwb3N0LmlzUHVibGlzaGVkID8gJ1VucHVibGlzaCBCbG9nIFBvc3QnIDogJ1B1Ymxpc2ggQmxvZyBQb3N0J31cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge3Bvc3QuaXNQdWJsaXNoZWQgPyAoXG4gICAgICAgICAgICAgICAgPEV5ZVNsYXNoSWNvbiBjbGFzc05hbWU9e2Bncm91cC1ob3Zlci9idG46c2NhbGUtMTEwICR7ZGVuc2l0eSA9PT0gJ2NvbXBhY3QnID8gJ2gtNCB3LTQnIDogZGVuc2l0eSA9PT0gJ3NwYWNpb3VzJyA/ICdoLTYgdy02JyA6ICdoLTUgdy01J31gfSAvPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxQb3dlckljb24gY2xhc3NOYW1lPXtgZ3JvdXAtaG92ZXIvYnRuOnNjYWxlLTExMCAke2RlbnNpdHkgPT09ICdjb21wYWN0JyA/ICdoLTQgdy00JyA6IGRlbnNpdHkgPT09ICdzcGFjaW91cycgPyAnaC02IHctNicgOiAnaC01IHctNSd9YH0gLz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7LyogRHVwbGljYXRlIEJ1dHRvbiAqL31cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpXG4gICAgICAgICAgICAgICAgaGFuZGxlQWN0aW9uKCdkdXBsaWNhdGUnLCBwb3N0KVxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bncm91cC9idG4gcmVsYXRpdmUgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLXB1cnBsZS02MDAgaG92ZXI6YmctcHVycGxlLTcwMCBib3JkZXIgYm9yZGVyLXB1cnBsZS01MDAgaG92ZXI6Ym9yZGVyLXB1cnBsZS02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1tZCBob3ZlcjpzaGFkb3ctbGcgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXB1cnBsZS01MDAgZm9jdXM6cmluZy1vZmZzZXQtMiBjdXJzb3ItcG9pbnRlciBob3ZlcjpzY2FsZS0xMTAgcG9pbnRlci1ldmVudHMtYXV0byAke1xuICAgICAgICAgICAgICAgIGRlbnNpdHkgPT09ICdjb21wYWN0JyA/ICd3LTggaC04JyA6IGRlbnNpdHkgPT09ICdzcGFjaW91cycgPyAndy0xMiBoLTEyJyA6ICd3LTEwIGgtMTAnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICB0aXRsZT1cIkR1cGxpY2F0ZSBCbG9nIFBvc3RcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8RG9jdW1lbnREdXBsaWNhdGVJY29uIGNsYXNzTmFtZT17YGdyb3VwLWhvdmVyL2J0bjpzY2FsZS0xMTAgJHtkZW5zaXR5ID09PSAnY29tcGFjdCcgPyAnaC00IHctNCcgOiBkZW5zaXR5ID09PSAnc3BhY2lvdXMnID8gJ2gtNiB3LTYnIDogJ2gtNSB3LTUnfWB9IC8+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgey8qIEFyY2hpdmUgQnV0dG9uICovfVxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKClcbiAgICAgICAgICAgICAgICBoYW5kbGVBY3Rpb24oJ2FyY2hpdmUnLCBwb3N0KVxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bncm91cC9idG4gcmVsYXRpdmUgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLXllbGxvdy02MDAgaG92ZXI6YmcteWVsbG93LTcwMCBib3JkZXIgYm9yZGVyLXllbGxvdy01MDAgaG92ZXI6Ym9yZGVyLXllbGxvdy02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1tZCBob3ZlcjpzaGFkb3ctbGcgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXllbGxvdy01MDAgZm9jdXM6cmluZy1vZmZzZXQtMiBjdXJzb3ItcG9pbnRlciBob3ZlcjpzY2FsZS0xMTAgcG9pbnRlci1ldmVudHMtYXV0byAke1xuICAgICAgICAgICAgICAgIGRlbnNpdHkgPT09ICdjb21wYWN0JyA/ICd3LTggaC04JyA6IGRlbnNpdHkgPT09ICdzcGFjaW91cycgPyAndy0xMiBoLTEyJyA6ICd3LTEwIGgtMTAnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICB0aXRsZT1cIkFyY2hpdmUgQmxvZyBQb3N0XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPEFyY2hpdmVCb3hJY29uIGNsYXNzTmFtZT17YGdyb3VwLWhvdmVyL2J0bjpzY2FsZS0xMTAgJHtkZW5zaXR5ID09PSAnY29tcGFjdCcgPyAnaC00IHctNCcgOiBkZW5zaXR5ID09PSAnc3BhY2lvdXMnID8gJ2gtNiB3LTYnIDogJ2gtNSB3LTUnfWB9IC8+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgey8qIERlbGV0ZSBCdXR0b24gKi99XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKVxuICAgICAgICAgICAgICAgIGhhbmRsZUFjdGlvbignZGVsZXRlJywgcG9zdClcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZ3JvdXAvYnRuIHJlbGF0aXZlIGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiZy1yZWQtNjAwIGhvdmVyOmJnLXJlZC03MDAgYm9yZGVyIGJvcmRlci1yZWQtNTAwIGhvdmVyOmJvcmRlci1yZWQtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbWQgaG92ZXI6c2hhZG93LWxnIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1yZWQtNTAwIGZvY3VzOnJpbmctb2Zmc2V0LTIgY3Vyc29yLXBvaW50ZXIgaG92ZXI6c2NhbGUtMTEwIHBvaW50ZXItZXZlbnRzLWF1dG8gJHtcbiAgICAgICAgICAgICAgICBkZW5zaXR5ID09PSAnY29tcGFjdCcgPyAndy04IGgtOCcgOiBkZW5zaXR5ID09PSAnc3BhY2lvdXMnID8gJ3ctMTIgaC0xMicgOiAndy0xMCBoLTEwJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgdGl0bGU9XCJEZWxldGUgQmxvZyBQb3N0XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFRyYXNoSWNvbiBjbGFzc05hbWU9e2Bncm91cC1ob3Zlci9idG46c2NhbGUtMTEwICR7ZGVuc2l0eSA9PT0gJ2NvbXBhY3QnID8gJ2gtNCB3LTQnIDogZGVuc2l0eSA9PT0gJ3NwYWNpb3VzJyA/ICdoLTYgdy02JyA6ICdoLTUgdy01J31gfSAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG5cblxuICAgICAgICAgIHsvKiBDaGVja2JveCBmb3IgYnVsayBhY3Rpb25zICovfVxuICAgICAgICAgIHtjb25maWcuZW5hYmxlQnVsa0FjdGlvbnMgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BhYnNvbHV0ZSB0b3AtMiBsZWZ0LTIgJHtjbGFzc2VzLmNoZWNrYm94fWB9PlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2lzU2VsZWN0ZWR9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVTZWxlY3RQb3N0KHBvc3QuaWQsIGUudGFyZ2V0LmNoZWNrZWQpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHJvdW5kZWQgYm9yZGVyLWdyYXktMzAwIHRleHQtYmx1ZS02MDAgZm9jdXM6cmluZy1ibHVlLTUwMCBiZy13aGl0ZSAhYmctd2hpdGUgJHtjbGFzc2VzLmNoZWNrYm94fWB9XG4gICAgICAgICAgICAgICAgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiAnd2hpdGUnIH19XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH07XG5cbiAgLy8gR3JpZCBkZW5zaXR5IHN0eWxpbmcgaGVscGVyXG4gIGNvbnN0IGdldEdyaWREZW5zaXR5Q2xhc3NlcyA9ICgpID0+IHtcbiAgICBjb25zdCBiYXNlQ2xhc3NlcyA9IHtcbiAgICAgIGNvbnRhaW5lcjoge1xuICAgICAgICBjb21wYWN0OiAncC0xJyxcbiAgICAgICAgY29tZm9ydGFibGU6ICdwLTMnLCBcbiAgICAgICAgc3BhY2lvdXM6ICdwLTQnXG4gICAgICB9LFxuICAgICAgZ3JpZDoge1xuICAgICAgICBjb21wYWN0OiAnZ2FwLTInLFxuICAgICAgICBjb21mb3J0YWJsZTogJ2dhcC0zJyxcbiAgICAgICAgc3BhY2lvdXM6ICdnYXAtNCdcbiAgICAgIH0sXG4gICAgICBjYXJkOiB7XG4gICAgICAgIGNvbXBhY3Q6ICdwLTEnLFxuICAgICAgICBjb21mb3J0YWJsZTogJ3AtMicsXG4gICAgICAgIHNwYWNpb3VzOiAncC0zJ1xuICAgICAgfSxcbiAgICAgIGNvbnRlbnQ6IHtcbiAgICAgICAgY29tcGFjdDogJ3AtMScsXG4gICAgICAgIGNvbWZvcnRhYmxlOiAncC0yJywgXG4gICAgICAgIHNwYWNpb3VzOiAncC0zJ1xuICAgICAgfSxcbiAgICAgIHRpdGxlOiB7XG4gICAgICAgIGNvbXBhY3Q6ICd0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgbWItMC41JyxcbiAgICAgICAgY29tZm9ydGFibGU6ICd0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItMScsXG4gICAgICAgIHNwYWNpb3VzOiAndGV4dC14bCBmb250LXNlbWlib2xkIG1iLTInXG4gICAgICB9LFxuICAgICAgZXhjZXJwdDoge1xuICAgICAgICBjb21wYWN0OiAndGV4dC14cyBtYi0xJyxcbiAgICAgICAgY29tZm9ydGFibGU6ICd0ZXh0LXNtIG1iLTEuNScsXG4gICAgICAgIHNwYWNpb3VzOiAndGV4dC1iYXNlIG1iLTInXG4gICAgICB9LFxuICAgICAgbWV0YToge1xuICAgICAgICBjb21wYWN0OiAnc3BhY2UteS0wLjUgbWItMScsXG4gICAgICAgIGNvbWZvcnRhYmxlOiAnc3BhY2UteS0xIG1iLTInLFxuICAgICAgICBzcGFjaW91czogJ3NwYWNlLXktMiBtYi0zJ1xuICAgICAgfSxcbiAgICAgIHN0YXR1czoge1xuICAgICAgICBjb21wYWN0OiAncHgtMSBweS0wLjUgdGV4dC14cycsXG4gICAgICAgIGNvbWZvcnRhYmxlOiAncHgtMS41IHB5LTAuNSB0ZXh0LXhzJyxcbiAgICAgICAgc3BhY2lvdXM6ICdweC0yIHB5LTEgdGV4dC1zbSdcbiAgICAgIH0sXG4gICAgICBkYXRlOiB7XG4gICAgICAgIGNvbXBhY3Q6ICd0ZXh0LXhzJyxcbiAgICAgICAgY29tZm9ydGFibGU6ICd0ZXh0LXhzJyxcbiAgICAgICAgc3BhY2lvdXM6ICd0ZXh0LXNtJ1xuICAgICAgfSxcbiAgICAgIGNhdGVnb3JpZXM6IHtcbiAgICAgICAgY29tcGFjdDogJ2dhcC0wLjUnLFxuICAgICAgICBjb21mb3J0YWJsZTogJ2dhcC0wLjUnLFxuICAgICAgICBzcGFjaW91czogJ2dhcC0xJ1xuICAgICAgfSxcbiAgICAgIGNhdGVnb3J5OiB7XG4gICAgICAgIGNvbXBhY3Q6ICdweC0xIHB5LTAuNSB0ZXh0LXhzJyxcbiAgICAgICAgY29tZm9ydGFibGU6ICdweC0xLjUgcHktMC41IHRleHQteHMnLFxuICAgICAgICBzcGFjaW91czogJ3B4LTIgcHktMSB0ZXh0LXNtJ1xuICAgICAgfSxcbiAgICAgIGFjdGlvbnM6IHtcbiAgICAgICAgY29tcGFjdDogJ3B0LTEnLFxuICAgICAgICBjb21mb3J0YWJsZTogJ3B0LTEuNScsXG4gICAgICAgIHNwYWNpb3VzOiAncHQtMidcbiAgICAgIH0sXG4gICAgICBidXR0b25zOiB7XG4gICAgICAgIGNvbXBhY3Q6ICdzcGFjZS14LTAuNScsXG4gICAgICAgIGNvbWZvcnRhYmxlOiAnc3BhY2UteC0xJyxcbiAgICAgICAgc3BhY2lvdXM6ICdzcGFjZS14LTInXG4gICAgICB9LFxuICAgICAgYnV0dG9uOiB7XG4gICAgICAgIGNvbXBhY3Q6ICdwLTAuNScsXG4gICAgICAgIGNvbWZvcnRhYmxlOiAncC0xJyxcbiAgICAgICAgc3BhY2lvdXM6ICdwLTEuNSdcbiAgICAgIH0sXG4gICAgICBpY29uOiB7XG4gICAgICAgIGNvbXBhY3Q6ICd3LTIuNSBoLTIuNScsXG4gICAgICAgIGNvbWZvcnRhYmxlOiAndy0zIGgtMycsXG4gICAgICAgIHNwYWNpb3VzOiAndy00IGgtNCdcbiAgICAgIH0sXG4gICAgICBjaGVja2JveDoge1xuICAgICAgICBjb21wYWN0OiAnaC0yLjUgdy0yLjUnLFxuICAgICAgICBjb21mb3J0YWJsZTogJ2gtMyB3LTMnLFxuICAgICAgICBzcGFjaW91czogJ2gtNCB3LTQnXG4gICAgICB9XG4gICAgfTtcblxuICAgIHJldHVybiB7XG4gICAgICBjb250YWluZXI6IGJhc2VDbGFzc2VzLmNvbnRhaW5lcltkZW5zaXR5XSxcbiAgICAgIGdyaWQ6IGJhc2VDbGFzc2VzLmdyaWRbZGVuc2l0eV0sXG4gICAgICBjYXJkOiBiYXNlQ2xhc3Nlcy5jYXJkW2RlbnNpdHldLFxuICAgICAgY29udGVudDogYmFzZUNsYXNzZXMuY29udGVudFtkZW5zaXR5XSxcbiAgICAgIHRpdGxlOiBiYXNlQ2xhc3Nlcy50aXRsZVtkZW5zaXR5XSxcbiAgICAgIGV4Y2VycHQ6IGJhc2VDbGFzc2VzLmV4Y2VycHRbZGVuc2l0eV0sXG4gICAgICBtZXRhOiBiYXNlQ2xhc3Nlcy5tZXRhW2RlbnNpdHldLFxuICAgICAgc3RhdHVzOiBiYXNlQ2xhc3Nlcy5zdGF0dXNbZGVuc2l0eV0sXG4gICAgICBkYXRlOiBiYXNlQ2xhc3Nlcy5kYXRlW2RlbnNpdHldLFxuICAgICAgY2F0ZWdvcmllczogYmFzZUNsYXNzZXMuY2F0ZWdvcmllc1tkZW5zaXR5XSxcbiAgICAgIGNhdGVnb3J5OiBiYXNlQ2xhc3Nlcy5jYXRlZ29yeVtkZW5zaXR5XSxcbiAgICAgIGFjdGlvbnM6IGJhc2VDbGFzc2VzLmFjdGlvbnNbZGVuc2l0eV0sXG4gICAgICBidXR0b25zOiBiYXNlQ2xhc3Nlcy5idXR0b25zW2RlbnNpdHldLFxuICAgICAgYnV0dG9uOiBiYXNlQ2xhc3Nlcy5idXR0b25bZGVuc2l0eV0sXG4gICAgICBpY29uOiBiYXNlQ2xhc3Nlcy5pY29uW2RlbnNpdHldLFxuICAgICAgY2hlY2tib3g6IGJhc2VDbGFzc2VzLmNoZWNrYm94W2RlbnNpdHldXG4gICAgfTtcbiAgfTtcblxuICAvLyBGb3JtYXQgZGF0ZVxuICBjb25zdCBmb3JtYXREYXRlID0gKGRhdGVTdHJpbmc6IHN0cmluZykgPT4ge1xuICAgIHJldHVybiBuZXcgRGF0ZShkYXRlU3RyaW5nKS50b0xvY2FsZURhdGVTdHJpbmcoKTtcbiAgfTtcblxuICAvLyBUcnVuY2F0ZSB0ZXh0XG4gIGNvbnN0IHRydW5jYXRlVGV4dCA9ICh0ZXh0OiBzdHJpbmcsIG1heExlbmd0aDogbnVtYmVyKSA9PiB7XG4gICAgaWYgKHRleHQubGVuZ3RoIDw9IG1heExlbmd0aCkgcmV0dXJuIHRleHQ7XG4gICAgcmV0dXJuIHRleHQuc3Vic3RyaW5nKDAsIG1heExlbmd0aCkgKyAnLi4uJztcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1mdWxsIGZsZXggZmxleC1jb2wgc3BhY2UteS00XCI+XG4gICAgICB7LyogQ1NTIGZvciBob3ZlciBlZmZlY3RzIGFuZCBkZW5zaXR5ICovfVxuICAgICAgPHN0eWxlIGpzeCBnbG9iYWw+e2BcbiAgICAgICAgLmFjdGlvbi1tZW51IHtcbiAgICAgICAgICBvcGFjaXR5OiAwICFpbXBvcnRhbnQ7XG4gICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDEwMCUpICFpbXBvcnRhbnQ7XG4gICAgICAgICAgcG9pbnRlci1ldmVudHM6IG5vbmUgIWltcG9ydGFudDtcbiAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlLWluLW91dCAhaW1wb3J0YW50O1xuICAgICAgICB9XG4gICAgICAgIC8qIERpc2FibGUgYWxsIGhvdmVyIGVmZmVjdHMgLSBvbmx5IHNob3cgb24gY2xpY2sgKi9cbiAgICAgICAgLmdyb3VwOmhvdmVyIC5hY3Rpb24tbWVudSB7XG4gICAgICAgICAgb3BhY2l0eTogMCAhaW1wb3J0YW50O1xuICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgxMDAlKSAhaW1wb3J0YW50O1xuICAgICAgICAgIHBvaW50ZXItZXZlbnRzOiBub25lICFpbXBvcnRhbnQ7XG4gICAgICAgIH1cbiAgICAgICAgLmdyb3VwOmhvdmVyIC5hY3Rpb24tbWVudSAqIHtcbiAgICAgICAgICBwb2ludGVyLWV2ZW50czogbm9uZSAhaW1wb3J0YW50O1xuICAgICAgICB9XG4gICAgICAgIC8qIEZvcmNlIGFjdGlvbiBtZW51IHRvIGJlIHZpc2libGUgd2hlbiB0cmlnZ2VyZWQgYnkgYnV0dG9uICovXG4gICAgICAgIC5hY3Rpb24tbWVudVtzdHlsZSo9XCJvcGFjaXR5OiAxXCJdIHtcbiAgICAgICAgICBvcGFjaXR5OiAxICFpbXBvcnRhbnQ7XG4gICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDApICFpbXBvcnRhbnQ7XG4gICAgICAgICAgcG9pbnRlci1ldmVudHM6IGF1dG8gIWltcG9ydGFudDtcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4ICFpbXBvcnRhbnQ7XG4gICAgICAgIH1cbiAgICAgICAgLyogT3ZlcnJpZGUgYW55IGNvbmZsaWN0aW5nIHN0eWxlcyB3aGVuIG1lbnUgaXMgdHJpZ2dlcmVkICovXG4gICAgICAgIC5hY3Rpb24tbWVudVtzdHlsZSo9XCJvcGFjaXR5OiAxXCJdICoge1xuICAgICAgICAgIHBvaW50ZXItZXZlbnRzOiBhdXRvICFpbXBvcnRhbnQ7XG4gICAgICAgIH1cbiAgICAgICAgLyogQWx0ZXJuYXRpdmUgYXBwcm9hY2ggLSB1c2UgYSBjbGFzcyAqL1xuICAgICAgICAuYWN0aW9uLW1lbnUuc2hvdyB7XG4gICAgICAgICAgb3BhY2l0eTogMSAhaW1wb3J0YW50O1xuICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgwKSAhaW1wb3J0YW50O1xuICAgICAgICAgIHBvaW50ZXItZXZlbnRzOiBhdXRvICFpbXBvcnRhbnQ7XG4gICAgICAgICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50O1xuICAgICAgICB9XG4gICAgICAgIC8qIERpc2FibGUgaG92ZXIgZWZmZWN0cyBvbiBtb2JpbGUvdGFibGV0IC0gb25seSBzaG93IG9uIGNsaWNrICovXG4gICAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMDI0cHgpIHtcbiAgICAgICAgICAuZ3JvdXA6aG92ZXIgLmFjdGlvbi1tZW51IHtcbiAgICAgICAgICAgIG9wYWNpdHk6IDAgIWltcG9ydGFudDtcbiAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgxMDAlKSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgcG9pbnRlci1ldmVudHM6IG5vbmUgIWltcG9ydGFudDtcbiAgICAgICAgICB9XG4gICAgICAgICAgLmdyb3VwOmhvdmVyIC5hY3Rpb24tbWVudSAqIHtcbiAgICAgICAgICAgIHBvaW50ZXItZXZlbnRzOiBub25lICFpbXBvcnRhbnQ7XG4gICAgICAgICAgfVxuICAgICAgICAgIC8qIEFsbG93IGFjdGlvbiBtZW51IG9uIG1vYmlsZSB3aGVuIHRyaWdnZXJlZCBieSBidXR0b24gKi9cbiAgICAgICAgICAuYWN0aW9uLW1lbnUge1xuICAgICAgICAgICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50O1xuICAgICAgICAgICAgb3BhY2l0eTogMCAhaW1wb3J0YW50O1xuICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDEwMCUpICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICBwb2ludGVyLWV2ZW50czogbm9uZSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZS1pbi1vdXQgIWltcG9ydGFudDtcbiAgICAgICAgICB9XG4gICAgICAgICAgLmFjdGlvbi1tZW51W3N0eWxlKj1cIm9wYWNpdHk6IDFcIl0ge1xuICAgICAgICAgICAgb3BhY2l0eTogMSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDApICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICBwb2ludGVyLWV2ZW50czogYXV0byAhaW1wb3J0YW50O1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICAvKiBFbnN1cmUgbW9iaWxlIHRocmVlLWRvdCBidXR0b24gaXMgdmlzaWJsZSAqL1xuICAgICAgICAubW9iaWxlLXRocmVlLWRvdCB7XG4gICAgICAgICAgZGlzcGxheTogYmxvY2sgIWltcG9ydGFudDtcbiAgICAgICAgICB6LWluZGV4OiAzMCAhaW1wb3J0YW50O1xuICAgICAgICB9XG4gICAgICAgIC8qIEZvcmNlIG1vYmlsZSBidXR0b24gdmlzaWJpbGl0eSAqL1xuICAgICAgICAubWRcXFxcOmhpZGRlbiB7XG4gICAgICAgICAgZGlzcGxheTogYmxvY2sgIWltcG9ydGFudDtcbiAgICAgICAgfVxuICAgICAgICBAbWVkaWEgKG1pbi13aWR0aDogNzY4cHgpIHtcbiAgICAgICAgICAubWRcXFxcOmhpZGRlbiB7XG4gICAgICAgICAgICBkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIC5kZW5zaXR5LWNvbXBhY3QgdGgsIC5kZW5zaXR5LWNvbXBhY3QgdGQge1xuICAgICAgICAgIHBhZGRpbmctdG9wOiA0cHggIWltcG9ydGFudDtcbiAgICAgICAgICBwYWRkaW5nLWJvdHRvbTogNHB4ICFpbXBvcnRhbnQ7XG4gICAgICAgIH1cbiAgICAgICAgLmRlbnNpdHktY29tZm9ydGFibGUgdGgsIC5kZW5zaXR5LWNvbWZvcnRhYmxlIHRkIHtcbiAgICAgICAgICBwYWRkaW5nLXRvcDogMTZweCAhaW1wb3J0YW50O1xuICAgICAgICAgIHBhZGRpbmctYm90dG9tOiAxNnB4ICFpbXBvcnRhbnQ7XG4gICAgICAgIH1cbiAgICAgICAgLmRlbnNpdHktc3BhY2lvdXMgdGgsIC5kZW5zaXR5LXNwYWNpb3VzIHRkIHtcbiAgICAgICAgICBwYWRkaW5nLXRvcDogMzJweCAhaW1wb3J0YW50O1xuICAgICAgICAgIHBhZGRpbmctYm90dG9tOiAzMnB4ICFpbXBvcnRhbnQ7XG4gICAgICAgIH1cbiAgICAgIGB9PC9zdHlsZT5cbiAgICAgIFxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgcm91bmRlZC1sZyBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMC81MCBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MC8zMCB2aWEtdHJhbnNwYXJlbnQgdG8taW5kaWdvLTUwLzIwXCIgLz5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHAtM1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICA8RG9jdW1lbnRUZXh0SWNvbiBjbGFzc05hbWU9XCJoLTE0IHctMTQgdGV4dC1saW1lLTYwMCAtbXQtMlwiIC8+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG10LTJcIj5cbiAgICAgICAgICAgICAgICAgIEJsb2cgTWFuYWdlbWVudFxuICAgICAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICBDcmVhdGUsIGVkaXQsIGFuZCBtYW5hZ2UgeW91ciBibG9nIGNvbnRlbnQuXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBsZzpmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQWRkQ2xpY2t9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHRleHQtc20gZm9udC1tZWRpdW0gcm91bmRlZC1tZCBob3ZlcjpiZy1ibHVlLTcwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6cmluZy1vZmZzZXQtMiB0cmFuc2l0aW9uLWNvbG9ycyBzaGFkb3ctc21cIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFBsdXNJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgQWRkIEJsb2cgUG9zdFxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBDb250ZW50IEFyZWEgKi99XG4gICAgICA8ZGl2XG4gICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSByb3VuZGVkLWxnIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwLzUwIG92ZXJmbG93LWhpZGRlblwiXG4gICAgICAgIHJvbGU9XCJtYWluXCJcbiAgICAgICAgYXJpYS1sYWJlbD1cIkJsb2cgbWFuYWdlbWVudCBzZWN0aW9uXCJcbiAgICAgID5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIiBzdHlsZT17eyBwYWRkaW5nQm90dG9tOiAnMCcgfX0+XG4gICAgICAgICAgey8qIFNlYXJjaCBhbmQgVmlldyBDb250cm9scyAtIFJlc3BvbnNpdmUgRGVzaWduICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zIHAtMiBiZy13aGl0ZSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDAgc2hhZG93LXNtXCI+XG4gICAgICAgICAgICB7LyogTW9iaWxlIExheW91dCAtIFN0YWNrZWQgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc3BhY2UteS0zIGxnOmhpZGRlblwiPlxuICAgICAgICAgICAgICB7LyogU2VhcmNoIEJhciAtIEZ1bGwgV2lkdGggb24gTW9iaWxlICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgIDxNYWduaWZ5aW5nR2xhc3NJY29uIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGgtNCB3LTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaCBibG9nIHBvc3RzLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFF1ZXJ5fVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFF1ZXJ5KGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicGwtMTAgcHItNCBweS0yLjUgdy1mdWxsIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDAgdGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIE1vYmlsZSBDb250cm9scyAtIFNpbmdsZSBSb3cgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XG4gICAgICAgICAgICAgIHsvKiBWaWV3IE1vZGUgVG9nZ2xlIC0gU3RyZXRjaGVkICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGJnLWdyYXktMTAwIHJvdW5kZWQtbGcgcC0wLjUgZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVmlld01vZGVDaGFuZ2UoJ2xpc3QnKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXgtMSBweC0yIHB5LTEuNSByb3VuZGVkLW1kIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0wLjUgJHtcbiAgICAgICAgICAgICAgICAgICAgdmlld01vZGUgPT09ICdsaXN0J1xuICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLXdoaXRlIHRleHQtYmx1ZS02MDAgc2hhZG93LXNtJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgdGl0bGU9XCJMaXN0IHZpZXdcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxMaXN0QnVsbGV0SWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gaGlkZGVuIHhzOmlubGluZVwiPkxpc3Q8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVmlld01vZGVDaGFuZ2UoJ2dyaWQnKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXgtMSBweC0yIHB5LTEuNSByb3VuZGVkLW1kIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0wLjUgJHtcbiAgICAgICAgICAgICAgICAgICAgdmlld01vZGUgPT09ICdncmlkJ1xuICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLXdoaXRlIHRleHQtYmx1ZS02MDAgc2hhZG93LXNtJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgdGl0bGU9XCJHcmlkIHZpZXdcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxTcXVhcmVzMlgySWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gaGlkZGVuIHhzOmlubGluZVwiPkdyaWQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBHcmlkIENvbHVtbnMgQ29udHJvbCAoZm9yIGdyaWQgdmlldykgKi99XG4gICAgICAgICAgICAgIHt2aWV3TW9kZSA9PT0gJ2dyaWQnICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGJnLWdyYXktMTAwIHJvdW5kZWQtbGcgcC0wLjUgZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgcHgtMVwiPkNvbDo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0wLjUgZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgIHtbMSwgMiwgMywgNF0ubWFwKChudW0pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBrZXk9e251bX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUdyaWRDb2x1bW5zQ2hhbmdlKG51bSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4LTEgcHgtMS41IHB5LTEgcm91bmRlZCB0ZXh0LXhzIGZvbnQtbWVkaXVtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGdyaWRDb2x1bW5zID09PSBudW1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy13aGl0ZSB0ZXh0LWdyZWVuLTYwMCBzaGFkb3ctc20nXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT17YCR7bnVtfSBjb2x1bW4ke251bSA+IDEgPyAncycgOiAnJ31gfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtudW19XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgey8qIFRhYmxlIENvbHVtbnMgQ29udHJvbCAoZm9yIGxpc3QgdmlldykgKi99XG4gICAgICAgICAgICAgIHt2aWV3TW9kZSA9PT0gJ2xpc3QnICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93Q29sdW1uU2VsZWN0b3IoIXNob3dDb2x1bW5TZWxlY3Rvcil9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHgtMiBweS0xLjUgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTUwXCJcbiAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJDb2x1bW5zXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPEFkanVzdG1lbnRzSG9yaXpvbnRhbEljb24gY2xhc3NOYW1lPVwiaC0zIHctMyBtci0wLjVcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJoaWRkZW4geHM6aW5saW5lXCI+Q29sPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8Q2hldnJvbkRvd25JY29uIGNsYXNzTmFtZT1cImgtMyB3LTMgbWwtMC41XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIHsvKiBGaWx0ZXJzIEJ1dHRvbiAtIFN0cmV0Y2hlZCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93RmlsdGVycyghc2hvd0ZpbHRlcnMpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweC0yIHB5LTEuNSB0ZXh0LXhzIGZvbnQtbWVkaXVtIHJvdW5kZWQtbGcgYm9yZGVyICR7XG4gICAgICAgICAgICAgICAgICAgIHNob3dGaWx0ZXJzIHx8IE9iamVjdC5rZXlzKGZpbHRlcnMpLnNvbWUoa2V5ID0+IGZpbHRlcnNba2V5XSlcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTUwIHRleHQtYmx1ZS03MDAgYm9yZGVyLWJsdWUtMzAwJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLXdoaXRlIHRleHQtZ3JheS03MDAgYm9yZGVyLWdyYXktMzAwIGhvdmVyOmJnLWdyYXktNTAnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgIHRpdGxlPVwiRmlsdGVyc1wiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPEZ1bm5lbEljb24gY2xhc3NOYW1lPVwiaC0zIHctMyBtci0wLjVcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaGlkZGVuIHhzOmlubGluZVwiPkZpbHRlcjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIHtPYmplY3Qua2V5cyhmaWx0ZXJzKS5zb21lKGtleSA9PiBmaWx0ZXJzW2tleV0pICYmIChcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMC41IGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0xIHB5LTAuNSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSBiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge09iamVjdC52YWx1ZXMoZmlsdGVycykuZmlsdGVyKEJvb2xlYW4pLmxlbmd0aH1cbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIERlbnNpdHkgQ29udHJvbCAtIFN0cmV0Y2hlZCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93V2luZG93TGlzdCghc2hvd1dpbmRvd0xpc3QpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweC0yIHB5LTEuNSB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktNTBcIlxuICAgICAgICAgICAgICAgICAgdGl0bGU9XCJEZW5zaXR5XCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8QWRqdXN0bWVudHNIb3Jpem9udGFsSWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zIG1yLTAuNVwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJoaWRkZW4geHM6aW5saW5lXCI+e2RlbnNpdHkuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyBkZW5zaXR5LnNsaWNlKDEpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxDaGV2cm9uRG93bkljb24gY2xhc3NOYW1lPVwiaC0zIHctMyBtbC0wLjVcIiAvPlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBEZXNrdG9wIExheW91dCAtIEhvcml6b250YWwgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIGxnOmZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIHsvKiBTZWFyY2ggQmFyIGFuZCBGaWx0ZXJzICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgZmxleC0xIG1heC13LW1kXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8TWFnbmlmeWluZ0dsYXNzSWNvbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBoLTQgdy00IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWFyY2ggYmxvZyBwb3N0cyBieSB0aXRsZSwgY29udGVudCwgZXhjZXJwdC4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFF1ZXJ5fVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoUXVlcnkoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInBsLTEwIHByLTQgcHktMiB3LWZ1bGwgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMCB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICB7LyogRmlsdGVycyBEcm9wZG93biAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgZHJvcGRvd24tY29udGFpbmVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93RmlsdGVycyghc2hvd0ZpbHRlcnMpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0zIHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSByb3VuZGVkLWxnIGJvcmRlciBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNob3dGaWx0ZXJzIHx8IE9iamVjdC5rZXlzKGZpbHRlcnMpLnNvbWUoa2V5ID0+IGZpbHRlcnNba2V5XSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctYmx1ZS01MCB0ZXh0LWJsdWUtNzAwIGJvcmRlci1ibHVlLTMwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctd2hpdGUgdGV4dC1ncmF5LTcwMCBib3JkZXItZ3JheS0zMDAgaG92ZXI6YmctZ3JheS01MCdcbiAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIlNob3cvaGlkZSBmaWx0ZXJzXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxGdW5uZWxJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgRmlsdGVyc1xuICAgICAgICAgICAgICAgICAgICAgIHtPYmplY3Qua2V5cyhmaWx0ZXJzKS5zb21lKGtleSA9PiBmaWx0ZXJzW2tleV0pICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTEuNSBweS0wLjUgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7T2JqZWN0LnZhbHVlcyhmaWx0ZXJzKS5maWx0ZXIoQm9vbGVhbikubGVuZ3RofVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPENoZXZyb25Eb3duSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IG1sLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogRmlsdGVycyBEcm9wZG93biAqL31cbiAgICAgICAgICAgICAgICAgICAge3Nob3dGaWx0ZXJzICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC1mdWxsIHJpZ2h0LTAgbXQtMSB3LTgwIGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZC1sZyBzaGFkb3ctbGcgei01MFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5GaWx0ZXJzPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93RmlsdGVycyhmYWxzZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxYTWFya0ljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29uZmlnLmZpbHRlcnM/Lm1hcCgoZmlsdGVyKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17ZmlsdGVyLmtleX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmaWx0ZXIubGFiZWx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZmlsdGVyc1tmaWx0ZXIua2V5XSB8fCAnJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0ZpbHRlcnMgPSB7IC4uLmZpbHRlcnMgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGUudGFyZ2V0LnZhbHVlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5ld0ZpbHRlcnNbZmlsdGVyLmtleV0gPSBlLnRhcmdldC52YWx1ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVsZXRlIG5ld0ZpbHRlcnNbZmlsdGVyLmtleV1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldEZpbHRlcnMobmV3RmlsdGVycylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgdGV4dC1zbSBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmaWx0ZXIub3B0aW9ucz8ubWFwKChvcHRpb24pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24ga2V5PXtvcHRpb24udmFsdWV9IHZhbHVlPXtvcHRpb24udmFsdWV9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7b3B0aW9uLmxhYmVsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIG10LTQgcHQtNCBib3JkZXItdCBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldEZpbHRlcnMoe30pXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNob3dGaWx0ZXJzKGZhbHNlKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMS41IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBiZy1ncmF5LTEwMCBob3ZlcjpiZy1ncmF5LTIwMCByb3VuZGVkLWxnXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBDbGVhciBBbGxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIERlc2t0b3AgQ29udHJvbHMgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgIHsvKiBWaWV3IE1vZGUgVG9nZ2xlICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5WaWV3Ojwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBiZy1ncmF5LTEwMCByb3VuZGVkLWxnIHAtMSBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVZpZXdNb2RlQ2hhbmdlKCdsaXN0Jyl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC0zIHB5LTIgcm91bmRlZC1tZCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICB2aWV3TW9kZSA9PT0gJ2xpc3QnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctd2hpdGUgdGV4dC1ibHVlLTYwMCBzaGFkb3ctc20nXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIkxpc3Qgdmlld1wiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPExpc3RCdWxsZXRJY29uIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPkxpc3Q8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVmlld01vZGVDaGFuZ2UoJ2dyaWQnKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTMgcHktMiByb3VuZGVkLW1kIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZpZXdNb2RlID09PSAnZ3JpZCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy13aGl0ZSB0ZXh0LWJsdWUtNjAwIHNoYWRvdy1zbSdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JheS05MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiR3JpZCB2aWV3XCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U3F1YXJlczJYMkljb24gY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+R3JpZDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIEdyaWQgQ29sdW1ucyBDb250cm9sIChmb3IgZ3JpZCB2aWV3KSAqL31cbiAgICAgICAgICAgICAgICAgIHt2aWV3TW9kZSA9PT0gJ2dyaWQnICYmIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPkNvbHVtbnM6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgYmctZ3JheS0xMDAgcm91bmRlZC1sZyBwLTEgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlR3JpZENvbHVtbnNDaGFuZ2UoMSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTIgcHktMSByb3VuZGVkIHRleHQteHMgZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBncmlkQ29sdW1ucyA9PT0gMVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctd2hpdGUgdGV4dC1ncmVlbi02MDAgc2hhZG93LXNtJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCIxIGNvbHVtblwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDFcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVHcmlkQ29sdW1uc0NoYW5nZSgyKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtMiBweS0xIHJvdW5kZWQgdGV4dC14cyBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdyaWRDb2x1bW5zID09PSAyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy13aGl0ZSB0ZXh0LWdyZWVuLTYwMCBzaGFkb3ctc20nXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JheS05MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIjIgY29sdW1uc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVHcmlkQ29sdW1uc0NoYW5nZSgzKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtMiBweS0xIHJvdW5kZWQgdGV4dC14cyBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdyaWRDb2x1bW5zID09PSAzXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy13aGl0ZSB0ZXh0LWdyZWVuLTYwMCBzaGFkb3ctc20nXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JheS05MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIjMgY29sdW1uc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDNcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVHcmlkQ29sdW1uc0NoYW5nZSg0KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtMiBweS0xIHJvdW5kZWQgdGV4dC14cyBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdyaWRDb2x1bW5zID09PSA0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy13aGl0ZSB0ZXh0LWdyZWVuLTYwMCBzaGFkb3ctc20nXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JheS05MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIjQgY29sdW1uc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDRcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBUYWJsZSBDb2x1bW5zIENvbnRyb2wgKGZvciBsaXN0IHZpZXcpICovfVxuICAgICAgICAgICAgICAgICAge3ZpZXdNb2RlID09PSAnbGlzdCcgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGRyb3Bkb3duLWNvbnRhaW5lclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dDb2x1bW5TZWxlY3Rvcighc2hvd0NvbHVtblNlbGVjdG9yKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0zIHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTUwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIlNlbGVjdCBjb2x1bW5zIHRvIGRpc3BsYXlcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxBZGp1c3RtZW50c0hvcml6b250YWxJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICBDb2x1bW5zXG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2hldnJvbkRvd25JY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgbWwtMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICAgICAgICB7LyogQ29sdW1uIFNlbGVjdG9yIERyb3Bkb3duICovfVxuICAgICAgICAgICAgICAgICAgICAgIHtzaG93Q29sdW1uU2VsZWN0b3IgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtZnVsbCBsZWZ0LTAgbXQtMSB3LTQ4IGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZC1sZyBzaGFkb3ctbGcgei01MFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlIG1iLTJcIj5TaG93IENvbHVtbnM8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29uZmlnLmNvbHVtbnMubWFwKChjb2x1bW4pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBrZXk9e2NvbHVtbi5rZXl9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBweS0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17dmlzaWJsZUNvbHVtbnMuaW5jbHVkZXMoY29sdW1uLmtleSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZS50YXJnZXQuY2hlY2tlZCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRWaXNpYmxlQ29sdW1ucyhwcmV2ID0+IFsuLi5wcmV2LCBjb2x1bW4ua2V5XSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRWaXNpYmxlQ29sdW1ucyhwcmV2ID0+IHByZXYuZmlsdGVyKGNvbCA9PiBjb2wgIT09IGNvbHVtbi5rZXkpKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ibHVlLTYwMCBmb2N1czpyaW5nLWJsdWUtNTAwIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbHVtbi5sYWJlbH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBEZW5zaXR5ICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBkcm9wZG93bi1jb250YWluZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dXaW5kb3dMaXN0KCFzaG93V2luZG93TGlzdCl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktNTAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIlNlbGVjdCBkZW5zaXR5XCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxBZGp1c3RtZW50c0hvcml6b250YWxJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAge2RlbnNpdHkuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyBkZW5zaXR5LnNsaWNlKDEpfVxuICAgICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uRG93bkljb24gY2xhc3NOYW1lPVwiaC00IHctNCBtbC0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIERlbnNpdHkgRHJvcGRvd24gKi99XG4gICAgICAgICAgICAgICAgICAgIHtzaG93V2luZG93TGlzdCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtZnVsbCBsZWZ0LTAgbXQtMSB3LTMyIGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZC1sZyBzaGFkb3ctbGcgei01MFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgeyhbJ2NvbXBhY3QnLCAnY29tZm9ydGFibGUnLCAnc3BhY2lvdXMnXSBhcyBjb25zdCkubWFwKChvcHRpb24pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e29wdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlRGVuc2l0eUNoYW5nZShvcHRpb24pXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNob3dXaW5kb3dMaXN0KGZhbHNlKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCB0ZXh0LWxlZnQgcHgtMyBweS0yIHRleHQtc20gcm91bmRlZC1tZCBob3ZlcjpiZy1ncmF5LTEwMCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZW5zaXR5ID09PSBvcHRpb24gPyAnYmctYmx1ZS01MCB0ZXh0LWJsdWUtNzAwIGZvbnQtbWVkaXVtJyA6ICd0ZXh0LWdyYXktNzAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge29wdGlvbi5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIG9wdGlvbi5zbGljZSgxKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBNb2JpbGUgRHJvcGRvd25zICovfVxuICAgICAgICAgICAgICB7LyogRmlsdGVycyBEcm9wZG93biAtIE1vYmlsZSAqL31cbiAgICAgICAgICAgICAge3Nob3dGaWx0ZXJzICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmhpZGRlbiBiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGcgc2hhZG93LWxnIHAtNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+RmlsdGVyczwvaDM+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93RmlsdGVycyhmYWxzZSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNjAwXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxYTWFya0ljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgIHtjb25maWcuZmlsdGVycz8ubWFwKChmaWx0ZXIpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17ZmlsdGVyLmtleX0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2ZpbHRlci5sYWJlbH1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmaWx0ZXJzW2ZpbHRlci5rZXldIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXdGaWx0ZXJzID0geyAuLi5maWx0ZXJzIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZS50YXJnZXQudmFsdWUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5ld0ZpbHRlcnNbZmlsdGVyLmtleV0gPSBlLnRhcmdldC52YWx1ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZWxldGUgbmV3RmlsdGVyc1tmaWx0ZXIua2V5XVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRGaWx0ZXJzKG5ld0ZpbHRlcnMpXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgdGV4dC1zbSBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2ZpbHRlci5vcHRpb25zPy5tYXAoKG9wdGlvbikgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24ga2V5PXtvcHRpb24udmFsdWV9IHZhbHVlPXtvcHRpb24udmFsdWV9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge29wdGlvbi5sYWJlbH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIG10LTQgcHQtNCBib3JkZXItdCBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldEZpbHRlcnMoe30pXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRTaG93RmlsdGVycyhmYWxzZSlcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMS41IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBiZy1ncmF5LTEwMCBob3ZlcjpiZy1ncmF5LTIwMCByb3VuZGVkLWxnXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIENsZWFyIEFsbFxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogQnVsayBBY3Rpb25zIEJhciAtIExpc3QgYW5kIEdyaWQgVmlld3MgKi99XG4gICAgICAgICAge2NvbmZpZy5lbmFibGVCdWxrQWN0aW9ucyAmJiBzZWxlY3RlZFBvc3RzLmxlbmd0aCA+IDAgJiYgKHZpZXdNb2RlID09PSAnbGlzdCcgfHwgdmlld01vZGUgPT09ICdncmlkJykgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTUwIGJvcmRlciBib3JkZXItYmx1ZS0yMDAgcm91bmRlZC1sZyBweC00IHB5LTIgc2hhZG93LXNtXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTYgaC02IGJnLWJsdWUtMTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCBmb250LXNlbWlib2xkIHRleHQteHNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkUG9zdHMubGVuZ3RofVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ibHVlLTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgYmxvZyBwb3N0e3NlbGVjdGVkUG9zdHMubGVuZ3RoID09PSAxID8gJycgOiAncyd9IHNlbGVjdGVkXG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgIHsvKiBCdWxrIEFjdGlvbiBCdXR0b25zIC0gTXVsdGktYmxvZyBvcGVyYXRpb25zICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMS41XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQnVsa0FjdGlvbigncHVibGlzaCcpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIgcHktMSB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JlZW4tNzAwIGJnLWdyZWVuLTEwMCBob3ZlcjpiZy1ncmVlbi0yMDAgYm9yZGVyIGJvcmRlci1ncmVlbi0zMDAgcm91bmRlZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0xIGZvY3VzOnJpbmctZ3JlZW4tNTAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIlB1Ymxpc2ggc2VsZWN0ZWQgYmxvZyBwb3N0c1wiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFBvd2VySWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgUHVibGlzaFxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUJ1bGtBY3Rpb24oJ3VucHVibGlzaCcpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIgcHktMSB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtb3JhbmdlLTcwMCBiZy1vcmFuZ2UtMTAwIGhvdmVyOmJnLW9yYW5nZS0yMDAgYm9yZGVyIGJvcmRlci1vcmFuZ2UtMzAwIHJvdW5kZWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMSBmb2N1czpyaW5nLW9yYW5nZS01MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiVW5wdWJsaXNoIHNlbGVjdGVkIGJsb2cgcG9zdHNcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxFeWVTbGFzaEljb24gY2xhc3NOYW1lPVwiaC0zIHctMyBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIFVucHVibGlzaFxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNob3dCdWxrRGVsZXRlQ29uZmlybWF0aW9uKCl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMiBweS0xIHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1yZWQtNzAwIGJnLXJlZC0xMDAgaG92ZXI6YmctcmVkLTIwMCBib3JkZXIgYm9yZGVyLXJlZC0zMDAgcm91bmRlZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0xIGZvY3VzOnJpbmctcmVkLTUwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJEZWxldGUgc2VsZWN0ZWQgYmxvZyBwb3N0c1wiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRyYXNoSWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgRGVsZXRlXG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIHsvKiBDbGVhciBTZWxlY3Rpb24gQnV0dG9uICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTEuNVwiPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRQb3N0cyhbXSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIgcHktMSB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgYmctd2hpdGUgaG92ZXI6YmctZ3JheS01MCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMSBmb2N1czpyaW5nLWdyYXktNTAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJDbGVhciBzZWxlY3Rpb25cIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPFhNYXJrSWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIENsZWFyXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgey8qIEVycm9yIFN0YXRlICovfVxuICAgICAgICAgICAge2Vycm9yICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC02IHB5LTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8WE1hcmtJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1yZWQtNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtcmVkLTgwMFwiPkVycm9yPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTIgdGV4dC1zbSB0ZXh0LXJlZC03MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwPntlcnJvcn08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRFcnJvcihudWxsKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmZXRjaEJsb2dQb3N0cygpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1yZWQtMTAwIHB4LTMgcHktMiByb3VuZGVkLW1kIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1yZWQtODAwIGhvdmVyOmJnLXJlZC0yMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBUcnkgQWdhaW5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBMb2FkaW5nIFN0YXRlICovfVxuICAgICAgICAgICAge2xvYWRpbmcgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTYgcHktOFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTggdy04IGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0zIHRleHQtZ3JheS02MDBcIj5Mb2FkaW5nIGJsb2cgcG9zdHMuLi48L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgey8qIERhdGEgRGlzcGxheSAqL31cbiAgICAgICAgICAgIHshbG9hZGluZyAmJiAhZXJyb3IgJiYgKFxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIHtibG9nUG9zdHMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICAgICAgLyogRW1wdHkgU3RhdGUgKi9cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0xMiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8RG9jdW1lbnRUZXh0SWNvbiBjbGFzc05hbWU9XCJteC1hdXRvIGgtMTIgdy0xMiB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cIm10LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+Tm8gYmxvZyBwb3N0cyBmb3VuZDwvaDM+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2RlYm91bmNlZFNlYXJjaFF1ZXJ5ID8gJ1RyeSBhZGp1c3RpbmcgeW91ciBzZWFyY2ggdGVybXMuJyA6ICdHZXQgc3RhcnRlZCBieSBjcmVhdGluZyB5b3VyIGZpcnN0IGJsb2cgcG9zdC4nfVxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIHtjb25maWcucGVybWlzc2lvbnM/LmNyZWF0ZSAmJiAhZGVib3VuY2VkU2VhcmNoUXVlcnkgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVBZGRDbGlja31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctYmx1ZS03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBBZGQgQmxvZyBQb3N0XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkgOiB2aWV3TW9kZSA9PT0gJ2xpc3QnID8gKFxuICAgICAgICAgICAgICAgICAgLyogVGFibGUgVmlldyAqL1xuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvdmVyZmxvdy14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPXtgbWluLXctZnVsbCBkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDAgZGVuc2l0eS0ke2RlbnNpdHl9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dGhlYWQgY2xhc3NOYW1lPVwiYmctZ3JheS0yMDAgYm9yZGVyLWIgYm9yZGVyLWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0cj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogQ2hlY2tib3ggQ29sdW1uICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjb25maWcuZW5hYmxlQnVsa0FjdGlvbnMgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRoIHNjb3BlPVwiY29sXCIgY2xhc3NOYW1lPVwicmVsYXRpdmUgcGwtMiBweS0yXCIgc3R5bGU9e3sgd2lkdGg6ICc2cHgnIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3NlbGVjdGVkUG9zdHMubGVuZ3RoID09PSBibG9nUG9zdHMubGVuZ3RoICYmIGJsb2dQb3N0cy5sZW5ndGggPiAwfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlU2VsZWN0QWxsKGUudGFyZ2V0LmNoZWNrZWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtYmx1ZS02MDAgZm9jdXM6cmluZy1ibHVlLTUwMCBib3JkZXItZ3JheS0zMDAgcm91bmRlZCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVuc2l0eSA9PT0gJ2NvbXBhY3QnID8gJ2gtNCB3LTQnIDogZGVuc2l0eSA9PT0gJ3NwYWNpb3VzJyA/ICdoLTUgdy01JyA6ICdoLTQgdy00J1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIERhdGEgQ29sdW1ucyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0VmlzaWJsZUZpZWxkcygpLm1hcCgoZmllbGQpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0aFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2ZpZWxkLmtleX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGU9XCJjb2xcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC02IHB5LTIgdGV4dC1sZWZ0IGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyIGN1cnNvci1wb2ludGVyIGhvdmVyOmJnLWdyYXktMTAwIHRleHQteHNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTb3J0KGZpZWxkLmtleSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e2ZpZWxkLmxhYmVsfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c29ydEJ5ID09PSBmaWVsZC5rZXkgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzb3J0T3JkZXIgPT09ICdhc2MnID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QXJyb3dVcEljb24gY2xhc3NOYW1lPVwiaC0zIHctMyB0ZXh0LWJsYWNrXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBcnJvd0Rvd25JY29uIGNsYXNzTmFtZT1cImgtMyB3LTMgdGV4dC1ibGFja1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBcnJvd1VwSWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zIHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBBY3Rpb25zIENvbHVtbiAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29uZmlnLmFjdGlvbnMgJiYgY29uZmlnLmFjdGlvbnMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGggc2NvcGU9XCJjb2xcIiBjbGFzc05hbWU9XCJweC02IHB5LTIgdGV4dC1yaWdodCBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlciB0ZXh0LXhzIG1pbi13LVsxMjBweF1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+QWN0aW9uczwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGhlYWQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dGJvZHkgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtibG9nUG9zdHMubWFwKChwb3N0KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRyIGtleT17cG9zdC5pZH0gY2xhc3NOYW1lPXtgaG92ZXI6YmctZ3JheS01MCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRQb3N0cy5pbmNsdWRlcyhwb3N0LmlkKSA/ICdiZy1ibHVlLTUwIGJvcmRlci1sLTQgYm9yZGVyLWwtYmx1ZS01MDAnIDogJydcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogQ2hlY2tib3ggKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29uZmlnLmVuYWJsZUJ1bGtBY3Rpb25zICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtzZWxlY3RlZFBvc3RzLmluY2x1ZGVzKHBvc3QuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVTZWxlY3RQb3N0KHBvc3QuaWQsIGUudGFyZ2V0LmNoZWNrZWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZCBib3JkZXItZ3JheS0zMDAgdGV4dC1ibHVlLTYwMCBmb2N1czpyaW5nLWJsdWUtNTAwIGJnLXdoaXRlICFiZy13aGl0ZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICd3aGl0ZScgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIERhdGEgQ2VsbHMgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0VmlzaWJsZUZpZWxkcygpLm1hcCgoZmllbGQpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGtleT17ZmllbGQua2V5fSBjbGFzc05hbWU9XCJweC02IHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2ZpZWxkLmtleSA9PT0gJ3RpdGxlJyA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJsb2dBdmF0YXJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT17cG9zdC50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmZWF0dXJlZEltYWdlVXJsPXtwb3N0LmZlYXR1cmVkSW1hZ2VVcmx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtci0zXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPntwb3N0LnRpdGxlfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+e3Bvc3Quc2x1Z308L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogZmllbGQua2V5ID09PSAnaXNQdWJsaXNoZWQnID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggcHgtMiBweS0xIHRleHQteHMgZm9udC1zZW1pYm9sZCByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcG9zdC5pc1B1Ymxpc2hlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cG9zdC5pc1B1Ymxpc2hlZCA/ICdQdWJsaXNoZWQnIDogJ0RyYWZ0J31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogZmllbGQua2V5ID09PSAnZXhjZXJwdCcgfHwgZmllbGQua2V5ID09PSAnY29udGVudCcgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTkwMCBtYXgtdy14cyB0cnVuY2F0ZVwiIHRpdGxlPXtwb3N0W2ZpZWxkLmtleV19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dHJ1bmNhdGVUZXh0KHBvc3RbZmllbGQua2V5XSB8fCAnJywgNTApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IGZpZWxkLmtleSA9PT0gJ3RhZ3MnIHx8IGZpZWxkLmtleSA9PT0gJ2NhdGVnb3JpZXMnID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Bvc3RbZmllbGQua2V5XSA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7KHBvc3RbZmllbGQua2V5XSBhcyBzdHJpbmcpLnNwbGl0KCcsJykuc2xpY2UoMCwgMikubWFwKCh0YWc6IHN0cmluZywgaW5kZXg6IG51bWJlcikgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBweC0yIHB5LTEgdGV4dC14cyBiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFnLnRyaW0oKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7KHBvc3RbZmllbGQua2V5XSBhcyBzdHJpbmcpLnNwbGl0KCcsJykubGVuZ3RoID4gMiAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPit7KHBvc3RbZmllbGQua2V5XSBhcyBzdHJpbmcpLnNwbGl0KCcsJykubGVuZ3RoIC0gMn0gbW9yZTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAnLSd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogZmllbGQua2V5ID09PSAnY3JlYXRlZEF0JyB8fCBmaWVsZC5rZXkgPT09ICd1cGRhdGVkQXQnIHx8IGZpZWxkLmtleSA9PT0gJ3B1Ymxpc2hlZEF0JyA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwb3N0W2ZpZWxkLmtleV0gPyBmb3JtYXREYXRlKHBvc3RbZmllbGQua2V5XSBhcyBzdHJpbmcpIDogJy0nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwb3N0W2ZpZWxkLmtleV0gfHwgJy0nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogQWN0aW9ucyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjb25maWcuYWN0aW9ucyAmJiBjb25maWcuYWN0aW9ucy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1yaWdodCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjb25maWcuYWN0aW9ucy5tYXAoKGFjdGlvbikgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBpc0xvYWRpbmcgPSBhY3Rpb25Mb2FkaW5nID09PSBgJHthY3Rpb24uYWN0aW9ufS0ke3Bvc3QuaWR9YDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgSWNvbkNvbXBvbmVudCA9IGFjdGlvbi5pY29uID09PSAnRXllSWNvbicgPyBFeWVTbGFzaEljb24gOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY3Rpb24uaWNvbiA9PT0gJ1BlbmNpbEljb24nID8gUGVuY2lsSWNvbiA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGlvbi5pY29uID09PSAnUG93ZXJJY29uJyA/IFBvd2VySWNvbiA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGlvbi5pY29uID09PSAnVHJhc2hJY29uJyA/IFRyYXNoSWNvbiA6IERvY3VtZW50VGV4dEljb247XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2FjdGlvbi5hY3Rpb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVBY3Rpb24oYWN0aW9uLmFjdGlvbiwgcG9zdCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0xIHJvdW5kZWQtbWQgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWN0aW9uLnZhcmlhbnQgPT09ICdwcmltYXJ5JyA/ICd0ZXh0LWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNTAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWN0aW9uLnZhcmlhbnQgPT09ICdzZWNvbmRhcnknID8gJ3RleHQtZ3JheS02MDAgaG92ZXI6YmctZ3JheS01MCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY3Rpb24udmFyaWFudCA9PT0gJ3dhcm5pbmcnID8gJ3RleHQteWVsbG93LTYwMCBob3ZlcjpiZy15ZWxsb3ctNTAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWN0aW9uLnZhcmlhbnQgPT09ICdkYW5nZXInID8gJ3RleHQtcmVkLTYwMCBob3ZlcjpiZy1yZWQtNTAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ3RleHQtZ3JheS02MDAgaG92ZXI6YmctZ3JheS01MCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gJHtpc0xvYWRpbmcgPyAnb3BhY2l0eS01MCBjdXJzb3Itbm90LWFsbG93ZWQnIDogJyd9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPXthY3Rpb24udG9vbHRpcH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC00IHctNCBib3JkZXItYi0yIGJvcmRlci1jdXJyZW50XCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SWNvbkNvbXBvbmVudCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3Rib2R5PlxuICAgICAgICAgICAgICAgICAgICAgIDwvdGFibGU+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSA6IHZpZXdNb2RlID09PSAnZ3JpZCcgPyAoXG4gICAgICAgICAgICAgICAgICAvKiBHcmlkIFZpZXcgLSBSZXN0cnVjdHVyZWQgKi9cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtnZXRHcmlkRGVuc2l0eUNsYXNzZXMoKS5jb250YWluZXJ9PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGdyaWQgJHtnZXRHcmlkRGVuc2l0eUNsYXNzZXMoKS5ncmlkfSAke1xuICAgICAgICAgICAgICAgICAgICAgIGdyaWRDb2x1bW5zID09PSAxID8gJ2dyaWQtY29scy0xJyA6XG4gICAgICAgICAgICAgICAgICAgICAgZ3JpZENvbHVtbnMgPT09IDIgPyAnZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTInIDpcbiAgICAgICAgICAgICAgICAgICAgICBncmlkQ29sdW1ucyA9PT0gMyA/ICdncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMycgOlxuICAgICAgICAgICAgICAgICAgICAgICdncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyB4bDpncmlkLWNvbHMtNCdcbiAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgIHtibG9nUG9zdHMubWFwKChwb3N0KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8R3JpZENhcmQga2V5PXtwb3N0LmlkfSBwb3N0PXtwb3N0fSAvPlxuICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAvKiBDYXJkIFZpZXcgKi9cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAge2Jsb2dQb3N0cy5tYXAoKHBvc3QpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtwb3N0LmlkfSBjbGFzc05hbWU9e2BiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGcgc2hhZG93LXNtIGhvdmVyOnNoYWRvdy1tZCB0cmFuc2l0aW9uLXNoYWRvdyAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZFBvc3RzLmluY2x1ZGVzKHBvc3QuaWQpID8gJ3JpbmctMiByaW5nLWJsdWUtNTAwIGJnLWJsdWUtNTAnIDogJydcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIEZlYXR1cmVkIEltYWdlICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy00OCBoLTMyIGJnLWdyYXktMTAwIHJvdW5kZWQtbC1sZyBvdmVyZmxvdy1oaWRkZW4gZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Bvc3QuZmVhdHVyZWRJbWFnZVVybCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNyYz17cG9zdC5mZWF0dXJlZEltYWdlVXJsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsdD17cG9zdC50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RG9jdW1lbnRUZXh0SWNvbiBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogQ29udGVudCAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogVGl0bGUgYW5kIFNsdWcgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwb3N0LnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwb3N0LnNsdWd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIEV4Y2VycHQgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Bvc3QuZXhjZXJwdCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWItMyBsaW5lLWNsYW1wLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Bvc3QuZXhjZXJwdH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIE1ldGEgSW5mb3JtYXRpb24gKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTQgdGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCBweC0yIHB5LTEgdGV4dC14cyBmb250LXNlbWlib2xkIHJvdW5kZWQtZnVsbCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwb3N0LmlzUHVibGlzaGVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwb3N0LmlzUHVibGlzaGVkID8gJ1B1Ymxpc2hlZCcgOiAnRHJhZnQnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+VXBkYXRlZDoge2Zvcm1hdERhdGUocG9zdC51cGRhdGVkQXQpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwb3N0LmNhdGVnb3JpZXMgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5DYXRlZ29yeToge3Bvc3QuY2F0ZWdvcmllcy5zcGxpdCgnLCcpWzBdLnRyaW0oKX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogQWN0aW9ucyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbWwtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjb25maWcuYWN0aW9ucz8ubWFwKChhY3Rpb24pID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzTG9hZGluZyA9IGFjdGlvbkxvYWRpbmcgPT09IGAke2FjdGlvbi5hY3Rpb259LSR7cG9zdC5pZH1gO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgSWNvbkNvbXBvbmVudCA9IGFjdGlvbi5pY29uID09PSAnRXllSWNvbicgPyBFeWVTbGFzaEljb24gOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWN0aW9uLmljb24gPT09ICdQZW5jaWxJY29uJyA/IFBlbmNpbEljb24gOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWN0aW9uLmljb24gPT09ICdQb3dlckljb24nID8gUG93ZXJJY29uIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGlvbi5pY29uID09PSAnVHJhc2hJY29uJyA/IFRyYXNoSWNvbiA6IERvY3VtZW50VGV4dEljb247XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2FjdGlvbi5hY3Rpb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQWN0aW9uKGFjdGlvbi5hY3Rpb24sIHBvc3QpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0yIHJvdW5kZWQtbWQgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGlvbi52YXJpYW50ID09PSAncHJpbWFyeScgPyAndGV4dC1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTUwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY3Rpb24udmFyaWFudCA9PT0gJ3NlY29uZGFyeScgPyAndGV4dC1ncmF5LTYwMCBob3ZlcjpiZy1ncmF5LTUwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY3Rpb24udmFyaWFudCA9PT0gJ3dhcm5pbmcnID8gJ3RleHQteWVsbG93LTYwMCBob3ZlcjpiZy15ZWxsb3ctNTAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGlvbi52YXJpYW50ID09PSAnZGFuZ2VyJyA/ICd0ZXh0LXJlZC02MDAgaG92ZXI6YmctcmVkLTUwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAndGV4dC1ncmF5LTYwMCBob3ZlcjpiZy1ncmF5LTUwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gJHtpc0xvYWRpbmcgPyAnb3BhY2l0eS01MCBjdXJzb3Itbm90LWFsbG93ZWQnIDogJyd9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT17YWN0aW9uLnRvb2x0aXB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNCB3LTQgYm9yZGVyLWItMiBib3JkZXItY3VycmVudFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SWNvbkNvbXBvbmVudCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSl9XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogQ2hlY2tib3ggZm9yIGJ1bGsgYWN0aW9ucyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29uZmlnLmVuYWJsZUJ1bGtBY3Rpb25zICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtzZWxlY3RlZFBvc3RzLmluY2x1ZGVzKHBvc3QuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVNlbGVjdFBvc3QocG9zdC5pZCwgZS50YXJnZXQuY2hlY2tlZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQgYm9yZGVyLWdyYXktMzAwIHRleHQtYmx1ZS02MDAgZm9jdXM6cmluZy1ibHVlLTUwMCBiZy13aGl0ZSAhYmctd2hpdGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICd3aGl0ZScgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNb2RhbHMgKi99XG4gICAgICA8QmxvZ01vZGFsXG4gICAgICAgIGlzT3Blbj17aXNDcmVhdGVNb2RhbE9wZW59XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldElzQ3JlYXRlTW9kYWxPcGVuKGZhbHNlKX1cbiAgICAgICAgb25TdWJtaXQ9e2hhbmRsZUNyZWF0ZX1cbiAgICAgICAgdGl0bGU9XCJDcmVhdGUgTmV3IEJsb2cgUG9zdFwiXG4gICAgICAvPlxuXG4gICAgICA8QmxvZ01vZGFsXG4gICAgICAgIGlzT3Blbj17aXNFZGl0TW9kYWxPcGVufVxuICAgICAgICBvbkNsb3NlPXsoKSA9PiB7XG4gICAgICAgICAgc2V0SXNFZGl0TW9kYWxPcGVuKGZhbHNlKTtcbiAgICAgICAgICBzZXRFZGl0aW5nUG9zdChudWxsKTtcbiAgICAgICAgfX1cbiAgICAgICAgb25TdWJtaXQ9e2FzeW5jIChmb3JtRGF0YSkgPT4ge1xuICAgICAgICAgIGlmIChlZGl0aW5nUG9zdCkge1xuICAgICAgICAgICAgYXdhaXQgaGFuZGxlVXBkYXRlKGVkaXRpbmdQb3N0LmlkLCBmb3JtRGF0YSk7XG4gICAgICAgICAgfVxuICAgICAgICB9fVxuICAgICAgICB0aXRsZT1cIkVkaXQgQmxvZyBQb3N0XCJcbiAgICAgICAgaW5pdGlhbERhdGE9e2VkaXRpbmdQb3N0ID8/IHVuZGVmaW5lZH1cbiAgICAgIC8+XG5cbiAgICAgIHsvKiBDb25maXJtYXRpb24gTW9kYWwgKi99XG4gICAgICA8Q29uZmlybWF0aW9uTW9kYWxcbiAgICAgICAgaXNPcGVuPXtkZWxldGVDb25maXJtYXRpb24uaXNPcGVufVxuICAgICAgICB0aXRsZT1cIkRlbGV0ZSBDb25maXJtYXRpb25cIlxuICAgICAgICBtZXNzYWdlPVwiQXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGRlbGV0ZSB0aGlzIGJsb2cgcG9zdD9cIlxuICAgICAgICBkZXRhaWxzPXsoKCkgPT4ge1xuICAgICAgICAgIGlmIChkZWxldGVDb25maXJtYXRpb24uaXNCdWxrRGVsZXRlKSB7XG4gICAgICAgICAgICBjb25zdCBjb3VudCA9IGRlbGV0ZUNvbmZpcm1hdGlvbi5idWxrUG9zdHM/Lmxlbmd0aCB8fCAwO1xuICAgICAgICAgICAgcmV0dXJuIGBUaGlzIGFjdGlvbiB3aWxsIHBlcm1hbmVudGx5IGRlbGV0ZSAke2NvdW50fSBibG9nIHBvc3Qke2NvdW50ID09PSAxID8gJycgOiAncyd9LiBUaGlzIGNhbm5vdCBiZSB1bmRvbmUuYDtcbiAgICAgICAgICB9XG4gICAgICAgICAgY29uc3QgcG9zdCA9IGRlbGV0ZUNvbmZpcm1hdGlvbi5wb3N0O1xuICAgICAgICAgIHJldHVybiBgVGhpcyBhY3Rpb24gd2lsbCBwZXJtYW5lbnRseSBkZWxldGUgXCIke3Bvc3Q/LnRpdGxlIHx8ICd0aGlzIGJsb2cgcG9zdCd9XCIuIFRoaXMgY2Fubm90IGJlIHVuZG9uZS5gO1xuICAgICAgICB9KSgpfVxuICAgICAgICBjb25maXJtVGV4dD1cIkRlbGV0ZVwiXG4gICAgICAgIGNhbmNlbFRleHQ9XCJDYW5jZWxcIlxuICAgICAgICBvbkNvbmZpcm09e2NvbmZpcm1EZWxldGV9XG4gICAgICAgIG9uQ2FuY2VsPXtjYW5jZWxEZWxldGV9XG4gICAgICAgIHR5cGU9XCJkYW5nZXJcIlxuICAgICAgICBzaG93VmVyaWZpY2F0aW9uPXt0cnVlfVxuICAgICAgICB2ZXJpZmljYXRpb25EYXRhPXt7XG4gICAgICAgICAgY2FuRGVsZXRlOiB0cnVlLFxuICAgICAgICAgIHJlYXNvbjogZGVsZXRlQ29uZmlybWF0aW9uLmlzQnVsa0RlbGV0ZSBcbiAgICAgICAgICAgID8gYCR7ZGVsZXRlQ29uZmlybWF0aW9uLmJ1bGtQb3N0cz8ubGVuZ3RoIHx8IDB9IGJsb2cgcG9zdCR7KGRlbGV0ZUNvbmZpcm1hdGlvbi5idWxrUG9zdHM/Lmxlbmd0aCB8fCAwKSA9PT0gMSA/ICcnIDogJ3MnfSBzZWxlY3RlZCBmb3IgZGVsZXRpb25gXG4gICAgICAgICAgICA6IGBCbG9nIHBvc3QgXCIke2RlbGV0ZUNvbmZpcm1hdGlvbi5wb3N0Py50aXRsZSB8fCAnVW5rbm93bid9XCIgcmVhZHkgZm9yIGRlbGV0aW9uYFxuICAgICAgICB9fVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiRXllU2xhc2hJY29uIiwiRXllSWNvbiIsIkFycm93VXBJY29uIiwiQXJyb3dEb3duSWNvbiIsIlBlbmNpbEljb24iLCJUcmFzaEljb24iLCJQb3dlckljb24iLCJEb2N1bWVudFRleHRJY29uIiwiRG9jdW1lbnREdXBsaWNhdGVJY29uIiwiQXJjaGl2ZUJveEljb24iLCJYTWFya0ljb24iLCJNYWduaWZ5aW5nR2xhc3NJY29uIiwiUGx1c0ljb24iLCJMaXN0QnVsbGV0SWNvbiIsIlNxdWFyZXMyWDJJY29uIiwiRnVubmVsSWNvbiIsIkNoZXZyb25Eb3duSWNvbiIsIkFkanVzdG1lbnRzSG9yaXpvbnRhbEljb24iLCJCbG9nTW9kYWwiLCJ1c2VOb3RpZmljYXRpb25zIiwiQ29uZmlybWF0aW9uTW9kYWwiLCJCbG9nQXZhdGFyIiwidGl0bGUiLCJmZWF0dXJlZEltYWdlVXJsIiwic2l6ZSIsImNsYXNzTmFtZSIsInN0eWxlIiwiaW1hZ2VFcnJvciIsInNldEltYWdlRXJyb3IiLCJpbWFnZUxvYWRpbmciLCJzZXRJbWFnZUxvYWRpbmciLCJzaXplQ2xhc3NlcyIsInhzIiwic20iLCJtZCIsImxnIiwieGwiLCJpY29uU2l6ZXMiLCJ0ZXh0U2l6ZXMiLCJnZXRJbml0aWFscyIsInNwbGl0IiwibWFwIiwid29yZCIsImNoYXJBdCIsImpvaW4iLCJ0b1VwcGVyQ2FzZSIsInNsaWNlIiwiZ2V0QmFja2dyb3VuZENvbG9yIiwiY29sb3JzIiwiaGFzaCIsImkiLCJsZW5ndGgiLCJjaGFyQ29kZUF0IiwiTWF0aCIsImFicyIsImhhbmRsZUltYWdlTG9hZCIsImhhbmRsZUltYWdlRXJyb3IiLCJiYXNlQ2xhc3NlcyIsImRpdiIsImltZyIsInNyYyIsImFsdCIsIm9uTG9hZCIsIm9uRXJyb3IiLCJzcGFuIiwiQmxvZ3NNYW5hZ2VtZW50IiwiY29uZmlnIiwiZGVsZXRlQ29uZmlybWF0aW9uIiwic2hvd1N1Y2Nlc3MiLCJzaG93RXJyb3IiLCJzaG93V2FybmluZyIsInNob3dJbmZvIiwic2hvd0xvYWRpbmciLCJjbGVhckxvYWRpbmdOb3RpZmljYXRpb25zIiwiYmxvZ1Bvc3RzIiwic2V0QmxvZ1Bvc3RzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwic2VhcmNoUXVlcnkiLCJzZXRTZWFyY2hRdWVyeSIsImRlYm91bmNlZFNlYXJjaFF1ZXJ5Iiwic2V0RGVib3VuY2VkU2VhcmNoUXVlcnkiLCJjdXJyZW50UGFnZSIsInNldEN1cnJlbnRQYWdlIiwidG90YWxQYWdlcyIsInNldFRvdGFsUGFnZXMiLCJzb3J0QnkiLCJzZXRTb3J0QnkiLCJkZWZhdWx0U29ydCIsImZpZWxkIiwic29ydE9yZGVyIiwic2V0U29ydE9yZGVyIiwiZGlyZWN0aW9uIiwic2VsZWN0ZWRQb3N0cyIsInNldFNlbGVjdGVkUG9zdHMiLCJ2aWV3TW9kZSIsInNldFZpZXdNb2RlIiwiZGVmYXVsdFZpZXdTZXR0aW5ncyIsIm1vZGUiLCJkZW5zaXR5Iiwic2V0RGVuc2l0eSIsInZpc2libGVDb2x1bW5zIiwic2V0VmlzaWJsZUNvbHVtbnMiLCJBcnJheSIsImlzQXJyYXkiLCJmaWx0ZXJzIiwic2V0RmlsdGVycyIsInNob3dGaWx0ZXJzIiwic2V0U2hvd0ZpbHRlcnMiLCJhY3Rpb25Mb2FkaW5nIiwic2V0QWN0aW9uTG9hZGluZyIsInNob3dDb2x1bW5TZWxlY3RvciIsInNldFNob3dDb2x1bW5TZWxlY3RvciIsInNob3dXaW5kb3dMaXN0Iiwic2V0U2hvd1dpbmRvd0xpc3QiLCJncmlkQ29sdW1ucyIsInNldEdyaWRDb2x1bW5zIiwiaXNDcmVhdGVNb2RhbE9wZW4iLCJzZXRJc0NyZWF0ZU1vZGFsT3BlbiIsImlzRWRpdE1vZGFsT3BlbiIsInNldElzRWRpdE1vZGFsT3BlbiIsImVkaXRpbmdQb3N0Iiwic2V0RWRpdGluZ1Bvc3QiLCJzZXREZWxldGVDb25maXJtYXRpb24iLCJpc09wZW4iLCJwb3N0IiwiaXNCdWxrRGVsZXRlIiwiYnVsa1Bvc3RzIiwidGltZXIiLCJzZXRUaW1lb3V0IiwiY2xlYXJUaW1lb3V0IiwiZmV0Y2hCbG9nUG9zdHMiLCJwcmVzZXJ2ZUZvY3VzIiwicGFyYW1zIiwiVVJMU2VhcmNoUGFyYW1zIiwicGFnZSIsInRvU3RyaW5nIiwibGltaXQiLCJwYWdlU2l6ZSIsInNlYXJjaCIsIk9iamVjdCIsImVudHJpZXMiLCJmb3JFYWNoIiwia2V5IiwidmFsdWUiLCJhcHBlbmQiLCJjb25zb2xlIiwibG9nIiwicmVzcG9uc2UiLCJmZXRjaCIsImVuZHBvaW50Iiwib2siLCJlcnJvclRleHQiLCJ0ZXh0IiwiRXJyb3IiLCJzdGF0dXMiLCJkYXRhIiwianNvbiIsInN1Y2Nlc3MiLCJwb3N0cyIsImNlaWwiLCJ0b3RhbENvdW50IiwiZXJyIiwibWVzc2FnZSIsImlzU2VhcmNoaW5nIiwiaGFuZGxlQ3JlYXRlIiwiZm9ybURhdGEiLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJlcnJvck1lc3NhZ2UiLCJoYW5kbGVBZGRDbGljayIsImhhbmRsZVVwZGF0ZSIsImlkIiwiaGFuZGxlRGVsZXRlIiwicG9zdFRvRGVsZXRlIiwiZmluZCIsInNob3dEZWxldGVDb25maXJtYXRpb24iLCJzaG93QnVsa0RlbGV0ZUNvbmZpcm1hdGlvbiIsInBvc3RzVG9EZWxldGUiLCJmaWx0ZXIiLCJpbmNsdWRlcyIsImNvbmZpcm1EZWxldGUiLCJwcm9taXNlcyIsIlByb21pc2UiLCJhbGwiLCJjYW5jZWxEZWxldGUiLCJoYW5kbGVCdWxrQWN0aW9uIiwiYWN0aW9uIiwiaXNQdWJsaXNoZWQiLCJoYW5kbGVBY3Rpb24iLCJpdGVtIiwibmV3U3RhdHVzIiwid2FybiIsImhhbmRsZVNvcnQiLCJuZXdPcmRlciIsIm5ld0ZpZWxkIiwiaGFuZGxlU2VsZWN0QWxsIiwiY2hlY2tlZCIsImhhbmRsZVNlbGVjdFBvc3QiLCJwIiwicG9zdElkIiwiZ2V0VmlzaWJsZUZpZWxkcyIsImZpZWxkcyIsImhhbmRsZVZpZXdNb2RlQ2hhbmdlIiwiaGFuZGxlRGVuc2l0eUNoYW5nZSIsIm5ld0RlbnNpdHkiLCJoYW5kbGVHcmlkQ29sdW1uc0NoYW5nZSIsImNvbHVtbnMiLCJoYW5kbGVGaWx0ZXJDaGFuZ2UiLCJuZXdGaWx0ZXJzIiwiR3JpZENhcmQiLCJpc1NlbGVjdGVkIiwiY2xhc3NlcyIsImdldEdyaWREZW5zaXR5Q2xhc3NlcyIsImNhcmQiLCJvbkNsaWNrIiwiZSIsIndpbmRvdyIsImlubmVyV2lkdGgiLCJhY3Rpb25NZW51IiwiY3VycmVudFRhcmdldCIsInF1ZXJ5U2VsZWN0b3IiLCJjbGFzc0xpc3QiLCJjb250YWlucyIsInJlbW92ZSIsIm9wYWNpdHkiLCJ0cmFuc2Zvcm0iLCJwb2ludGVyRXZlbnRzIiwib25Nb3VzZUxlYXZlIiwiY29udGVudCIsImgzIiwiZXhjZXJwdCIsIm1ldGEiLCJkYXRlIiwiZm9ybWF0RGF0ZSIsInVwZGF0ZWRBdCIsImNhdGVnb3JpZXMiLCJjYXRlZ29yeSIsImluZGV4IiwidHJpbSIsImJ1dHRvbiIsInN0b3BQcm9wYWdhdGlvbiIsImNsb3Nlc3QiLCJpc1Zpc2libGUiLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJlbmFibGVCdWxrQWN0aW9ucyIsImNoZWNrYm94IiwiaW5wdXQiLCJ0eXBlIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJiYWNrZ3JvdW5kQ29sb3IiLCJjb250YWluZXIiLCJjb21wYWN0IiwiY29tZm9ydGFibGUiLCJzcGFjaW91cyIsImdyaWQiLCJhY3Rpb25zIiwiYnV0dG9ucyIsImljb24iLCJkYXRlU3RyaW5nIiwiRGF0ZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsInRydW5jYXRlVGV4dCIsIm1heExlbmd0aCIsInN1YnN0cmluZyIsImgxIiwicm9sZSIsImFyaWEtbGFiZWwiLCJwYWRkaW5nQm90dG9tIiwicGxhY2Vob2xkZXIiLCJudW0iLCJrZXlzIiwic29tZSIsInZhbHVlcyIsIkJvb2xlYW4iLCJsYWJlbCIsInNlbGVjdCIsIm9wdGlvbnMiLCJvcHRpb24iLCJjb2x1bW4iLCJwcmV2IiwiY29sIiwicGVybWlzc2lvbnMiLCJjcmVhdGUiLCJ0YWJsZSIsInRoZWFkIiwidHIiLCJ0aCIsInNjb3BlIiwid2lkdGgiLCJ0Ym9keSIsInRkIiwic2x1ZyIsInRhZyIsImlzTG9hZGluZyIsIkljb25Db21wb25lbnQiLCJkaXNhYmxlZCIsInRvb2x0aXAiLCJ2YXJpYW50Iiwib25DbG9zZSIsIm9uU3VibWl0IiwiaW5pdGlhbERhdGEiLCJ1bmRlZmluZWQiLCJkZXRhaWxzIiwiY291bnQiLCJjb25maXJtVGV4dCIsImNhbmNlbFRleHQiLCJvbkNvbmZpcm0iLCJvbkNhbmNlbCIsInNob3dWZXJpZmljYXRpb24iLCJ2ZXJpZmljYXRpb25EYXRhIiwiY2FuRGVsZXRlIiwicmVhc29uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/blog/blogs-management.tsx\n"));

/***/ })

});