"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/blog/page",{

/***/ "(app-pages-browser)/./src/components/admin/blog/blogs-management.tsx":
/*!********************************************************!*\
  !*** ./src/components/admin/blog/blogs-management.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlogsManagement: () => (/* binding */ BlogsManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PowerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentDuplicateIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArchiveBoxIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js\");\n/* harmony import */ var _blog_modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./blog-modal */ \"(app-pages-browser)/./src/components/admin/blog/blog-modal.tsx\");\n/* harmony import */ var _components_providers_notification_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/notification-provider */ \"(app-pages-browser)/./src/components/providers/notification-provider.tsx\");\n/* harmony import */ var _shared_confirmation_modal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/confirmation-modal */ \"(app-pages-browser)/./src/components/admin/shared/confirmation-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ BlogsManagement auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nfunction BlogAvatar(param) {\n    let { title, featuredImageUrl, size = 'md', className = '', style = {} } = param;\n    _s();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [imageLoading, setImageLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // Size configurations\n    const sizeClasses = {\n        xs: 'w-6 h-6',\n        sm: 'w-8 h-8',\n        md: 'w-12 h-12',\n        lg: 'w-16 h-16',\n        xl: 'w-24 h-24',\n        'full-height': 'w-full h-full'\n    };\n    const iconSizes = {\n        xs: 'w-3 h-3',\n        sm: 'w-4 h-4',\n        md: 'w-6 h-6',\n        lg: 'w-8 h-8',\n        xl: 'w-12 h-12',\n        'full-height': 'w-16 h-16'\n    };\n    const textSizes = {\n        xs: 'text-xs',\n        sm: 'text-sm',\n        md: 'text-base',\n        lg: 'text-lg',\n        xl: 'text-xl',\n        'full-height': 'text-4xl'\n    };\n    // Generate initials from blog title\n    const getInitials = (title)=>{\n        return title.split(' ').map((word)=>word.charAt(0)).join('').toUpperCase().slice(0, 2);\n    };\n    // Generate a consistent color based on the blog title\n    const getBackgroundColor = (title)=>{\n        const colors = [\n            'bg-blue-500',\n            'bg-green-500',\n            'bg-purple-500',\n            'bg-pink-500',\n            'bg-indigo-500',\n            'bg-yellow-500',\n            'bg-red-500',\n            'bg-teal-500',\n            'bg-orange-500',\n            'bg-cyan-500'\n        ];\n        let hash = 0;\n        for(let i = 0; i < title.length; i++){\n            hash = title.charCodeAt(i) + ((hash << 5) - hash);\n        }\n        return colors[Math.abs(hash) % colors.length];\n    };\n    const handleImageLoad = ()=>{\n        setImageLoading(false);\n        setImageError(false);\n    };\n    const handleImageError = ()=>{\n        setImageLoading(false);\n        setImageError(true);\n    };\n    const baseClasses = \"\\n    \".concat(sizeClasses[size], \" \\n    rounded-lg \\n    flex \\n    items-center \\n    justify-center \\n    overflow-hidden \\n    \").concat(size === 'full-height' ? 'min-h-[320px]' : '', \"\\n    \").concat(className, \"\\n  \");\n    // If we have a valid featured image URL and no error, show the image\n    if (featuredImageUrl && !imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(baseClasses, \" bg-gray-100 relative\"),\n            style: style,\n            children: [\n                imageLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 w-6 h-6\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: featuredImageUrl,\n                    alt: \"\".concat(title, \" featured image\"),\n                    className: \"\\n            \".concat(size === 'full-height' ? 'w-full h-full object-cover' : 'w-full h-full object-cover', \"\\n            \").concat(imageLoading ? 'opacity-0' : 'opacity-100', \"\\n            transition-opacity duration-200\\n          \"),\n                    onLoad: handleImageLoad,\n                    onError: handleImageError\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 283,\n            columnNumber: 7\n        }, this);\n    }\n    // Fallback: Show initials with colored background\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\\n        \".concat(baseClasses, \" \\n        \").concat(getBackgroundColor(title), \" \\n        text-white \\n        font-semibold \\n        \").concat(textSizes[size], \"\\n        shadow-sm\\n      \"),\n        style: style,\n        title: title,\n        children: size === 'full-height' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-24 h-24 text-white opacity-80\"\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: getInitials(title)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm opacity-90 break-words line-clamp-3\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 322,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            children: getInitials(title)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 330,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n        lineNumber: 309,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogAvatar, \"yA6MC4/13YXgE42AlKw5vrWMK58=\");\n_c = BlogAvatar;\nfunction BlogsManagement(param) {\n    let { config } = param;\n    var _config_defaultSort, _config_defaultSort1, _config_defaultViewSettings, _config_defaultViewSettings1, _config_defaultViewSettings2, _config_defaultViewSettings3, _config_defaultViewSettings4, _config_defaultViewSettings5, _config_filters, _config_filters1, _config_permissions, _deleteConfirmation_bulkPosts, _deleteConfirmation_bulkPosts1, _deleteConfirmation_post;\n    _s1();\n    // Notification system\n    const { showSuccess, showError, showWarning, showInfo, showLoading, clearLoadingNotifications } = (0,_components_providers_notification_provider__WEBPACK_IMPORTED_MODULE_4__.useNotifications)();\n    // State management\n    const [blogPosts, setBlogPosts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [debouncedSearchQuery, setDebouncedSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultSort = config.defaultSort) === null || _config_defaultSort === void 0 ? void 0 : _config_defaultSort.field) || 'updatedAt');\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultSort1 = config.defaultSort) === null || _config_defaultSort1 === void 0 ? void 0 : _config_defaultSort1.direction) === 'asc' ? 'asc' : 'desc');\n    const [selectedPosts, setSelectedPosts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultViewSettings = config.defaultViewSettings) === null || _config_defaultViewSettings === void 0 ? void 0 : _config_defaultViewSettings.mode) === 'list' || ((_config_defaultViewSettings1 = config.defaultViewSettings) === null || _config_defaultViewSettings1 === void 0 ? void 0 : _config_defaultViewSettings1.mode) === 'grid' ? config.defaultViewSettings.mode : 'list');\n    const [density, setDensity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultViewSettings2 = config.defaultViewSettings) === null || _config_defaultViewSettings2 === void 0 ? void 0 : _config_defaultViewSettings2.density) === 'compact' || ((_config_defaultViewSettings3 = config.defaultViewSettings) === null || _config_defaultViewSettings3 === void 0 ? void 0 : _config_defaultViewSettings3.density) === 'comfortable' || ((_config_defaultViewSettings4 = config.defaultViewSettings) === null || _config_defaultViewSettings4 === void 0 ? void 0 : _config_defaultViewSettings4.density) === 'spacious' ? config.defaultViewSettings.density : 'comfortable');\n    const [visibleColumns, setVisibleColumns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(Array.isArray((_config_defaultViewSettings5 = config.defaultViewSettings) === null || _config_defaultViewSettings5 === void 0 ? void 0 : _config_defaultViewSettings5.visibleColumns) ? config.defaultViewSettings.visibleColumns : []);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showColumnSelector, setShowColumnSelector] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showWindowList, setShowWindowList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [gridColumns, setGridColumns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(3);\n    // Modal states\n    const [isCreateModalOpen, setIsCreateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isEditModalOpen, setIsEditModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [editingPost, setEditingPost] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Confirmation modal state\n    const [deleteConfirmation, setDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        isOpen: false,\n        post: null,\n        isBulkDelete: false,\n        bulkPosts: []\n    });\n    // Debounce search query\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BlogsManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"BlogsManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchQuery(searchQuery);\n                    setCurrentPage(1); // Reset to first page when searching\n                }\n            }[\"BlogsManagement.useEffect.timer\"], 300);\n            return ({\n                \"BlogsManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"BlogsManagement.useEffect\"];\n        }\n    }[\"BlogsManagement.useEffect\"], [\n        searchQuery\n    ]);\n    // Fetch blog posts\n    const fetchBlogPosts = async function() {\n        let preserveFocus = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        setLoading(true);\n        if (!preserveFocus) {\n            showLoading('Loading Blog Posts', 'Retrieving blog posts...');\n        }\n        try {\n            const params = new URLSearchParams({\n                page: currentPage.toString(),\n                limit: (config.pageSize || 10).toString(),\n                search: debouncedSearchQuery,\n                sortBy,\n                sortOrder\n            });\n            // Add filters to params\n            Object.entries(filters).forEach((param)=>{\n                let [key, value] = param;\n                if (value) {\n                    params.append(key, value);\n                }\n            });\n            console.log('Fetching blog posts with params:', params.toString()); // Debug log\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"?\").concat(params));\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('API Error Response:', errorText);\n                throw new Error(\"HTTP error! status: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const data = await response.json();\n            console.log('Received blog posts data:', data); // Debug log\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to fetch blog posts');\n            }\n            setBlogPosts(data.posts || []);\n            setTotalPages(Math.ceil((data.totalCount || 0) / (config.pageSize || 10)));\n            setError(null); // Clear any previous errors on successful fetch\n            if (!preserveFocus) {\n                var _data_posts, _data_posts1;\n                showSuccess('Blog Posts Loaded', \"Loaded \".concat(((_data_posts = data.posts) === null || _data_posts === void 0 ? void 0 : _data_posts.length) || 0, \" blog post\").concat(((_data_posts1 = data.posts) === null || _data_posts1 === void 0 ? void 0 : _data_posts1.length) === 1 ? '' : 's'));\n            }\n        } catch (err) {\n            console.error('Error fetching blog posts:', err); // Debug log\n            setError(err instanceof Error ? err.message : 'Failed to fetch blog posts');\n            if (!preserveFocus) {\n                showError('Failed to Load Blog Posts', 'Unable to retrieve blog posts');\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BlogsManagement.useEffect\": ()=>{\n            // Preserve focus when searching\n            const isSearching = debouncedSearchQuery !== '';\n            fetchBlogPosts(isSearching);\n        }\n    }[\"BlogsManagement.useEffect\"], [\n        currentPage,\n        debouncedSearchQuery,\n        sortBy,\n        sortOrder,\n        filters\n    ]);\n    // Handle create\n    const handleCreate = async (formData)=>{\n        try {\n            showLoading('Creating Blog Post', 'Saving new blog post...');\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to create blog post (\".concat(response.status, \")\"));\n            }\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to create blog post');\n            }\n            setIsCreateModalOpen(false);\n            fetchBlogPosts();\n            showSuccess('Blog Post Created', '\"'.concat(formData.title || 'New blog post', '\" created successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to create blog post';\n            setError(errorMessage);\n            showError('Failed to Create Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Handle add button click\n    const handleAddClick = ()=>{\n        showInfo('Opening Create Form', 'Preparing to create a new blog post...');\n        setIsCreateModalOpen(true);\n    };\n    // Handle update\n    const handleUpdate = async (id, formData)=>{\n        try {\n            showLoading('Updating Blog Post', 'Saving changes...');\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to update blog post (\".concat(response.status, \")\"));\n            }\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to update blog post');\n            }\n            setIsEditModalOpen(false);\n            setEditingPost(null);\n            fetchBlogPosts();\n            showSuccess('Blog Post Updated', '\"'.concat(formData.title || 'Blog post', '\" updated successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to update blog post';\n            setError(errorMessage);\n            showError('Failed to Update Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Handle delete\n    const handleDelete = async (id)=>{\n        try {\n            const postToDelete = blogPosts.find((post)=>post.id === id);\n            showLoading('Deleting Blog Post', 'Removing \"'.concat((postToDelete === null || postToDelete === void 0 ? void 0 : postToDelete.title) || 'blog post', '\"...'));\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete blog post');\n            fetchBlogPosts();\n            showSuccess('Blog Post Deleted', '\"'.concat((postToDelete === null || postToDelete === void 0 ? void 0 : postToDelete.title) || 'Blog post', '\" deleted successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to delete blog post';\n            setError(errorMessage);\n            showError('Failed to Delete Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Show delete confirmation\n    const showDeleteConfirmation = (post)=>{\n        setDeleteConfirmation({\n            isOpen: true,\n            post,\n            isBulkDelete: false,\n            bulkPosts: []\n        });\n    };\n    // Show bulk delete confirmation\n    const showBulkDeleteConfirmation = ()=>{\n        const postsToDelete = blogPosts.filter((post)=>selectedPosts.includes(post.id));\n        setDeleteConfirmation({\n            isOpen: true,\n            post: null,\n            isBulkDelete: true,\n            bulkPosts: postsToDelete\n        });\n    };\n    // Confirm delete\n    const confirmDelete = async ()=>{\n        try {\n            if (deleteConfirmation.isBulkDelete && deleteConfirmation.bulkPosts) {\n                // Bulk delete\n                const promises = deleteConfirmation.bulkPosts.map((post)=>fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(post.id), {\n                        method: 'DELETE'\n                    }));\n                await Promise.all(promises);\n                setSelectedPosts([]);\n                fetchBlogPosts();\n                showSuccess('Blog Posts Deleted', \"\".concat(deleteConfirmation.bulkPosts.length, \" blog post(s) deleted successfully!\"));\n            } else if (deleteConfirmation.post) {\n                // Single delete\n                await handleDelete(deleteConfirmation.post.id);\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to delete blog post(s)';\n            showError('Failed to Delete Blog Post(s)', errorMessage);\n        } finally{\n            setDeleteConfirmation({\n                isOpen: false,\n                post: null,\n                isBulkDelete: false,\n                bulkPosts: []\n            });\n        }\n    };\n    // Cancel delete\n    const cancelDelete = ()=>{\n        setDeleteConfirmation({\n            isOpen: false,\n            post: null,\n            isBulkDelete: false,\n            bulkPosts: []\n        });\n    };\n    // Handle bulk actions\n    const handleBulkAction = async (action)=>{\n        if (selectedPosts.length === 0) return;\n        setActionLoading(action);\n        try {\n            if (action === 'delete') {\n                showBulkDeleteConfirmation();\n                return;\n            }\n            showLoading(\"Bulk \".concat(action), \"Processing \".concat(selectedPosts.length, \" blog post\").concat(selectedPosts.length === 1 ? '' : 's', \"...\"));\n            const promises = selectedPosts.map(async (id)=>{\n                switch(action){\n                    case 'publish':\n                        return fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                            method: 'PUT',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                isPublished: true\n                            })\n                        });\n                    case 'unpublish':\n                        return fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                            method: 'PUT',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                isPublished: false\n                            })\n                        });\n                    default:\n                        throw new Error(\"Unknown bulk action: \".concat(action));\n                }\n            });\n            await Promise.all(promises);\n            setSelectedPosts([]);\n            fetchBlogPosts();\n            showSuccess(\"Bulk \".concat(action, \" completed\"), \"\".concat(selectedPosts.length, \" blog post\").concat(selectedPosts.length === 1 ? '' : 's', \" \").concat(action, \"ed successfully!\"));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"Failed to \".concat(action, \" blog posts\");\n            setError(errorMessage);\n            showError(\"Failed to \".concat(action, \" blog posts\"), errorMessage);\n        } finally{\n            setActionLoading(null);\n        }\n    };\n    // Handle individual actions\n    const handleAction = async (action, item)=>{\n        setActionLoading(\"\".concat(action, \"-\").concat(item.id));\n        try {\n            switch(action){\n                case 'edit':\n                    showInfo('Opening Editor', 'Editing \"'.concat(item.title, '\"'));\n                    setEditingPost(item);\n                    setIsEditModalOpen(true);\n                    break;\n                case 'view':\n                    showInfo('Opening View', 'Viewing \"'.concat(item.title, '\"'));\n                    // TODO: Implement view functionality\n                    showInfo('View Blog Post', 'Opening \"'.concat(item.title, '\" in new tab'));\n                    break;\n                case 'delete':\n                    showInfo('Delete Confirmation', 'Preparing to delete \"'.concat(item.title, '\"'));\n                    showDeleteConfirmation(item);\n                    break;\n                case 'toggle-published':\n                    const newStatus = !item.isPublished;\n                    showLoading(newStatus ? 'Publishing Blog Post' : 'Unpublishing Blog Post', \"\".concat(newStatus ? 'Publishing' : 'Unpublishing', ' \"').concat(item.title, '\"...'));\n                    await handleUpdate(item.id, {\n                        isPublished: newStatus\n                    });\n                    showSuccess(newStatus ? 'Blog Post Published' : 'Blog Post Unpublished', '\"'.concat(item.title, '\" ').concat(newStatus ? 'published' : 'unpublished', \" successfully!\"));\n                    break;\n                case 'duplicate':\n                    showLoading('Duplicating Blog Post', 'Creating copy of \"'.concat(item.title, '\"...'));\n                    // TODO: Implement duplicate functionality\n                    showSuccess('Blog Post Duplicated', '\"'.concat(item.title, '\" duplicated successfully!'));\n                    break;\n                case 'archive':\n                    showLoading('Archiving Blog Post', 'Archiving \"'.concat(item.title, '\"...'));\n                    // TODO: Implement archive functionality\n                    showSuccess('Blog Post Archived', '\"'.concat(item.title, '\" archived successfully!'));\n                    break;\n                default:\n                    console.warn(\"Unknown action: \".concat(action));\n            }\n        } finally{\n            setActionLoading(null);\n        }\n    };\n    // Handle sorting\n    const handleSort = (field)=>{\n        const newOrder = sortBy === field ? sortOrder === 'asc' ? 'desc' : 'asc' : 'asc';\n        const newField = sortBy === field ? field : field;\n        setSortBy(newField);\n        setSortOrder(newOrder);\n        setCurrentPage(1);\n        showInfo('Sorting Blog Posts', \"Sorting by \".concat(field, \" (\").concat(newOrder, \")\"));\n    };\n    // Handle selection\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedPosts(blogPosts.map((post)=>post.id));\n            showInfo('Selection Updated', \"Selected all \".concat(blogPosts.length, \" blog post\").concat(blogPosts.length === 1 ? '' : 's'));\n        } else {\n            setSelectedPosts([]);\n            showInfo('Selection Cleared', 'Deselected all blog posts');\n        }\n    };\n    const handleSelectPost = (id, checked)=>{\n        if (checked) {\n            setSelectedPosts([\n                ...selectedPosts,\n                id\n            ]);\n            const post = blogPosts.find((p)=>p.id === id);\n            showInfo('Post Selected', '\"'.concat((post === null || post === void 0 ? void 0 : post.title) || 'Blog post', '\" added to selection'));\n        } else {\n            setSelectedPosts(selectedPosts.filter((postId)=>postId !== id));\n            const post = blogPosts.find((p)=>p.id === id);\n            showInfo('Post Deselected', '\"'.concat((post === null || post === void 0 ? void 0 : post.title) || 'Blog post', '\" removed from selection'));\n        }\n    };\n    // Get visible fields for table\n    const getVisibleFields = ()=>{\n        if (visibleColumns.length > 0) {\n            var _config_fields;\n            return ((_config_fields = config.fields) === null || _config_fields === void 0 ? void 0 : _config_fields.filter((field)=>visibleColumns.includes(field.key))) || [];\n        }\n        return config.fields || [];\n    };\n    // Handle view mode change\n    const handleViewModeChange = (mode)=>{\n        setViewMode(mode);\n        showInfo('View Mode Changed', \"Switched to \".concat(mode, \" view\"));\n    };\n    // Handle density change\n    const handleDensityChange = (newDensity)=>{\n        setDensity(newDensity);\n        showInfo('Density Updated', \"Changed to \".concat(newDensity, \" density\"));\n    };\n    // Handle grid columns change\n    const handleGridColumnsChange = (columns)=>{\n        setGridColumns(columns);\n        showInfo('Grid Layout Updated', \"Changed to \".concat(columns, \" column\").concat(columns === 1 ? '' : 's', \" layout\"));\n    };\n    // Handle filter changes\n    const handleFilterChange = (key, value)=>{\n        const newFilters = {\n            ...filters\n        };\n        if (value) {\n            newFilters[key] = value;\n        } else {\n            delete newFilters[key];\n        }\n        setFilters(newFilters);\n        showInfo('Filter Applied', \"Filter updated: \".concat(key, \" = \").concat(value || 'all'));\n    };\n    // Grid Card Component\n    const GridCard = (param)=>{\n        let { post } = param;\n        const isSelected = selectedPosts.includes(post.id);\n        const classes = getGridDensityClasses();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"group relative bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 \".concat(isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : '', \" \").concat(classes.card),\n            onClick: (e)=>{\n                // Hide action menu when clicking on card (mobile only)\n                if (window.innerWidth <= 1024) {\n                    const actionMenu = e.currentTarget.querySelector('.action-menu');\n                    if (actionMenu && actionMenu.style.opacity === '1') {\n                        actionMenu.style.opacity = '0';\n                        actionMenu.style.transform = 'translateX(100%)';\n                        actionMenu.style.pointerEvents = 'none';\n                    }\n                }\n            },\n            onMouseLeave: (e)=>{\n                // Hide action menu when mouse leaves (all screen sizes)\n                const actionMenu = e.currentTarget.querySelector('.action-menu');\n                if (actionMenu && actionMenu.style.opacity === '1') {\n                    actionMenu.style.opacity = '0';\n                    actionMenu.style.transform = 'translateX(100%)';\n                    actionMenu.style.pointerEvents = 'none';\n                }\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"aspect-video bg-gray-100 rounded-t-lg overflow-hidden\",\n                    children: post.featuredImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: post.featuredImageUrl,\n                        alt: post.title,\n                        className: \"w-full h-full object-cover\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 831,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-12 h-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 838,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 837,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 829,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: classes.content,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"\".concat(classes.title, \" text-gray-900 line-clamp-2\"),\n                            children: post.title\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 846,\n                            columnNumber: 11\n                        }, this),\n                        post.excerpt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"\".concat(classes.excerpt, \" text-gray-600 line-clamp-3\"),\n                            children: post.excerpt\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 852,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: classes.meta,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex \".concat(classes.status, \" font-semibold rounded-full \").concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                            children: post.isPublished ? 'Published' : 'Draft'\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 861,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"\".concat(classes.date, \" text-gray-500\"),\n                                            children: formatDate(post.updatedAt)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 868,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 860,\n                                    columnNumber: 17\n                                }, this),\n                                post.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap \".concat(classes.categories),\n                                    children: post.categories.split(',').slice(0, 2).map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex \".concat(classes.category, \" bg-blue-100 text-blue-800 rounded\"),\n                                            children: category.trim()\n                                        }, index, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 877,\n                                            columnNumber: 23\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 875,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 858,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between \".concat(density === 'compact' ? 'mt-3 pt-2' : density === 'spacious' ? 'mt-5 pt-4' : 'mt-4 pt-3', \" border-t border-gray-200\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: [\n                                        \"Updated \",\n                                        formatDate(post.updatedAt)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 887,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            var _e_currentTarget_closest;\n                                            e.stopPropagation();\n                                            console.log('Three-dot button clicked');\n                                            const actionMenu = (_e_currentTarget_closest = e.currentTarget.closest('.group')) === null || _e_currentTarget_closest === void 0 ? void 0 : _e_currentTarget_closest.querySelector('.action-menu');\n                                            console.log('Action menu found:', actionMenu);\n                                            if (actionMenu) {\n                                                const isVisible = actionMenu.style.opacity === '1';\n                                                console.log('Menu is currently visible:', isVisible);\n                                                actionMenu.style.opacity = isVisible ? '0' : '1';\n                                                actionMenu.style.transform = isVisible ? 'translateX(100%)' : 'translateX(0)';\n                                                actionMenu.style.pointerEvents = isVisible ? 'none' : 'auto';\n                                                console.log('Menu opacity set to:', actionMenu.style.opacity);\n                                            }\n                                        },\n                                        className: \"p-2 text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors\",\n                                        title: \"Show Actions\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-4 w-4\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 912,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 911,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 893,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 892,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 886,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"action-menu absolute bg-white rounded-lg border border-gray-200 flex flex-col items-center justify-center transition-all duration-200 z-10 \".concat(density === 'compact' ? 'top-1 right-1 bottom-1 w-12 space-y-1.5 px-1' : density === 'spacious' ? 'top-3 right-3 bottom-3 w-16 space-y-3 px-2' : 'top-2 right-2 bottom-2 w-14 space-y-2 px-1.5'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAction('edit', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 border border-blue-500 hover:border-blue-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: \"Edit Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 937,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 927,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAction('view', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-indigo-600 hover:bg-indigo-700 border border-indigo-500 hover:border-indigo-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: \"View Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 951,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 941,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAction('toggle-published', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center \".concat(post.isPublished ? 'bg-orange-600 hover:bg-orange-700 border-orange-500 hover:border-orange-600' : 'bg-green-600 hover:bg-green-700 border-green-500 hover:border-green-600', \" border text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \").concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: post.isPublished ? 'Unpublish Blog Post' : 'Publish Blog Post',\n                                    children: post.isPublished ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 970,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 972,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 955,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAction('duplicate', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-purple-600 hover:bg-purple-700 border border-purple-500 hover:border-purple-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: \"Duplicate Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 987,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 977,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAction('archive', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-yellow-600 hover:bg-yellow-700 border border-yellow-500 hover:border-yellow-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: \"Archive Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1001,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 991,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAction('delete', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-red-600 hover:bg-red-700 border border-red-500 hover:border-red-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: \"Delete Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1015,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1005,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 919,\n                            columnNumber: 11\n                        }, this),\n                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 left-2 \".concat(classes.checkbox),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                checked: isSelected,\n                                onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white \".concat(classes.checkbox),\n                                style: {\n                                    backgroundColor: 'white'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1023,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1022,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 844,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 803,\n            columnNumber: 7\n        }, this);\n    };\n    // Grid density styling helper\n    const getGridDensityClasses = ()=>{\n        const baseClasses = {\n            container: {\n                compact: 'p-1',\n                comfortable: 'p-3',\n                spacious: 'p-4'\n            },\n            grid: {\n                compact: 'gap-2',\n                comfortable: 'gap-3',\n                spacious: 'gap-4'\n            },\n            card: {\n                compact: 'p-1',\n                comfortable: 'p-2',\n                spacious: 'p-3'\n            },\n            content: {\n                compact: 'p-1',\n                comfortable: 'p-2',\n                spacious: 'p-3'\n            },\n            title: {\n                compact: 'text-sm font-semibold mb-0.5',\n                comfortable: 'text-lg font-semibold mb-1',\n                spacious: 'text-xl font-semibold mb-2'\n            },\n            excerpt: {\n                compact: 'text-xs mb-1',\n                comfortable: 'text-sm mb-1.5',\n                spacious: 'text-base mb-2'\n            },\n            meta: {\n                compact: 'space-y-0.5 mb-1',\n                comfortable: 'space-y-1 mb-2',\n                spacious: 'space-y-2 mb-3'\n            },\n            status: {\n                compact: 'px-1 py-0.5 text-xs',\n                comfortable: 'px-1.5 py-0.5 text-xs',\n                spacious: 'px-2 py-1 text-sm'\n            },\n            date: {\n                compact: 'text-xs',\n                comfortable: 'text-xs',\n                spacious: 'text-sm'\n            },\n            categories: {\n                compact: 'gap-0.5',\n                comfortable: 'gap-0.5',\n                spacious: 'gap-1'\n            },\n            category: {\n                compact: 'px-1 py-0.5 text-xs',\n                comfortable: 'px-1.5 py-0.5 text-xs',\n                spacious: 'px-2 py-1 text-sm'\n            },\n            actions: {\n                compact: 'pt-1',\n                comfortable: 'pt-1.5',\n                spacious: 'pt-2'\n            },\n            buttons: {\n                compact: 'space-x-0.5',\n                comfortable: 'space-x-1',\n                spacious: 'space-x-2'\n            },\n            button: {\n                compact: 'p-0.5',\n                comfortable: 'p-1',\n                spacious: 'p-1.5'\n            },\n            icon: {\n                compact: 'w-2.5 h-2.5',\n                comfortable: 'w-3 h-3',\n                spacious: 'w-4 h-4'\n            },\n            checkbox: {\n                compact: 'h-2.5 w-2.5',\n                comfortable: 'h-3 w-3',\n                spacious: 'h-4 w-4'\n            }\n        };\n        return {\n            container: baseClasses.container[density],\n            grid: baseClasses.grid[density],\n            card: baseClasses.card[density],\n            content: baseClasses.content[density],\n            title: baseClasses.title[density],\n            excerpt: baseClasses.excerpt[density],\n            meta: baseClasses.meta[density],\n            status: baseClasses.status[density],\n            date: baseClasses.date[density],\n            categories: baseClasses.categories[density],\n            category: baseClasses.category[density],\n            actions: baseClasses.actions[density],\n            buttons: baseClasses.buttons[density],\n            button: baseClasses.button[density],\n            icon: baseClasses.icon[density],\n            checkbox: baseClasses.checkbox[density]\n        };\n    };\n    // Format date\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString();\n    };\n    // Truncate text\n    const truncateText = (text, maxLength)=>{\n        if (text.length <= maxLength) return text;\n        return text.substring(0, maxLength) + '...';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-37cba23e0791254b\" + \" \" + \"h-full flex flex-col space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"37cba23e0791254b\",\n                children: '.action-menu{opacity:0!important;transform:translatex(100%)!important;pointer-events:none!important;transition:all.2s ease-in-out!important}.group:hover .action-menu{opacity:0!important;transform:translatex(100%)!important;pointer-events:none!important}.group:hover .action-menu *{pointer-events:none!important}.action-menu[style*=\"opacity: 1\"]{opacity:1!important;transform:translatex(0)!important;pointer-events:auto!important;display:flex!important}.action-menu[style*=\"opacity: 1\"] *{pointer-events:auto!important}@media(max-width:1024px){.group:hover .action-menu{opacity:0!important;transform:translatex(100%)!important;pointer-events:none!important}.group:hover .action-menu *{pointer-events:none!important}.action-menu{display:flex!important;opacity:0!important;transform:translatex(100%)!important;pointer-events:none!important;transition:all.2s ease-in-out!important}.action-menu[style*=\"opacity: 1\"]{opacity:1!important;transform:translatex(0)!important;pointer-events:auto!important}}.mobile-three-dot{display:block!important;z-index:30!important}.md\\\\\\\\:hidden{display:block!important}@media(min-width:768px){.md\\\\\\\\:hidden{display:none!important}}.density-compact th,.density-compact td{padding-top:4px!important;padding-bottom:4px!important}.density-comfortable th,.density-comfortable td{padding-top:16px!important;padding-bottom:16px!important}.density-spacious th,.density-spacious td{padding-top:32px!important;padding-bottom:32px!important}'\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-37cba23e0791254b\" + \" \" + \"relative rounded-lg shadow-sm border border-gray-200/50 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-37cba23e0791254b\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-indigo-50/20\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 1237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-37cba23e0791254b\" + \" \" + \"relative p-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-14 w-14 text-lime-600 -mt-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1242,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-37cba23e0791254b\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"text-3xl font-bold text-gray-900 mt-2\",\n                                                    children: \"Blog Management\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1244,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"text-sm font-medium text-gray-600\",\n                                                    children: \"Create, edit, and manage your blog content.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1247,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1243,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"hidden lg:flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAddClick,\n                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors shadow-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1258,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Add Blog Post\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1254,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1253,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1240,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 1239,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 1236,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                role: \"main\",\n                \"aria-label\": \"Blog management section\",\n                className: \"jsx-37cba23e0791254b\" + \" \" + \"flex-1 rounded-lg shadow-sm border border-gray-200/50 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        paddingBottom: '0'\n                    },\n                    className: \"jsx-37cba23e0791254b\" + \" \" + \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-37cba23e0791254b\" + \" \" + \"space-y-3 p-2 bg-white rounded-lg border border-gray-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"flex flex-col space-y-3 lg:hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1281,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"Search blog posts...\",\n                                                        value: searchQuery,\n                                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"pl-10 pr-4 py-2.5 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1282,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1280,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1279,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-0.5 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleViewModeChange('list'),\n                                                            title: \"List view\",\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 \".concat(viewMode === 'list' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1305,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"text-xs font-medium hidden xs:inline\",\n                                                                    children: \"List\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1306,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1296,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleViewModeChange('grid'),\n                                                            title: \"Grid view\",\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 \".concat(viewMode === 'grid' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1317,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"text-xs font-medium hidden xs:inline\",\n                                                                    children: \"Grid\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1318,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1308,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1295,\n                                                    columnNumber: 15\n                                                }, this),\n                                                viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-0.5 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"text-xs font-medium text-gray-700 px-1\",\n                                                            children: \"Col:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1325,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center gap-0.5 flex-1\",\n                                                            children: [\n                                                                1,\n                                                                2,\n                                                                3,\n                                                                4\n                                                            ].map((num)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(num),\n                                                                    title: \"\".concat(num, \" column\").concat(num > 1 ? 's' : ''),\n                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"flex-1 px-1.5 py-1 rounded text-xs font-medium \".concat(gridColumns === num ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: num\n                                                                }, num, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1328,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1326,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1324,\n                                                    columnNumber: 17\n                                                }, this),\n                                                viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowColumnSelector(!showColumnSelector),\n                                                        title: \"Columns\",\n                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1353,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"hidden xs:inline\",\n                                                                children: \"Col\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1354,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-3 w-3 ml-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1355,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1348,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1347,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowFilters(!showFilters),\n                                                        title: \"Filters\",\n                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium rounded-lg border \".concat(showFilters || Object.keys(filters).some((key)=>filters[key]) ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1371,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"hidden xs:inline\",\n                                                                children: \"Filter\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1372,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            Object.keys(filters).some((key)=>filters[key]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"ml-0.5 inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                children: Object.values(filters).filter(Boolean).length\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1374,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1362,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1361,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowWindowList(!showWindowList),\n                                                        title: \"Density\",\n                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1388,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"hidden xs:inline\",\n                                                                children: density.charAt(0).toUpperCase() + density.slice(1)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1389,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-3 w-3 ml-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1390,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1383,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1382,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1293,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1277,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"hidden lg:flex items-center justify-between gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center gap-3 flex-1 max-w-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"relative flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1402,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"Search blog posts by title, content, excerpt...\",\n                                                            value: searchQuery,\n                                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1403,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1401,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowFilters(!showFilters),\n                                                            title: \"Show/hide filters\",\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(showFilters || Object.keys(filters).some((key)=>filters[key]) ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1423,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Filters\",\n                                                                Object.keys(filters).some((key)=>filters[key]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                    children: Object.values(filters).filter(Boolean).length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1426,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1430,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1414,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"absolute top-full right-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center justify-between mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                                                children: \"Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1438,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>setShowFilters(false),\n                                                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"text-gray-400 hover:text-gray-600\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1443,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1439,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1437,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"space-y-4\",\n                                                                        children: (_config_filters = config.filters) === null || _config_filters === void 0 ? void 0 : _config_filters.map((filter)=>{\n                                                                            var _filter_options;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-37cba23e0791254b\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"block text-xs font-medium text-gray-700 mb-2\",\n                                                                                        children: filter.label\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1450,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                        value: filters[filter.key] || '',\n                                                                                        onChange: (e)=>{\n                                                                                            const newFilters = {\n                                                                                                ...filters\n                                                                                            };\n                                                                                            if (e.target.value) {\n                                                                                                newFilters[filter.key] = e.target.value;\n                                                                                            } else {\n                                                                                                delete newFilters[filter.key];\n                                                                                            }\n                                                                                            setFilters(newFilters);\n                                                                                        },\n                                                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                                                        children: (_filter_options = filter.options) === null || _filter_options === void 0 ? void 0 : _filter_options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: option.value,\n                                                                                                className: \"jsx-37cba23e0791254b\",\n                                                                                                children: option.label\n                                                                                            }, option.value, false, {\n                                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                                lineNumber: 1467,\n                                                                                                columnNumber: 37\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1453,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, filter.key, true, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1449,\n                                                                                columnNumber: 31\n                                                                            }, this);\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1447,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"flex justify-end mt-4 pt-4 border-t border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                setFilters({});\n                                                                                setShowFilters(false);\n                                                                            },\n                                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg\",\n                                                                            children: \"Clear All\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1477,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1476,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1436,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1435,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1413,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1400,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"text-sm font-medium text-gray-700\",\n                                                            children: \"View:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1497,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-1 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleViewModeChange('list'),\n                                                                    title: \"List view\",\n                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"px-3 py-2 rounded-md flex items-center gap-2 \".concat(viewMode === 'list' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-5 w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1508,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"text-sm font-medium\",\n                                                                            children: \"List\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1509,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1499,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleViewModeChange('grid'),\n                                                                    title: \"Grid view\",\n                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"px-3 py-2 rounded-md flex items-center gap-2 \".concat(viewMode === 'grid' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-5 w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1520,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"text-sm font-medium\",\n                                                                            children: \"Grid\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1521,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1511,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1498,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1496,\n                                                    columnNumber: 19\n                                                }, this),\n                                                viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"text-sm font-medium text-gray-700\",\n                                                            children: \"Columns:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1529,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-1 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(1),\n                                                                    title: \"1 column\",\n                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 1 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1531,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(2),\n                                                                    title: \"2 columns\",\n                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 2 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1542,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(3),\n                                                                    title: \"3 columns\",\n                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 3 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1553,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(4),\n                                                                    title: \"4 columns\",\n                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 4 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1564,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1530,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1528,\n                                                    columnNumber: 21\n                                                }, this),\n                                                viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowColumnSelector(!showColumnSelector),\n                                                            title: \"Select columns to display\",\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1587,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Columns\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1589,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1582,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        showColumnSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"text-xs font-medium text-gray-500 uppercase tracking-wide mb-2\",\n                                                                        children: \"Show Columns\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1596,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    config.columns.map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center space-x-2 py-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: visibleColumns.includes(column.key),\n                                                                                    onChange: (e)=>{\n                                                                                        if (e.target.checked) {\n                                                                                            setVisibleColumns((prev)=>[\n                                                                                                    ...prev,\n                                                                                                    column.key\n                                                                                                ]);\n                                                                                        } else {\n                                                                                            setVisibleColumns((prev)=>prev.filter((col)=>col !== column.key));\n                                                                                        }\n                                                                                    },\n                                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1599,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"text-sm text-gray-700\",\n                                                                                    children: column.label\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1611,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, column.key, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1598,\n                                                                            columnNumber: 31\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1595,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1594,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1581,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowWindowList(!showWindowList),\n                                                            title: \"Select density\",\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1629,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                density.charAt(0).toUpperCase() + density.slice(1),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1631,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1624,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showWindowList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"absolute top-full left-0 mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"p-1\",\n                                                                children: [\n                                                                    'compact',\n                                                                    'comfortable',\n                                                                    'spacious'\n                                                                ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            handleDensityChange(option);\n                                                                            setShowWindowList(false);\n                                                                        },\n                                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 \".concat(density === option ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'),\n                                                                        children: option.charAt(0).toUpperCase() + option.slice(1)\n                                                                    }, option, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1639,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1637,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1636,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1623,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1494,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1398,\n                                    columnNumber: 15\n                                }, this),\n                                showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                    children: \"Filters\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1665,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowFilters(false),\n                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"text-gray-400 hover:text-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1670,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1666,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1664,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"space-y-4\",\n                                            children: (_config_filters1 = config.filters) === null || _config_filters1 === void 0 ? void 0 : _config_filters1.map((filter)=>{\n                                                var _filter_options;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-37cba23e0791254b\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"block text-xs font-medium text-gray-700 mb-2\",\n                                                            children: filter.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1677,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: filters[filter.key] || '',\n                                                            onChange: (e)=>{\n                                                                const newFilters = {\n                                                                    ...filters\n                                                                };\n                                                                if (e.target.value) {\n                                                                    newFilters[filter.key] = e.target.value;\n                                                                } else {\n                                                                    delete newFilters[filter.key];\n                                                                }\n                                                                setFilters(newFilters);\n                                                            },\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                            children: (_filter_options = filter.options) === null || _filter_options === void 0 ? void 0 : _filter_options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: option.value,\n                                                                    className: \"jsx-37cba23e0791254b\",\n                                                                    children: option.label\n                                                                }, option.value, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1694,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1680,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, filter.key, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1676,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1674,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"flex justify-end mt-4 pt-4 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setFilters({});\n                                                    setShowFilters(false);\n                                                },\n                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg\",\n                                                children: \"Clear All\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1704,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1703,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1663,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1275,\n                            columnNumber: 11\n                        }, this),\n                        config.enableBulkActions && selectedPosts.length > 0 && (viewMode === 'list' || viewMode === 'grid') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-37cba23e0791254b\" + \" \" + \"bg-blue-50 border border-blue-200 rounded-lg px-4 py-2 shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"text-blue-600 font-semibold text-xs\",\n                                                            children: selectedPosts.length\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1725,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1724,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"text-xs font-medium text-blue-900\",\n                                                        children: [\n                                                            \"blog post\",\n                                                            selectedPosts.length === 1 ? '' : 's',\n                                                            \" selected\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1729,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1723,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center space-x-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleBulkAction('publish'),\n                                                        title: \"Publish selected blog posts\",\n                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 border border-green-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1741,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Publish\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1736,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleBulkAction('unpublish'),\n                                                        title: \"Unpublish selected blog posts\",\n                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1750,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Unpublish\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1745,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>showBulkDeleteConfirmation(),\n                                                        title: \"Delete selected blog posts\",\n                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 border border-red-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1759,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Delete\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1754,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1735,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1722,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center space-x-1.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedPosts([]),\n                                            title: \"Clear selection\",\n                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gray-500 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1772,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Clear\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1767,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1766,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1721,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1720,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-37cba23e0791254b\" + \" \" + \"px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-37cba23e0791254b\" + \" \" + \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-5 w-5 text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1786,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1785,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"text-sm font-medium text-red-800\",\n                                                    children: \"Error\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1789,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"mt-2 text-sm text-red-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-37cba23e0791254b\",\n                                                        children: error\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1791,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1790,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setError(null);\n                                                            fetchBlogPosts();\n                                                        },\n                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200\",\n                                                        children: \"Try Again\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1794,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1793,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1788,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1784,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1783,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1782,\n                            columnNumber: 15\n                        }, this),\n                        loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-37cba23e0791254b\" + \" \" + \"px-6 py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1814,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"ml-3 text-gray-600\",\n                                        children: \"Loading blog posts...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1815,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1813,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1812,\n                            columnNumber: 15\n                        }, this),\n                        !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-37cba23e0791254b\",\n                            children: blogPosts.length === 0 ? /* Empty State */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-37cba23e0791254b\" + \" \" + \"p-12 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"mx-auto h-12 w-12 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1826,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"mt-2 text-sm font-medium text-gray-900\",\n                                        children: \"No blog posts found\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1827,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"mt-1 text-sm text-gray-500\",\n                                        children: debouncedSearchQuery ? 'Try adjusting your search terms.' : 'Get started by creating your first blog post.'\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1828,\n                                        columnNumber: 21\n                                    }, this),\n                                    ((_config_permissions = config.permissions) === null || _config_permissions === void 0 ? void 0 : _config_permissions.create) && !debouncedSearchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleAddClick,\n                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: \"Add Blog Post\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1833,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1832,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1825,\n                                columnNumber: 19\n                            }, this) : viewMode === 'list' ? /* Table View */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-37cba23e0791254b\" + \" \" + \"overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"min-w-full divide-y divide-gray-200 density-\".concat(density),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"bg-gray-200 border-b border-gray-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"jsx-37cba23e0791254b\",\n                                                    children: [\n                                                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            scope: \"col\",\n                                                            style: {\n                                                                width: '6px'\n                                                            },\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"relative pl-2 py-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: selectedPosts.length === blogPosts.length && blogPosts.length > 0,\n                                                                onChange: (e)=>handleSelectAll(e.target.checked),\n                                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"text-blue-600 focus:ring-blue-500 border-gray-300 rounded \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-5 w-5' : 'h-4 w-4')\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1852,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1851,\n                                                            columnNumber: 31\n                                                        }, this),\n                                                        getVisibleFields().map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                scope: \"col\",\n                                                                onClick: ()=>handleSort(field.key),\n                                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"px-6 py-2 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 text-xs\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-37cba23e0791254b\",\n                                                                            children: field.label\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1872,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        sortBy === field.key ? sortOrder === 'asc' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-black\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1875,\n                                                                            columnNumber: 39\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-black\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1877,\n                                                                            columnNumber: 39\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1880,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1871,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, field.key, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1865,\n                                                                columnNumber: 31\n                                                            }, this)),\n                                                        config.actions && config.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            scope: \"col\",\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"px-6 py-2 text-right font-medium text-gray-900 uppercase tracking-wider text-xs min-w-[120px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-37cba23e0791254b\",\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1889,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1888,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1848,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1847,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"bg-white divide-y divide-gray-200\",\n                                                children: blogPosts.map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"hover:bg-gray-50 \".concat(selectedPosts.includes(post.id) ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''),\n                                                        children: [\n                                                            config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"px-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: selectedPosts.includes(post.id),\n                                                                    onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                                                    style: {\n                                                                        backgroundColor: 'white'\n                                                                    },\n                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1902,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1901,\n                                                                columnNumber: 33\n                                                            }, this),\n                                                            getVisibleFields().map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"px-6 whitespace-nowrap\",\n                                                                    children: field.key === 'title' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BlogAvatar, {\n                                                                                title: post.title,\n                                                                                featuredImageUrl: post.featuredImageUrl,\n                                                                                size: \"sm\",\n                                                                                className: \"mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1917,\n                                                                                columnNumber: 39\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-37cba23e0791254b\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                                                        children: post.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1924,\n                                                                                        columnNumber: 41\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"text-sm text-gray-500\",\n                                                                                        children: post.slug\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1925,\n                                                                                        columnNumber: 41\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1923,\n                                                                                columnNumber: 39\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1916,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'isPublished' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                        children: post.isPublished ? 'Published' : 'Draft'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1929,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'excerpt' || field.key === 'content' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        title: post[field.key],\n                                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"text-sm text-gray-900 max-w-xs truncate\",\n                                                                        children: truncateText(post[field.key] || '', 50)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1937,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'tags' || field.key === 'categories' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"text-sm text-gray-900\",\n                                                                        children: post[field.key] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"flex flex-wrap gap-1\",\n                                                                            children: [\n                                                                                post[field.key].split(',').slice(0, 2).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\",\n                                                                                        children: tag.trim()\n                                                                                    }, index, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1945,\n                                                                                        columnNumber: 45\n                                                                                    }, this)),\n                                                                                post[field.key].split(',').length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"text-xs text-gray-500\",\n                                                                                    children: [\n                                                                                        \"+\",\n                                                                                        post[field.key].split(',').length - 2,\n                                                                                        \" more\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1950,\n                                                                                    columnNumber: 45\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1943,\n                                                                            columnNumber: 41\n                                                                        }, this) : '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1941,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'createdAt' || field.key === 'updatedAt' || field.key === 'publishedAt' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"text-sm text-gray-500\",\n                                                                        children: post[field.key] ? formatDate(post[field.key]) : '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1956,\n                                                                        columnNumber: 37\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"text-sm text-gray-900\",\n                                                                        children: post[field.key] || '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1960,\n                                                                        columnNumber: 37\n                                                                    }, this)\n                                                                }, field.key, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1914,\n                                                                    columnNumber: 33\n                                                                }, this)),\n                                                            config.actions && config.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"px-6 whitespace-nowrap text-right text-sm font-medium\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center space-x-2\",\n                                                                    children: config.actions.map((action)=>{\n                                                                        const isLoading = actionLoading === \"\".concat(action.action, \"-\").concat(post.id);\n                                                                        const IconComponent = action.icon === 'EyeIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"] : action.icon === 'PencilIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"] : action.icon === 'PowerIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"] : action.icon === 'TrashIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"] : _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleAction(action.action, post),\n                                                                            disabled: isLoading,\n                                                                            title: action.tooltip,\n                                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"p-1 rounded-md transition-colors \".concat(action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' : action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' : action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' : action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' : 'text-gray-600 hover:bg-gray-50', \" \").concat(isLoading ? 'opacity-50 cursor-not-allowed' : ''),\n                                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"animate-spin rounded-full h-4 w-4 border-b-2 border-current\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1993,\n                                                                                columnNumber: 45\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1995,\n                                                                                columnNumber: 45\n                                                                            }, this)\n                                                                        }, action.action, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1979,\n                                                                            columnNumber: 41\n                                                                        }, this);\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1970,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1969,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        ]\n                                                    }, post.id, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1896,\n                                                        columnNumber: 29\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1894,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1846,\n                                        columnNumber: 23\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1845,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1844,\n                                columnNumber: 19\n                            }, this) : viewMode === 'grid' ? /* Grid View - Restructured */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-37cba23e0791254b\" + \" \" + (getGridDensityClasses().container || \"\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"grid \".concat(getGridDensityClasses().grid, \" \").concat(gridColumns === 1 ? 'grid-cols-1' : gridColumns === 2 ? 'grid-cols-1 md:grid-cols-2' : gridColumns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'),\n                                    children: blogPosts.map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridCard, {\n                                            post: post\n                                        }, post.id, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 2019,\n                                            columnNumber: 25\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 2012,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 2011,\n                                columnNumber: 19\n                            }, this) : /* Card View */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-37cba23e0791254b\" + \" \" + \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"space-y-4\",\n                                    children: blogPosts.map((post)=>{\n                                        var _config_actions;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow \".concat(selectedPosts.includes(post.id) ? 'ring-2 ring-blue-500 bg-blue-50' : ''),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"w-48 h-32 bg-gray-100 rounded-l-lg overflow-hidden flex-shrink-0\",\n                                                        children: post.featuredImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: post.featuredImageUrl,\n                                                            alt: post.title,\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"w-full h-full object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 2035,\n                                                            columnNumber: 33\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"w-full h-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-8 h-8 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 2042,\n                                                                columnNumber: 35\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 2041,\n                                                            columnNumber: 33\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 2033,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-37cba23e0791254b\" + \" \" + \"flex-1 p-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-start justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"text-lg font-semibold text-gray-900 mb-1\",\n                                                                            children: post.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2052,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"text-sm text-gray-500 mb-2\",\n                                                                            children: post.slug\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2055,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        post.excerpt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"text-sm text-gray-600 mb-3 line-clamp-2\",\n                                                                            children: post.excerpt\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2061,\n                                                                            columnNumber: 37\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center space-x-4 text-sm text-gray-500\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                                    children: post.isPublished ? 'Published' : 'Draft'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2068,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-37cba23e0791254b\",\n                                                                                    children: [\n                                                                                        \"Updated: \",\n                                                                                        formatDate(post.updatedAt)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2075,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                post.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-37cba23e0791254b\",\n                                                                                    children: [\n                                                                                        \"Category: \",\n                                                                                        post.categories.split(',')[0].trim()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2077,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2067,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 2050,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"flex items-center space-x-2 ml-4\",\n                                                                    children: [\n                                                                        (_config_actions = config.actions) === null || _config_actions === void 0 ? void 0 : _config_actions.map((action)=>{\n                                                                            const isLoading = actionLoading === \"\".concat(action.action, \"-\").concat(post.id);\n                                                                            const IconComponent = action.icon === 'EyeIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"] : action.icon === 'PencilIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"] : action.icon === 'PowerIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"] : action.icon === 'TrashIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"] : _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleAction(action.action, post),\n                                                                                disabled: isLoading,\n                                                                                title: action.tooltip,\n                                                                                className: \"jsx-37cba23e0791254b\" + \" \" + \"p-2 rounded-md transition-colors \".concat(action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' : action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' : action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' : action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' : 'text-gray-600 hover:bg-gray-50', \" \").concat(isLoading ? 'opacity-50 cursor-not-allowed' : ''),\n                                                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"animate-spin rounded-full h-4 w-4 border-b-2 border-current\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2106,\n                                                                                    columnNumber: 43\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                    className: \"jsx-37cba23e0791254b\" + \" \" + \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2108,\n                                                                                    columnNumber: 43\n                                                                                }, this)\n                                                                            }, action.action, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 2092,\n                                                                                columnNumber: 39\n                                                                            }, this);\n                                                                        }),\n                                                                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: selectedPosts.includes(post.id),\n                                                                            onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                                                            style: {\n                                                                                backgroundColor: 'white'\n                                                                            },\n                                                                            className: \"jsx-37cba23e0791254b\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2116,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 2083,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 2049,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 2048,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 2031,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, post.id, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 2028,\n                                            columnNumber: 25\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 2026,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 2025,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1822,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 1273,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 1268,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_modal__WEBPACK_IMPORTED_MODULE_3__.BlogModal, {\n                isOpen: isCreateModalOpen,\n                onClose: ()=>setIsCreateModalOpen(false),\n                onSubmit: handleCreate,\n                title: \"Create New Blog Post\"\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 2139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_modal__WEBPACK_IMPORTED_MODULE_3__.BlogModal, {\n                isOpen: isEditModalOpen,\n                onClose: ()=>{\n                    setIsEditModalOpen(false);\n                    setEditingPost(null);\n                },\n                onSubmit: async (formData)=>{\n                    if (editingPost) {\n                        await handleUpdate(editingPost.id, formData);\n                    }\n                },\n                title: \"Edit Blog Post\",\n                initialData: editingPost !== null && editingPost !== void 0 ? editingPost : undefined\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 2146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_confirmation_modal__WEBPACK_IMPORTED_MODULE_5__.ConfirmationModal, {\n                isOpen: deleteConfirmation.isOpen,\n                title: \"Delete Confirmation\",\n                message: \"Are you sure you want to delete this blog post?\",\n                details: (()=>{\n                    if (deleteConfirmation.isBulkDelete) {\n                        var _deleteConfirmation_bulkPosts;\n                        const count = ((_deleteConfirmation_bulkPosts = deleteConfirmation.bulkPosts) === null || _deleteConfirmation_bulkPosts === void 0 ? void 0 : _deleteConfirmation_bulkPosts.length) || 0;\n                        return \"This action will permanently delete \".concat(count, \" blog post\").concat(count === 1 ? '' : 's', \". This cannot be undone.\");\n                    }\n                    const post = deleteConfirmation.post;\n                    return 'This action will permanently delete \"'.concat((post === null || post === void 0 ? void 0 : post.title) || 'this blog post', '\". This cannot be undone.');\n                })(),\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                onConfirm: confirmDelete,\n                onCancel: cancelDelete,\n                type: \"danger\",\n                showVerification: true,\n                verificationData: {\n                    canDelete: true,\n                    reason: deleteConfirmation.isBulkDelete ? \"\".concat(((_deleteConfirmation_bulkPosts = deleteConfirmation.bulkPosts) === null || _deleteConfirmation_bulkPosts === void 0 ? void 0 : _deleteConfirmation_bulkPosts.length) || 0, \" blog post\").concat((((_deleteConfirmation_bulkPosts1 = deleteConfirmation.bulkPosts) === null || _deleteConfirmation_bulkPosts1 === void 0 ? void 0 : _deleteConfirmation_bulkPosts1.length) || 0) === 1 ? '' : 's', \" selected for deletion\") : 'Blog post \"'.concat(((_deleteConfirmation_post = deleteConfirmation.post) === null || _deleteConfirmation_post === void 0 ? void 0 : _deleteConfirmation_post.title) || 'Unknown', '\" ready for deletion')\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 2162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n        lineNumber: 1154,\n        columnNumber: 5\n    }, this);\n}\n_s1(BlogsManagement, \"oAck7AUskU6uZdwdlzA1u342lWQ=\", false, function() {\n    return [\n        _components_providers_notification_provider__WEBPACK_IMPORTED_MODULE_4__.useNotifications\n    ];\n});\n_c1 = BlogsManagement;\nvar _c, _c1;\n$RefreshReg$(_c, \"BlogAvatar\");\n$RefreshReg$(_c1, \"BlogsManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/blog/blogs-management.tsx\n"));

/***/ })

});